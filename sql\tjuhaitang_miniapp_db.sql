/*
 Navicat Premium Dump SQL

 Source Server         : **************
 Source Server Type    : MySQL
 Source Server Version : 80024 (8.0.24)
 Source Host           : **************:3306
 Source Schema         : tjuhaitang_miniapp_db

 Target Server Type    : MySQL
 Target Server Version : 80024 (8.0.24)
 File Encoding         : 65001

 Date: 30/07/2025 14:31:20
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for gen_table
-- ----------------------------
DROP TABLE IF EXISTS `gen_table`;
CREATE TABLE `gen_table`  (
  `table_id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
  `table_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '表名称',
  `table_comment` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '表描述',
  `sub_table_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '关联子表的表名',
  `sub_table_fk_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '子表关联的外键名',
  `class_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '实体类名称',
  `tpl_category` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT 'crud' COMMENT '使用的模板（crud单表操作 tree树表操作）',
  `tpl_web_type` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '前端模板类型（element-ui模版 element-plus模版）',
  `package_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '生成包路径',
  `module_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '生成模块名',
  `business_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '生成业务名',
  `function_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '生成功能名',
  `function_author` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '生成功能作者',
  `gen_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '0' COMMENT '生成代码方式（0zip压缩包 1自定义路径）',
  `gen_path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '/' COMMENT '生成路径（不填默认项目路径）',
  `options` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '其它生成选项',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`table_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci COMMENT = '代码生成业务表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of gen_table
-- ----------------------------
INSERT INTO `gen_table` VALUES (1, 'mini_activity', '精彩活动表', NULL, NULL, 'MiniActivity', 'crud', '', 'com.ruoyi.system', 'system', 'activity', '精彩活动', 'ruoyi', '0', '/', NULL, 'admin', '2025-07-04 18:16:34', '', NULL, NULL);
INSERT INTO `gen_table` VALUES (2, 'sys_oper_log', '操作日志记录', NULL, NULL, 'SysOperLog', 'crud', '', 'com.ruoyi.system', 'system', 'log', '操作日志记录', 'ruoyi', '0', '/', NULL, 'admin', '2025-07-04 18:16:34', '', NULL, NULL);

-- ----------------------------
-- Table structure for gen_table_column
-- ----------------------------
DROP TABLE IF EXISTS `gen_table_column`;
CREATE TABLE `gen_table_column`  (
  `column_id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
  `table_id` bigint NULL DEFAULT NULL COMMENT '归属表编号',
  `column_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '列名称',
  `column_comment` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '列描述',
  `column_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '列类型',
  `java_type` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT 'JAVA类型',
  `java_field` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT 'JAVA字段名',
  `is_pk` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '是否主键（1是）',
  `is_increment` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '是否自增（1是）',
  `is_required` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '是否必填（1是）',
  `is_insert` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '是否为插入字段（1是）',
  `is_edit` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '是否编辑字段（1是）',
  `is_list` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '是否列表字段（1是）',
  `is_query` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '是否查询字段（1是）',
  `query_type` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT 'EQ' COMMENT '查询方式（等于、不等于、大于、小于、范围）',
  `html_type` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '显示类型（文本框、文本域、下拉框、复选框、单选框、日期控件）',
  `dict_type` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '字典类型',
  `sort` int NULL DEFAULT NULL COMMENT '排序',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`column_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 30 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci COMMENT = '代码生成业务表字段' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of gen_table_column
-- ----------------------------
INSERT INTO `gen_table_column` VALUES (1, 1, 'activity_id', '活动ID', 'bigint', 'Long', 'activityId', '1', '1', '0', '1', NULL, NULL, NULL, 'EQ', 'input', '', 1, 'admin', '2025-07-04 18:16:34', '', NULL);
INSERT INTO `gen_table_column` VALUES (2, 1, 'title', '活动标题', 'varchar(200)', 'String', 'title', '0', '0', '1', '1', '1', '1', '1', 'EQ', 'input', '', 2, 'admin', '2025-07-04 18:16:34', '', NULL);
INSERT INTO `gen_table_column` VALUES (3, 1, 'description', '活动描述', 'text', 'String', 'description', '0', '0', '0', '1', '1', '1', '1', 'EQ', 'textarea', '', 3, 'admin', '2025-07-04 18:16:34', '', NULL);
INSERT INTO `gen_table_column` VALUES (4, 1, 'cover_image', '封面图片', 'varchar(500)', 'String', 'coverImage', '0', '0', '1', '1', '1', '1', '1', 'EQ', 'imageUpload', '', 4, 'admin', '2025-07-04 18:16:34', '', NULL);
INSERT INTO `gen_table_column` VALUES (5, 1, 'article_url', '公众号文章链接', 'varchar(500)', 'String', 'articleUrl', '0', '0', '1', '1', '1', '1', '1', 'EQ', 'textarea', '', 5, 'admin', '2025-07-04 18:16:34', '', NULL);
INSERT INTO `gen_table_column` VALUES (6, 1, 'sort_order', '排序（数字越小越靠前）', 'int', 'Long', 'sortOrder', '0', '0', '0', '1', '1', '1', '1', 'EQ', 'input', '', 6, 'admin', '2025-07-04 18:16:34', '', NULL);
INSERT INTO `gen_table_column` VALUES (7, 1, 'status', '状态（0正常 1停用）', 'char(1)', 'String', 'status', '0', '0', '0', '1', '1', '1', '1', 'EQ', 'radio', '', 7, 'admin', '2025-07-04 18:16:34', '', NULL);
INSERT INTO `gen_table_column` VALUES (8, 1, 'create_by', '创建者', 'varchar(64)', 'String', 'createBy', '0', '0', '0', '1', NULL, NULL, NULL, 'EQ', 'input', '', 8, 'admin', '2025-07-04 18:16:34', '', NULL);
INSERT INTO `gen_table_column` VALUES (9, 1, 'create_time', '创建时间', 'datetime', 'Date', 'createTime', '0', '0', '0', '1', NULL, NULL, NULL, 'EQ', 'datetime', '', 9, 'admin', '2025-07-04 18:16:34', '', NULL);
INSERT INTO `gen_table_column` VALUES (10, 1, 'update_by', '更新者', 'varchar(64)', 'String', 'updateBy', '0', '0', '0', '1', '1', NULL, NULL, 'EQ', 'input', '', 10, 'admin', '2025-07-04 18:16:34', '', NULL);
INSERT INTO `gen_table_column` VALUES (11, 1, 'update_time', '更新时间', 'datetime', 'Date', 'updateTime', '0', '0', '0', '1', '1', NULL, NULL, 'EQ', 'datetime', '', 11, 'admin', '2025-07-04 18:16:34', '', NULL);
INSERT INTO `gen_table_column` VALUES (12, 1, 'remark', '备注', 'varchar(500)', 'String', 'remark', '0', '0', '0', '1', '1', '1', NULL, 'EQ', 'textarea', '', 12, 'admin', '2025-07-04 18:16:34', '', NULL);
INSERT INTO `gen_table_column` VALUES (13, 2, 'oper_id', '日志主键', 'bigint', 'Long', 'operId', '1', '1', '0', '1', NULL, NULL, NULL, 'EQ', 'input', '', 1, 'admin', '2025-07-04 18:16:34', '', NULL);
INSERT INTO `gen_table_column` VALUES (14, 2, 'title', '模块标题', 'varchar(50)', 'String', 'title', '0', '0', '0', '1', '1', '1', '1', 'EQ', 'input', '', 2, 'admin', '2025-07-04 18:16:34', '', NULL);
INSERT INTO `gen_table_column` VALUES (15, 2, 'business_type', '业务类型（0其它 1新增 2修改 3删除）', 'int', 'Long', 'businessType', '0', '0', '0', '1', '1', '1', '1', 'EQ', 'select', '', 3, 'admin', '2025-07-04 18:16:34', '', NULL);
INSERT INTO `gen_table_column` VALUES (16, 2, 'method', '方法名称', 'varchar(200)', 'String', 'method', '0', '0', '0', '1', '1', '1', '1', 'EQ', 'input', '', 4, 'admin', '2025-07-04 18:16:34', '', NULL);
INSERT INTO `gen_table_column` VALUES (17, 2, 'request_method', '请求方式', 'varchar(10)', 'String', 'requestMethod', '0', '0', '0', '1', '1', '1', '1', 'EQ', 'input', '', 5, 'admin', '2025-07-04 18:16:34', '', NULL);
INSERT INTO `gen_table_column` VALUES (18, 2, 'operator_type', '操作类别（0其它 1后台用户 2手机端用户）', 'int', 'Long', 'operatorType', '0', '0', '0', '1', '1', '1', '1', 'EQ', 'select', '', 6, 'admin', '2025-07-04 18:16:34', '', NULL);
INSERT INTO `gen_table_column` VALUES (19, 2, 'oper_name', '操作人员', 'varchar(50)', 'String', 'operName', '0', '0', '0', '1', '1', '1', '1', 'LIKE', 'input', '', 7, 'admin', '2025-07-04 18:16:34', '', NULL);
INSERT INTO `gen_table_column` VALUES (20, 2, 'dept_name', '部门名称', 'varchar(50)', 'String', 'deptName', '0', '0', '0', '1', '1', '1', '1', 'LIKE', 'input', '', 8, 'admin', '2025-07-04 18:16:34', '', NULL);
INSERT INTO `gen_table_column` VALUES (21, 2, 'oper_url', '请求URL', 'varchar(255)', 'String', 'operUrl', '0', '0', '0', '1', '1', '1', '1', 'EQ', 'input', '', 9, 'admin', '2025-07-04 18:16:34', '', NULL);
INSERT INTO `gen_table_column` VALUES (22, 2, 'oper_ip', '主机地址', 'varchar(128)', 'String', 'operIp', '0', '0', '0', '1', '1', '1', '1', 'EQ', 'input', '', 10, 'admin', '2025-07-04 18:16:34', '', NULL);
INSERT INTO `gen_table_column` VALUES (23, 2, 'oper_location', '操作地点', 'varchar(255)', 'String', 'operLocation', '0', '0', '0', '1', '1', '1', '1', 'EQ', 'input', '', 11, 'admin', '2025-07-04 18:16:34', '', NULL);
INSERT INTO `gen_table_column` VALUES (24, 2, 'oper_param', '请求参数', 'varchar(2000)', 'String', 'operParam', '0', '0', '0', '1', '1', '1', '1', 'EQ', 'textarea', '', 12, 'admin', '2025-07-04 18:16:34', '', NULL);
INSERT INTO `gen_table_column` VALUES (25, 2, 'json_result', '返回参数', 'varchar(2000)', 'String', 'jsonResult', '0', '0', '0', '1', '1', '1', '1', 'EQ', 'textarea', '', 13, 'admin', '2025-07-04 18:16:34', '', NULL);
INSERT INTO `gen_table_column` VALUES (26, 2, 'status', '操作状态（0正常 1异常）', 'int', 'Long', 'status', '0', '0', '0', '1', '1', '1', '1', 'EQ', 'radio', '', 14, 'admin', '2025-07-04 18:16:34', '', NULL);
INSERT INTO `gen_table_column` VALUES (27, 2, 'error_msg', '错误消息', 'varchar(2000)', 'String', 'errorMsg', '0', '0', '0', '1', '1', '1', '1', 'EQ', 'textarea', '', 15, 'admin', '2025-07-04 18:16:34', '', NULL);
INSERT INTO `gen_table_column` VALUES (28, 2, 'oper_time', '操作时间', 'datetime', 'Date', 'operTime', '0', '0', '0', '1', '1', '1', '1', 'EQ', 'datetime', '', 16, 'admin', '2025-07-04 18:16:34', '', NULL);
INSERT INTO `gen_table_column` VALUES (29, 2, 'cost_time', '消耗时间', 'bigint', 'Long', 'costTime', '0', '0', '0', '1', '1', '1', '1', 'EQ', 'input', '', 17, 'admin', '2025-07-04 18:16:34', '', NULL);

-- ----------------------------
-- Table structure for mini_activity
-- ----------------------------
DROP TABLE IF EXISTS `mini_activity`;
CREATE TABLE `mini_activity`  (
  `activity_id` bigint NOT NULL AUTO_INCREMENT COMMENT '活动ID',
  `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '活动标题',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL COMMENT '活动描述',
  `cover_image` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '封面图片',
  `article_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '公众号文章链接',
  `sort_order` int NULL DEFAULT 0 COMMENT '排序（数字越小越靠前）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `wechat_article_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '微信文章ID',
  `wechat_source` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '公众号来源',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`activity_id`) USING BTREE,
  INDEX `idx_sort_order`(`sort_order` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_create_time`(`create_time` ASC) USING BTREE,
  INDEX `idx_wechat_article_id`(`wechat_article_id` ASC) USING BTREE,
  INDEX `idx_wechat_source`(`wechat_source` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 13 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci COMMENT = '精彩活动表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of mini_activity
-- ----------------------------
INSERT INTO `mini_activity` VALUES (9, '测试页', '测试1：？？？？？？。。。。。', 'http://mmecoa.qpic.cn/sz_mmecoa_jpg/pPkwklJGkOjIquLf969jCs7PibyfibbHFR3cKjjcZwRcTqkUhiae6rGAq0OcvoGKtmBDyTS9LeJnK0ZhkNiaZmOBtg/0?wx_fmt=jpeg', 'http://mp.weixin.qq.com/s?__biz=MzkwMjg1MDQ0OA==&mid=2247483691&idx=1&sn=d174a5077d4e53936c141998a7559d12&chksm=c09e73d4f7e9fac205b4156971ce00a9f71e97e8f500dd4fddb176b610952f52349bffa1791e#rd', 0, '0', '5Rne1KrITOUcetDKrjXdbkE5xkM7QD8QpqU525RUG5KcPjiXbvt6WKgrD2E40irt', '宇悦灵识', '', '2025-07-18 09:39:58', '', NULL, NULL);
INSERT INTO `mini_activity` VALUES (10, '\\u58eb\\u5927\\u592b\\u53d1\\u751fs', '\\u4e09\\u56fd\\u6740', 'http://mmecoa.qpic.cn/sz_mmecoa_jpg/pPkwklJGkOjIquLf969jCs7PibyfibbHFRI4loxQIX2OkWgzm2Qz6Vze9gU9jcnrwc2XYVQhiadiahQob6TC5fia4FA/0?wx_fmt=jpeg', 'http://mp.weixin.qq.com/s?__biz=MzkwMjg1MDQ0OA==&mid=2247483681&idx=1&sn=bd8985930c85718086532a6eec2bd2b6&chksm=c09e73def7e9fac8f9902ecfe225e868a8789aaa0c19ade07e64e130b90afbe4d8412f476601#rd', 0, '0', '5Rne1KrITOUcetDKrjXdbosiB60-zAvqOGfyGSPpbI03YdFes0Fijl40FxmYjRS3', '宇悦灵识', '', '2025-07-18 09:39:13', '', NULL, NULL);
INSERT INTO `mini_activity` VALUES (11, '江西宇悦科技有限公司', '江西宇悦科技有限公司，成立于2020-07-28，位于江西省上饶市，是一家以从事互联网和相关服务为主的企业。', 'http://mmecoa.qpic.cn/sz_mmecoa_jpg/pPkwklJGkOjIquLf969jCs7PibyfibbHFR3cKjjcZwRcTqkUhiae6rGAq0OcvoGKtmBDyTS9LeJnK0ZhkNiaZmOBtg/0?wx_fmt=jpeg', 'http://mp.weixin.qq.com/s?__biz=MzkwMjg1MDQ0OA==&mid=2247483686&idx=1&sn=1e1060f7efcb2d52eda01c30741caab5&chksm=c09e73d9f7e9facf47bb14bd85f59cd2aef27f271eb503eabd3708bea54cc07df78611043684#rd', 0, '0', '5Rne1KrITOUcetDKrjXdbh0cIgQKxFx5HWwXw8CSOie9c16CfkTLZCizxZAxfjVW', '宇悦灵识', '', '2025-07-16 11:14:25', '', NULL, NULL);
INSERT INTO `mini_activity` VALUES (12, '\\u963f\\u8fbe\\u5927\\u6492\\u5927\\u6492\\u5927\\u5927', '\\u7684\\u53d1\\u751f\\u53d1\\u987a\\u4e30', 'http://mmecoa.qpic.cn/sz_mmecoa_jpg/pPkwklJGkOjIquLf969jCs7PibyfibbHFRI4loxQIX2OkWgzm2Qz6Vze9gU9jcnrwc2XYVQhiadiahQob6TC5fia4FA/0?wx_fmt=jpeg', 'http://mp.weixin.qq.com/s?__biz=MzkwMjg1MDQ0OA==&mid=2247483676&idx=1&sn=3304ee2fc84a1999f136455b1702f373&chksm=c09e73e3f7e9faf57dbd1160b9c67fd3520b04d9c9e8a6f9fe0e9fb5746e5973e601e70cb5b8#rd', 0, '0', '5Rne1KrITOUcetDKrjXdbtONvWPO29AoMO7Q5TR1WT2oWDS5ROe_a6tPsk4hTg5d', '宇悦灵识', '', '2025-07-16 10:34:12', '', NULL, NULL);

-- ----------------------------
-- Table structure for mini_banner
-- ----------------------------
DROP TABLE IF EXISTS `mini_banner`;
CREATE TABLE `mini_banner`  (
  `banner_id` bigint NOT NULL AUTO_INCREMENT COMMENT '轮播图ID',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '轮播图标题',
  `image_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '图片地址',
  `link_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '跳转链接',
  `sort_order` int NULL DEFAULT 0 COMMENT '排序（数字越小越靠前）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`banner_id`) USING BTREE,
  INDEX `idx_sort_order`(`sort_order` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci COMMENT = '轮播图表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of mini_banner
-- ----------------------------
INSERT INTO `mini_banner` VALUES (1, '轮播图-测试一', 'http://************:8080/profile/upload/2025/07/17/wallhaven-3k62ov_1920x1080_20250717170539A003.png', 'https://www.baidu.com/', 0, '0', '', '2025-07-04 16:17:48', '', '2025-07-17 17:08:41', NULL);
INSERT INTO `mini_banner` VALUES (2, '轮播图-测试二', 'http://************:8080/profile/upload/2025/07/17/wallhaven-5wrwq9_1920x1080_20250717170551A004.png', '', 1, '0', '', '2025-07-04 16:47:08', '', '2025-07-17 17:05:52', NULL);
INSERT INTO `mini_banner` VALUES (3, '测试', 'http://************:8080/profile/upload/2025/07/17/05671daab7d04086aaa594abb85e99ca_20250717170616A006.jpg', '', 3, '0', '', '2025-07-17 16:40:21', '', '2025-07-17 17:06:19', NULL);
INSERT INTO `mini_banner` VALUES (4, '测试', 'http://************:8080/profile/upload/2025/07/17/bbc58c3d998d4f389fdae649334873f721_20250717170600A005.jpg', '', 3, '0', '', '2025-07-17 16:50:17', '', '2025-07-17 17:06:01', NULL);
INSERT INTO `mini_banner` VALUES (5, 'test', 'http://************:8080/profile/upload/2025/07/17/09fa24b9a06f4f69b8284ca56b67b2b3_20250717171550A001.jpg', '', 4, '0', '', '2025-07-17 17:16:04', '', NULL, NULL);

-- ----------------------------
-- Table structure for mini_barrage
-- ----------------------------
DROP TABLE IF EXISTS `mini_barrage`;
CREATE TABLE `mini_barrage`  (
  `barrage_id` bigint NOT NULL AUTO_INCREMENT COMMENT '弹幕ID',
  `user_id` bigint NOT NULL COMMENT '发布用户ID',
  `user_nick_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '发布用户昵称（冗余字段）',
  `user_avatar_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '发布用户头像（冗余字段）',
  `content` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '弹幕内容',
  `audit_status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '0' COMMENT '审核状态（0待审核 1审核通过 2审核拒绝）',
  `audit_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '审核人',
  `audit_time` datetime NULL DEFAULT NULL COMMENT '审核时间',
  `audit_remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '审核备注',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`barrage_id`) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_audit_status`(`audit_status` ASC) USING BTREE,
  INDEX `idx_create_time`(`create_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 28 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci COMMENT = '弹幕表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of mini_barrage
-- ----------------------------

-- ----------------------------
-- Table structure for mini_college
-- ----------------------------
DROP TABLE IF EXISTS `mini_college`;
CREATE TABLE `mini_college`  (
  `college_id` bigint NOT NULL AUTO_INCREMENT COMMENT '学院ID',
  `college_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '学院名称',
  `college_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '学院代码',
  `college_desc` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '学院描述',
  `dean_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '院长姓名',
  `contact_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '联系电话',
  `contact_email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '联系邮箱',
  `address` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '学院地址',
  `website_url` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '学院官网',
  `sort_order` int NULL DEFAULT 0 COMMENT '排序',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`college_id`) USING BTREE,
  UNIQUE INDEX `uk_college_code`(`college_code` ASC) USING BTREE,
  INDEX `idx_college_name`(`college_name` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci COMMENT = '学院信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of mini_college
-- ----------------------------
INSERT INTO `mini_college` VALUES (1, '计算机科学与技术学院', 'CS', '致力于培养计算机科学与技术领域的高素质人才', '张教授', '022-12345678', '<EMAIL>', '天津大学北洋园校区', 'http://cs.tju.edu.cn', 1, '0', 'admin', '2025-07-23 18:12:52', '', NULL, NULL);
INSERT INTO `mini_college` VALUES (2, '电气自动化与信息工程学院', 'EE', '专注于电气工程和自动化技术人才培养', '李教授', '022-12345679', '<EMAIL>', '天津大学北洋园校区', 'http://ee.tju.edu.cn', 2, '0', 'admin', '2025-07-23 18:12:52', '', NULL, NULL);
INSERT INTO `mini_college` VALUES (3, '机械工程学院', 'ME', '培养机械工程领域的创新型人才', '王教授', '022-12345680', '<EMAIL>', '天津大学北洋园校区', 'http://me.tju.edu.cn', 3, '0', 'admin', '2025-07-23 18:12:52', '', NULL, NULL);
INSERT INTO `mini_college` VALUES (4, '化工学院', 'CHE', '化学工程与工艺专业人才培养基地', '刘教授', '022-12345681', '<EMAIL>', '天津大学北洋园校区', 'http://che.tju.edu.cn', 4, '0', 'admin', '2025-07-23 18:12:52', '', NULL, NULL);
INSERT INTO `mini_college` VALUES (5, '建筑工程学院', 'CE', '土木工程和建筑学专业教育', '陈教授', '022-12345682', '<EMAIL>', '天津大学北洋园校区', 'http://ce.tju.edu.cn', 5, '0', 'admin', '2025-07-23 18:12:52', '', NULL, NULL);

-- ----------------------------
-- Table structure for mini_competition_config
-- ----------------------------
DROP TABLE IF EXISTS `mini_competition_config`;
CREATE TABLE `mini_competition_config`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `top_image_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '顶部图片URL',
  `description_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '文字介绍内容',
  `schedule_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '赛程说明（富文本）',
  `registration_conditions` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '报名条件（富文本）',
  `faq_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '常见问题（富文本）',
  `business_cooperation` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '商务合作（富文本）',
  `sponsor_image_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '冠名图片URL',
  `contact_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系人',
  `contact_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系电话',
  `contact_wechat` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '微信号',
  `status` tinyint(1) NULL DEFAULT 0 COMMENT '状态：0-正常，1-停用',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '大赛报名页面配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of mini_competition_config
-- ----------------------------
INSERT INTO `mini_competition_config` VALUES (1, 'http://************:8080/profile/upload/2025/07/21/2ee98ca5-8979-409a-a864-5ed7234a69b4_20250721112830A001.jpg', '<h2>海棠杯创新创业大赛</h2><p>海棠杯创新创业大赛是天津大学举办的高水平创新创业竞赛，旨在培养学生创新精神和创业能力，促进产学研深度融合。</p><p>大赛面向全校师生开放，鼓励跨学科、跨专业团队参赛，为优秀项目提供孵化支持和投资对接机会。</p><p><br></p><p><img src=\"http://************:8086/profile/upload/2025/07/30/066ba870048b40098b3db2b9a9072812_20250730112533A007.jpg\"></p>', '<h3>赛程安排</h3><ol><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><strong>报名阶段：</strong>2025年2月1日 - 2月28日</li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><strong>初赛：</strong>2025年3月1日 - 3月15日</li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><strong>复赛：</strong>2025年3月20日 - 4月5日</li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><strong>决赛：</strong>2025年4月10日 - 4月15日</li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><strong>颁奖典礼：</strong>2025年4月20日</li></ol><p>具体时间安排将根据报名情况进行微调，请关注官方通知。</p>', '<p>报名条件</p><ol><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span>天津大学在校学生（本科生、研究生、博士生）</li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span>天津大学教职工可作为指导老师参与</li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span>团队规模2-8人，鼓励跨学科组队</li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span>项目具有创新性、可行性和市场前景</li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span>遵守学术诚信，无知识产权争议</li></ol>', '<h3>常见问题</h3><h4>Q: 可以个人参赛吗？</h4><p>A: 建议以团队形式参赛，个人参赛需要项目特别优秀。</p><h4>Q: 项目需要已经实施吗？</h4><p>A: 不需要，创意阶段的项目也可以参赛。</p><h4>Q: 有奖金吗？</h4><p>A: 设有丰厚奖金，一等奖10万元，二等奖5万元，三等奖2万元。</p>', '<h3>商务合作</h3><p>欢迎企业、投资机构、孵化器等与我们合作，共同促进大学生创新创业。</p><p>合作形式包括：</p><ol><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span>赞助支持</li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span>导师指导</li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span>项目孵化</li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span>投资对接</li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span>实习就业</li></ol><p>合作洽谈请联系组委会。</p>', 'http://************:8080/profile/upload/2025/07/21/中国工商银行 (2)_20250721181101A001.png', '张老师', '022-27400001', 'haitangbei2025', 0, '2025-07-08 10:10:14', '2025-07-30 03:25:35');

-- ----------------------------
-- Table structure for mini_contact
-- ----------------------------
DROP TABLE IF EXISTS `mini_contact`;
CREATE TABLE `mini_contact`  (
  `contact_id` bigint NOT NULL AUTO_INCREMENT COMMENT '联系人ID',
  `contact_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '联系人姓名',
  `contact_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '联系人唯一标识',
  `contact_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '联系电话',
  `qr_code_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '联系二维码URL',
  `sort_order` int NULL DEFAULT 0 COMMENT '排序（数字越小越靠前）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`contact_id`) USING BTREE,
  UNIQUE INDEX `uk_contact_code`(`contact_code` ASC) USING BTREE COMMENT '联系人标识唯一索引',
  INDEX `idx_sort_order`(`sort_order` ASC) USING BTREE COMMENT '排序索引',
  INDEX `idx_status`(`status` ASC) USING BTREE COMMENT '状态索引',
  INDEX `idx_create_time`(`create_time` ASC) USING BTREE COMMENT '创建时间索引'
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '联系人管理表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of mini_contact
-- ----------------------------
INSERT INTO `mini_contact` VALUES (1, '客服小助手', 'SERVICE', '************', NULL, 1, '0', 'admin', '2025-07-28 04:37:00', '', '2025-07-29 16:21:53', '主要客服联系人');
INSERT INTO `mini_contact` VALUES (2, '官方需求对接', 'OFFICIAL', '400-123-4568', 'http://************:8086/profile/upload/2025/07/28/20250728-143224_20250728143235A001.jpg', 2, '0', 'admin', '2025-07-28 04:37:00', '', '2025-07-29 16:21:58', '技术问题咨询');
INSERT INTO `mini_contact` VALUES (3, '商务合作', 'BUSINESS', '400-123-4569', NULL, 3, '0', 'admin', '2025-07-28 04:37:00', '', '2025-07-29 16:22:02', '商务合作洽谈');
INSERT INTO `mini_contact` VALUES (4, '田佳灵', 'GOLDEN', '***********', 'http://************:8086/profile/upload/2025/07/29/20250728-143224_20250729113154A001.jpg', 5, '0', '', '2025-07-29 11:32:13', '', '2025-07-29 16:22:12', '金种子专区问题咨询');
INSERT INTO `mini_contact` VALUES (5, '李帅', 'PARK_OFFICIAL', '***********', NULL, 4, '0', '', '2025-07-29 11:34:35', '', '2025-07-29 16:22:07', '园区入驻/办公载体问题咨询');
INSERT INTO `mini_contact` VALUES (6, '刘璐', 'DEFAULT', '***********', 'http://************:8086/profile/upload/2025/07/29/刘璐_20250729162118A002.png', 0, '0', '', '2025-07-29 16:21:43', '', NULL, NULL);

-- ----------------------------
-- Table structure for mini_demand
-- ----------------------------
DROP TABLE IF EXISTS `mini_demand`;
CREATE TABLE `mini_demand`  (
  `demand_id` bigint NOT NULL AUTO_INCREMENT COMMENT '需求ID',
  `category_id` bigint NOT NULL COMMENT '分类ID',
  `user_id` bigint NULL DEFAULT NULL COMMENT '发布用户ID',
  `demand_title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '需求标题',
  `demand_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '需求类型(字典值)',
  `demand_desc` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL COMMENT '需求描述',
  `contact_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '联系人姓名',
  `contact_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '联系人手机',
  `demand_status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '0' COMMENT '需求状态（0已发布 1已对接 2已下架）',
  `is_top` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '0' COMMENT '是否置顶（0否 1是）',
  `view_count` int NULL DEFAULT 0 COMMENT '浏览次数',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '备注',
  `form_data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL COMMENT '动态表单数据(JSON格式)',
  PRIMARY KEY (`demand_id`) USING BTREE,
  INDEX `idx_category_id`(`category_id` ASC) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_demand_status`(`demand_status` ASC) USING BTREE,
  INDEX `idx_create_time`(`create_time` ASC) USING BTREE,
  INDEX `idx_is_top`(`is_top` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 12 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci COMMENT = '需求信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of mini_demand
-- ----------------------------

-- ----------------------------
-- Table structure for mini_demand_category
-- ----------------------------
DROP TABLE IF EXISTS `mini_demand_category`;
CREATE TABLE `mini_demand_category`  (
  `category_id` bigint NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `category_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '分类名称',
  `category_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '类型标识代码',
  `category_short_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '类型名称简称',
  `category_icon` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '类型图标URL',
  `category_desc` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL COMMENT '分类详细介绍',
  `sort_order` int NULL DEFAULT 0 COMMENT '排序',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '备注',
  `form_fields` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL COMMENT '表单字段配置(JSON格式)',
  PRIMARY KEY (`category_id`) USING BTREE,
  UNIQUE INDEX `uk_category_code`(`category_code` ASC) USING BTREE COMMENT '类型标识唯一索引',
  INDEX `idx_sort_order`(`sort_order` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci COMMENT = '需求分类表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of mini_demand_category
-- ----------------------------
INSERT INTO `mini_demand_category` VALUES (1, '融资对接', 'financing', '融资', 'http://************:8086/profile/upload/2025/07/28/img_v3_02ok_2a7eaa5b-74f5-49fa-b11b-59f818d623fg_20250728110515A011.png', '<p>软件开发、系统集成、技术咨询等技术类需求</p>', 1, '0', 'admin', '2025-07-07 14:46:37', '', NULL, '技术开发类需求', '[{\"name\":\"技术合作\",\"description\":\"请仔细阅读以下说明后填写表单\",\"fields\":[{\"label\":\"温馨提示\",\"name\":\"field_404542\",\"type\":\"static\",\"required\":false,\"hidden\":false,\"options\":\"\",\"placeholder\":\"\",\"staticContent\":\"请如实填写以下信息，我们将在24小时内与您取得联系。带*号的为必填项。\"}],\"icon\":\"http://localhost:8086/profile/upload/2025/07/28/科学技术-5_20250728104013A002.png\"},{\"name\":\"基础信息\",\"description\":\"请填写需求的基本信息\",\"fields\":[{\"label\":\"需求标题\",\"name\":\"field_404542\",\"type\":\"input\",\"required\":true,\"hidden\":false,\"options\":\"\",\"placeholder\":\"请输入需求标题\",\"staticContent\":\"\"},{\"label\":\"需求描述\",\"name\":\"description\",\"type\":\"textarea\",\"required\":true,\"hidden\":false,\"options\":\"\",\"placeholder\":\"请详细描述您的需求\",\"staticContent\":\"\"}]},{\"name\":\"联系方式\",\"description\":\"请填写您的联系方式，以便我们与您取得联系\",\"fields\":[{\"label\":\"联系人\",\"name\":\"contact_name\",\"type\":\"input\",\"required\":true,\"hidden\":false,\"options\":\"\",\"placeholder\":\"请输入联系人姓名\",\"staticContent\":\"\"},{\"label\":\"联系电话\",\"name\":\"phone\",\"type\":\"tel\",\"required\":true,\"hidden\":false,\"options\":\"\",\"placeholder\":\"请输入手机号码\",\"staticContent\":\"\"}]}]');
INSERT INTO `mini_demand_category` VALUES (2, '技术合作', 'technology', '技术', 'http://************:8086/profile/upload/2025/07/28/img_v3_02ok_5da964dd-82d3-4e84-be35-52e11fff321g_20250728110527A012.png', '<p>品牌推广、市场营销、渠道合作等市场类需求</p>', 2, '0', 'admin', '2025-07-07 14:46:37', '', NULL, '揭榜挂帅', '[{\"name\":\"技术需求\",\"description\":\"请详细描述您的技术需求\",\"fields\":[{\"label\":\"技术方向\",\"name\":\"tech_direction\",\"type\":\"select\",\"required\":true,\"options\":\"前端开发,后端开发,移动开发,人工智能,大数据,云计算\",\"placeholder\":\"请选择技术方向\",\"staticContent\":\"\",\"hidden\":true},{\"label\":\"技术栈\",\"name\":\"tech_stack\",\"type\":\"checkbox\",\"required\":true,\"options\":\"Java,Python,JavaScript,React,Vue,Spring Boot,MySQL,Redis\",\"placeholder\":\"\",\"staticContent\":\"\",\"hidden\":false},{\"label\":\"项目详细需求\",\"name\":\"detailed_requirements\",\"type\":\"textarea\",\"required\":true,\"options\":\"\",\"placeholder\":\"请详细描述项目需求、功能要求、技术要求等\",\"staticContent\":\"\",\"hidden\":false},{\"label\":\"附件上传\",\"name\":\"field_901841\",\"type\":\"file\",\"required\":false,\"options\":\"\",\"placeholder\":\"未选择文件\",\"staticContent\":\"\",\"hidden\":false}],\"icon\":\"http://************:8080/profile/upload/2025/07/23/6d1ffde61ff84984a225945dd3383da0_20250723151630A001.jpg\"}]');
INSERT INTO `mini_demand_category` VALUES (3, '资源场景', 'scenario', '场景', 'http://************:8086/profile/upload/2025/07/28/img_v3_02ok_b934e4e6-8270-4d6a-b17d-b465096e127g_20250728110541A014.png', '<p>人才招聘、人才推荐、人才培训等人才类需求</p>', 3, '0', 'admin', '2025-07-07 14:46:37', '', NULL, '人才招聘类需求', '[{\"name\":\"基础信息\",\"description\":\"\",\"fields\":[{\"label\":\"企业全称\",\"name\":\"field_652408\",\"type\":\"input\",\"required\":true,\"options\":\"\",\"placeholder\":\"请输入\",\"staticContent\":\"\"},{\"label\":\"行业标签\",\"name\":\"field_720944\",\"type\":\"select\",\"required\":true,\"options\":\"新能源,硬科技\",\"placeholder\":\"请选择\",\"staticContent\":\"\"},{\"label\":\"所在地区\",\"name\":\"field_756673\",\"type\":\"select\",\"required\":true,\"options\":\"西青,江西\",\"placeholder\":\"请选择\",\"staticContent\":\"\"},{\"label\":\"联系人\",\"name\":\"contact_name\",\"type\":\"input\",\"required\":true,\"options\":\"\",\"placeholder\":\"请输入\",\"staticContent\":\"\"},{\"label\":\"职位\",\"name\":\"position\",\"type\":\"input\",\"required\":true,\"options\":\"\",\"placeholder\":\"请输入\",\"staticContent\":\"\"},{\"label\":\"电话\",\"name\":\"phone\",\"type\":\"tel\",\"required\":true,\"options\":\"\",\"placeholder\":\"请输入\",\"staticContent\":\"\"},{\"label\":\"官网\",\"name\":\"field_840720\",\"type\":\"input\",\"required\":false,\"options\":\"\",\"placeholder\":\"请输入\",\"staticContent\":\"\"},{\"label\":\"公众号/视频号\",\"name\":\"field_866127\",\"type\":\"input\",\"required\":false,\"options\":\"\",\"placeholder\":\"请输入\",\"staticContent\":\"\"}],\"icon\":\"http://************:8080/profile/upload/2025/07/22/20250722-100228_20250722100403A004.png\"},{\"name\":\"传播诉求（可多选）\",\"description\":\"\",\"fields\":[{\"label\":\"需求类型（可多选）\",\"name\":\"field_904177\",\"type\":\"checkbox\",\"required\":true,\"options\":\"投资机构,产业链合作伙伴,终端客户,行业专家,高水平人才\",\"placeholder\":\"请输入\",\"staticContent\":\"\"},{\"label\":\"当前传播短板（可多选）\",\"name\":\"field_040280\",\"type\":\"checkbox\",\"required\":true,\"options\":\"技术价值说不清,行业认知度低,缺乏发声通道,品牌故事薄弱\",\"placeholder\":\"请输入\",\"staticContent\":\"\"},{\"label\":\"希望强化的内容方向（可多选）\",\"name\":\"field_149229\",\"type\":\"checkbox\",\"required\":true,\"options\":\"技术解析（专利/原理）,应用场景案例,创始人故事,行业趋势洞察,融资/合作需求\",\"placeholder\":\"请输入\",\"staticContent\":\"\"}],\"icon\":\"http://************:8080/profile/upload/2025/07/22/20250722-100236_20250722100422A005.png\"},{\"name\":\"资源需求（可多选）\",\"description\":\"\",\"fields\":[{\"label\":\"资源需求（可多选）\",\"name\":\"field_321414\",\"type\":\"checkbox\",\"required\":true,\"options\":\"融资对接,产业链合作,媒体曝光,行业论坛发声,技术专家背书,人才招募支持,其他\",\"placeholder\":\"请输入\",\"staticContent\":\"\"}],\"icon\":\"http://************:8080/profile/upload/2025/07/22/20250722-100240_20250722100431A006.png\"},{\"name\":\"企业亮点\",\"description\":\"\",\"fields\":[{\"label\":\"所突破的技术壁垒\",\"name\":\"field_425137\",\"type\":\"input\",\"required\":true,\"options\":\"\",\"placeholder\":\"请用非专业人士能理解的语言描述，例：“全球首个实现XX材料低温合成技术，成本降低50%”\",\"staticContent\":\"\"},{\"label\":\"所解决的行业核心痛点\",\"name\":\"field_566277\",\"type\":\"input\",\"required\":true,\"options\":\"\",\"placeholder\":\"请输入，例：“解决新能源电池低温续航衰减30%的难题”\",\"staticContent\":\"\"},{\"label\":\"差异化优势\",\"name\":\"field_707577\",\"type\":\"input\",\"required\":true,\"options\":\"\",\"placeholder\":\"请用非专业人士能理解的语言描述，例：“独家专利算法，精准度超行业标准2倍”\",\"staticContent\":\"\"},{\"label\":\"关键数据\",\"name\":\"field_810378\",\"type\":\"input\",\"required\":true,\"options\":\"\",\"placeholder\":\"订单/合作/技术指标，例：“已获3家上市公司订单，2024营收突破2000万”\",\"staticContent\":\"\"},{\"label\":\"重要背书\",\"name\":\"field_901660\",\"type\":\"input\",\"required\":true,\"options\":\"\",\"placeholder\":\"奖项/专家评价/头部客户，例：“入选国家XX计划，获院士团队技术认证”\",\"staticContent\":\"\"}],\"icon\":\"http://************:8080/profile/upload/2025/07/22/20250722-100244_20250722100451A008.png\"},{\"name\":\"其他材料补充\",\"description\":\"\",\"fields\":[{\"label\":\"上传附件\",\"name\":\"field_989222\",\"type\":\"file\",\"required\":false,\"options\":\"\",\"placeholder\":\"未选择任何文件\",\"staticContent\":\"\"},{\"label\":\"邮件提交至\",\"name\":\"field_227969\",\"type\":\"static\",\"required\":false,\"options\":\"\",\"placeholder\":\"请输入\",\"staticContent\":\"<EMAIL>(文件名：【企业曝光申请】-企业/项目名）\"}],\"icon\":\"http://************:8080/profile/upload/2025/07/22/20250722-100247_20250722100459A009.png\"}]');
INSERT INTO `mini_demand_category` VALUES (4, '政策资质', 'qualification', '资质', 'http://************:8086/profile/upload/2025/07/28/img_v3_02ok_4be7b4c5-9ec4-49c2-892f-ea46059eb0bg_20250728110548A015.png', '<p>项目投资、风险投资、资金对接等投资类需求</p>', 6, '0', 'admin', '2025-07-07 14:46:37', '', NULL, '资金投资类需求', '[{\"name\":\"基础信息\",\"description\":\"\",\"fields\":[{\"label\":\"企业全称\",\"name\":\"field_652408\",\"type\":\"input\",\"required\":true,\"options\":\"\",\"placeholder\":\"请输入\",\"staticContent\":\"\",\"hidden\":false},{\"label\":\"行业标签\",\"name\":\"field_720944\",\"type\":\"select\",\"required\":true,\"options\":\"新能源,硬科技\",\"placeholder\":\"请选择\",\"staticContent\":\"\",\"hidden\":false},{\"label\":\"所在地区\",\"name\":\"field_756673\",\"type\":\"select\",\"required\":true,\"options\":\"西青,江西\",\"placeholder\":\"请选择\",\"staticContent\":\"\",\"hidden\":false},{\"label\":\"联系人\",\"name\":\"contact_name\",\"type\":\"input\",\"required\":true,\"options\":\"\",\"placeholder\":\"请输入\",\"staticContent\":\"\",\"hidden\":false},{\"label\":\"职位\",\"name\":\"position\",\"type\":\"input\",\"required\":true,\"options\":\"\",\"placeholder\":\"请输入\",\"staticContent\":\"\",\"hidden\":false},{\"label\":\"电话\",\"name\":\"phone\",\"type\":\"tel\",\"required\":true,\"options\":\"\",\"placeholder\":\"请输入\",\"staticContent\":\"\",\"hidden\":false},{\"label\":\"官网\",\"name\":\"field_840720\",\"type\":\"input\",\"required\":false,\"options\":\"\",\"placeholder\":\"请输入\",\"staticContent\":\"\",\"hidden\":false},{\"label\":\"公众号/视频号\",\"name\":\"field_866127\",\"type\":\"input\",\"required\":false,\"options\":\"\",\"placeholder\":\"请输入\",\"staticContent\":\"\",\"hidden\":false}],\"icon\":\"http://************:8080/profile/upload/2025/07/22/20250722-100228_20250722100403A004.png\"},{\"name\":\"传播诉求（可多选）\",\"description\":\"\",\"fields\":[{\"label\":\"需求类型（可多选）\",\"name\":\"field_904177\",\"type\":\"checkbox\",\"required\":true,\"options\":\"投资机构,产业链合作伙伴,终端客户,行业专家,高水平人才\",\"placeholder\":\"请输入\",\"staticContent\":\"\",\"hidden\":false},{\"label\":\"当前传播短板（可多选）\",\"name\":\"field_040280\",\"type\":\"checkbox\",\"required\":true,\"options\":\"技术价值说不清,行业认知度低,缺乏发声通道,品牌故事薄弱\",\"placeholder\":\"请输入\",\"staticContent\":\"\",\"hidden\":false},{\"label\":\"希望强化的内容方向（可多选）\",\"name\":\"field_149229\",\"type\":\"checkbox\",\"required\":true,\"options\":\"技术解析（专利/原理）,应用场景案例,创始人故事,行业趋势洞察,融资/合作需求\",\"placeholder\":\"请输入\",\"staticContent\":\"\",\"hidden\":false}],\"icon\":\"http://************:8080/profile/upload/2025/07/22/20250722-100236_20250722100422A005.png\"},{\"name\":\"资源需求（可多选）\",\"description\":\"\",\"fields\":[{\"label\":\"资源需求（可多选）\",\"name\":\"field_321414\",\"type\":\"checkbox\",\"required\":true,\"options\":\"融资对接,产业链合作,媒体曝光,行业论坛发声,技术专家背书,人才招募支持,其他\",\"placeholder\":\"请输入\",\"staticContent\":\"\",\"hidden\":false}],\"icon\":\"http://************:8080/profile/upload/2025/07/22/20250722-100240_20250722100431A006.png\"},{\"name\":\"企业亮点\",\"description\":\"\",\"fields\":[{\"label\":\"所突破的技术壁垒\",\"name\":\"field_425137\",\"type\":\"input\",\"required\":true,\"options\":\"\",\"placeholder\":\"请用非专业人士能理解的语言描述，例：“全球首个实现XX材料低温合成技术，成本降低50%”\",\"staticContent\":\"\",\"hidden\":false},{\"label\":\"所解决的行业核心痛点\",\"name\":\"field_566277\",\"type\":\"input\",\"required\":true,\"options\":\"\",\"placeholder\":\"请输入，例：“解决新能源电池低温续航衰减30%的难题”\",\"staticContent\":\"\",\"hidden\":false},{\"label\":\"差异化优势\",\"name\":\"field_707577\",\"type\":\"input\",\"required\":true,\"options\":\"\",\"placeholder\":\"请用非专业人士能理解的语言描述，例：“独家专利算法，精准度超行业标准2倍”\",\"staticContent\":\"\",\"hidden\":false},{\"label\":\"关键数据\",\"name\":\"field_810378\",\"type\":\"input\",\"required\":true,\"options\":\"\",\"placeholder\":\"订单/合作/技术指标，例：“已获3家上市公司订单，2024营收突破2000万”\",\"staticContent\":\"\",\"hidden\":false},{\"label\":\"重要背书\",\"name\":\"field_901660\",\"type\":\"input\",\"required\":true,\"options\":\"\",\"placeholder\":\"奖项/专家评价/头部客户，例：“入选国家XX计划，获院士团队技术认证”\",\"staticContent\":\"\",\"hidden\":false}],\"icon\":\"http://************:8080/profile/upload/2025/07/22/20250722-100244_20250722100451A008.png\"},{\"name\":\"其他材料补充\",\"description\":\"\",\"fields\":[{\"label\":\"上传附件\",\"name\":\"field_989222\",\"type\":\"file\",\"required\":false,\"options\":\"\",\"placeholder\":\"未选择任何文件\",\"staticContent\":\"\",\"hidden\":false},{\"label\":\"邮件提交至\",\"name\":\"field_227969\",\"type\":\"static\",\"required\":false,\"options\":\"\",\"placeholder\":\"请输入\",\"staticContent\":\"<EMAIL>(文件名：【企业曝光申请】-企业/项目名）\",\"hidden\":false}],\"icon\":\"http://************:8080/profile/upload/2025/07/22/20250722-100247_20250722100459A009.png\"}]');
INSERT INTO `mini_demand_category` VALUES (5, '载体厂房', 'office', '载体', 'http://************:8086/profile/upload/2025/07/30/img_v3_02ok_f3e3b87e-3b81-47b9-afa5-58397a2f19cg_20250730093135A004.png', '<p>产品采购、设备采购、原材料采购等采购类需求</p>', 5, '0', 'admin', '2025-07-07 14:46:37', '', NULL, '办公载体', '[{\"name\":\"基础信息\",\"description\":\"\",\"fields\":[{\"label\":\"企业全称\",\"name\":\"field_652408\",\"type\":\"input\",\"required\":true,\"options\":\"\",\"placeholder\":\"请输入\",\"staticContent\":\"\",\"hidden\":false},{\"label\":\"行业标签\",\"name\":\"field_720944\",\"type\":\"select\",\"required\":true,\"options\":\"新能源,硬科技\",\"placeholder\":\"请选择\",\"staticContent\":\"\",\"hidden\":false},{\"label\":\"所在地区\",\"name\":\"field_756673\",\"type\":\"select\",\"required\":true,\"options\":\"西青,江西\",\"placeholder\":\"请选择\",\"staticContent\":\"\",\"hidden\":false},{\"label\":\"联系人\",\"name\":\"contact_name\",\"type\":\"input\",\"required\":true,\"options\":\"\",\"placeholder\":\"请输入\",\"staticContent\":\"\",\"hidden\":false},{\"label\":\"职位\",\"name\":\"position\",\"type\":\"input\",\"required\":true,\"options\":\"\",\"placeholder\":\"请输入\",\"staticContent\":\"\",\"hidden\":false},{\"label\":\"电话\",\"name\":\"phone\",\"type\":\"tel\",\"required\":true,\"options\":\"\",\"placeholder\":\"请输入\",\"staticContent\":\"\",\"hidden\":false},{\"label\":\"官网\",\"name\":\"field_840720\",\"type\":\"input\",\"required\":false,\"options\":\"\",\"placeholder\":\"请输入\",\"staticContent\":\"\",\"hidden\":false},{\"label\":\"公众号/视频号\",\"name\":\"field_866127\",\"type\":\"input\",\"required\":false,\"options\":\"\",\"placeholder\":\"请输入\",\"staticContent\":\"\",\"hidden\":false}],\"icon\":\"http://************:8080/profile/upload/2025/07/22/20250722-100228_20250722100403A004.png\"},{\"name\":\"传播诉求（可多选）\",\"description\":\"\",\"fields\":[{\"label\":\"需求类型（可多选）\",\"name\":\"field_904177\",\"type\":\"checkbox\",\"required\":true,\"options\":\"投资机构,产业链合作伙伴,终端客户,行业专家,高水平人才\",\"placeholder\":\"请输入\",\"staticContent\":\"\",\"hidden\":false},{\"label\":\"当前传播短板（可多选）\",\"name\":\"field_040280\",\"type\":\"checkbox\",\"required\":true,\"options\":\"技术价值说不清,行业认知度低,缺乏发声通道,品牌故事薄弱\",\"placeholder\":\"请输入\",\"staticContent\":\"\",\"hidden\":false},{\"label\":\"希望强化的内容方向（可多选）\",\"name\":\"field_149229\",\"type\":\"checkbox\",\"required\":true,\"options\":\"技术解析（专利/原理）,应用场景案例,创始人故事,行业趋势洞察,融资/合作需求\",\"placeholder\":\"请输入\",\"staticContent\":\"\",\"hidden\":false}],\"icon\":\"http://************:8080/profile/upload/2025/07/22/20250722-100236_20250722100422A005.png\"},{\"name\":\"资源需求（可多选）\",\"description\":\"\",\"fields\":[{\"label\":\"资源需求（可多选）\",\"name\":\"field_321414\",\"type\":\"checkbox\",\"required\":true,\"options\":\"融资对接,产业链合作,媒体曝光,行业论坛发声,技术专家背书,人才招募支持,其他\",\"placeholder\":\"请输入\",\"staticContent\":\"\",\"hidden\":false}],\"icon\":\"http://************:8080/profile/upload/2025/07/22/20250722-100240_20250722100431A006.png\"},{\"name\":\"企业亮点\",\"description\":\"\",\"fields\":[{\"label\":\"所突破的技术壁垒\",\"name\":\"field_425137\",\"type\":\"input\",\"required\":true,\"options\":\"\",\"placeholder\":\"请用非专业人士能理解的语言描述，例：“全球首个实现XX材料低温合成技术，成本降低50%”\",\"staticContent\":\"\",\"hidden\":false},{\"label\":\"所解决的行业核心痛点\",\"name\":\"field_566277\",\"type\":\"input\",\"required\":true,\"options\":\"\",\"placeholder\":\"请输入，例：“解决新能源电池低温续航衰减30%的难题”\",\"staticContent\":\"\",\"hidden\":false},{\"label\":\"差异化优势\",\"name\":\"field_707577\",\"type\":\"input\",\"required\":true,\"options\":\"\",\"placeholder\":\"请用非专业人士能理解的语言描述，例：“独家专利算法，精准度超行业标准2倍”\",\"staticContent\":\"\",\"hidden\":false},{\"label\":\"关键数据\",\"name\":\"field_810378\",\"type\":\"input\",\"required\":true,\"options\":\"\",\"placeholder\":\"订单/合作/技术指标，例：“已获3家上市公司订单，2024营收突破2000万”\",\"staticContent\":\"\",\"hidden\":false},{\"label\":\"重要背书\",\"name\":\"field_901660\",\"type\":\"input\",\"required\":true,\"options\":\"\",\"placeholder\":\"奖项/专家评价/头部客户，例：“入选国家XX计划，获院士团队技术认证”\",\"staticContent\":\"\",\"hidden\":false}],\"icon\":\"http://************:8080/profile/upload/2025/07/22/20250722-100244_20250722100451A008.png\"},{\"name\":\"其他材料补充\",\"description\":\"\",\"fields\":[{\"label\":\"上传附件\",\"name\":\"field_989222\",\"type\":\"file\",\"required\":false,\"options\":\"\",\"placeholder\":\"未选择任何文件\",\"staticContent\":\"\",\"hidden\":false},{\"label\":\"邮件提交至\",\"name\":\"field_227969\",\"type\":\"static\",\"required\":false,\"options\":\"\",\"placeholder\":\"请输入\",\"staticContent\":\"<EMAIL>(文件名：【企业曝光申请】-企业/项目名）\",\"hidden\":false}],\"icon\":\"http://************:8080/profile/upload/2025/07/22/20250722-100247_20250722100459A009.png\"}]');
INSERT INTO `mini_demand_category` VALUES (6, '载体厂房', 'factory', '载体', 'http://************:8086/profile/upload/2025/07/28/img_v3_02ok_f3e3b87e-3b81-47b9-afa5-58397a2f19cg_20250728110603A017.png', '<p>房贷发放大师傅但是</p>', 5, '0', '', '2025-07-07 14:55:25', '', NULL, '厂房载体', '[{\"name\":\"基础信息\",\"description\":\"\",\"fields\":[{\"label\":\"企业全称\",\"name\":\"field_652408\",\"type\":\"input\",\"required\":true,\"options\":\"\",\"placeholder\":\"请输入\",\"staticContent\":\"\",\"hidden\":false},{\"label\":\"行业标签\",\"name\":\"field_720944\",\"type\":\"select\",\"required\":true,\"options\":\"新能源,硬科技\",\"placeholder\":\"请选择\",\"staticContent\":\"\",\"hidden\":false},{\"label\":\"所在地区\",\"name\":\"field_756673\",\"type\":\"select\",\"required\":true,\"options\":\"西青,江西\",\"placeholder\":\"请选择\",\"staticContent\":\"\",\"hidden\":false},{\"label\":\"联系人\",\"name\":\"contact_name\",\"type\":\"input\",\"required\":true,\"options\":\"\",\"placeholder\":\"请输入\",\"staticContent\":\"\",\"hidden\":false},{\"label\":\"职位\",\"name\":\"position\",\"type\":\"input\",\"required\":true,\"options\":\"\",\"placeholder\":\"请输入\",\"staticContent\":\"\",\"hidden\":false},{\"label\":\"电话\",\"name\":\"phone\",\"type\":\"tel\",\"required\":true,\"options\":\"\",\"placeholder\":\"请输入\",\"staticContent\":\"\",\"hidden\":false},{\"label\":\"官网\",\"name\":\"field_840720\",\"type\":\"input\",\"required\":false,\"options\":\"\",\"placeholder\":\"请输入\",\"staticContent\":\"\",\"hidden\":false},{\"label\":\"公众号/视频号\",\"name\":\"field_866127\",\"type\":\"input\",\"required\":false,\"options\":\"\",\"placeholder\":\"请输入\",\"staticContent\":\"\",\"hidden\":false}],\"icon\":\"http://************:8080/profile/upload/2025/07/22/20250722-100228_20250722100403A004.png\"},{\"name\":\"传播诉求（可多选）\",\"description\":\"\",\"fields\":[{\"label\":\"需求类型（可多选）\",\"name\":\"field_904177\",\"type\":\"checkbox\",\"required\":true,\"options\":\"投资机构,产业链合作伙伴,终端客户,行业专家,高水平人才\",\"placeholder\":\"请输入\",\"staticContent\":\"\",\"hidden\":false},{\"label\":\"当前传播短板（可多选）\",\"name\":\"field_040280\",\"type\":\"checkbox\",\"required\":true,\"options\":\"技术价值说不清,行业认知度低,缺乏发声通道,品牌故事薄弱\",\"placeholder\":\"请输入\",\"staticContent\":\"\",\"hidden\":false},{\"label\":\"希望强化的内容方向（可多选）\",\"name\":\"field_149229\",\"type\":\"checkbox\",\"required\":true,\"options\":\"技术解析（专利/原理）,应用场景案例,创始人故事,行业趋势洞察,融资/合作需求\",\"placeholder\":\"请输入\",\"staticContent\":\"\",\"hidden\":false}],\"icon\":\"http://************:8080/profile/upload/2025/07/22/20250722-100236_20250722100422A005.png\"},{\"name\":\"资源需求（可多选）\",\"description\":\"\",\"fields\":[{\"label\":\"资源需求（可多选）\",\"name\":\"field_321414\",\"type\":\"checkbox\",\"required\":true,\"options\":\"融资对接,产业链合作,媒体曝光,行业论坛发声,技术专家背书,人才招募支持,其他\",\"placeholder\":\"请输入\",\"staticContent\":\"\",\"hidden\":false}],\"icon\":\"http://************:8080/profile/upload/2025/07/22/20250722-100240_20250722100431A006.png\"},{\"name\":\"企业亮点\",\"description\":\"\",\"fields\":[{\"label\":\"所突破的技术壁垒\",\"name\":\"field_425137\",\"type\":\"input\",\"required\":true,\"options\":\"\",\"placeholder\":\"请用非专业人士能理解的语言描述，例：“全球首个实现XX材料低温合成技术，成本降低50%”\",\"staticContent\":\"\",\"hidden\":false},{\"label\":\"所解决的行业核心痛点\",\"name\":\"field_566277\",\"type\":\"input\",\"required\":true,\"options\":\"\",\"placeholder\":\"请输入，例：“解决新能源电池低温续航衰减30%的难题”\",\"staticContent\":\"\",\"hidden\":false},{\"label\":\"差异化优势\",\"name\":\"field_707577\",\"type\":\"input\",\"required\":true,\"options\":\"\",\"placeholder\":\"请用非专业人士能理解的语言描述，例：“独家专利算法，精准度超行业标准2倍”\",\"staticContent\":\"\",\"hidden\":false},{\"label\":\"关键数据\",\"name\":\"field_810378\",\"type\":\"input\",\"required\":true,\"options\":\"\",\"placeholder\":\"订单/合作/技术指标，例：“已获3家上市公司订单，2024营收突破2000万”\",\"staticContent\":\"\",\"hidden\":false},{\"label\":\"重要背书\",\"name\":\"field_901660\",\"type\":\"input\",\"required\":true,\"options\":\"\",\"placeholder\":\"奖项/专家评价/头部客户，例：“入选国家XX计划，获院士团队技术认证”\",\"staticContent\":\"\",\"hidden\":false}],\"icon\":\"http://************:8080/profile/upload/2025/07/22/20250722-100244_20250722100451A008.png\"},{\"name\":\"其他材料补充\",\"description\":\"\",\"fields\":[{\"label\":\"上传附件\",\"name\":\"field_989222\",\"type\":\"file\",\"required\":false,\"options\":\"\",\"placeholder\":\"未选择任何文件\",\"staticContent\":\"\",\"hidden\":false},{\"label\":\"邮件提交至\",\"name\":\"field_227969\",\"type\":\"static\",\"required\":false,\"options\":\"\",\"placeholder\":\"请输入\",\"staticContent\":\"<EMAIL>(文件名：【企业曝光申请】-企业/项目名）\",\"hidden\":false}],\"icon\":\"http://************:8080/profile/upload/2025/07/22/20250722-100247_20250722100459A009.png\"}]');
INSERT INTO `mini_demand_category` VALUES (7, '品牌曝光', 'exposure', '曝光', 'http://************:8086/profile/upload/2025/07/28/img_v3_02ok_54b8d790-831d-44a3-922e-c200682004bg_20250728110610A018.png', '<p><img src=\"http://************:8080/profile/upload/2025/07/21/e8be1f22f976b30bf9391590fb70610_20250721150115A002.jpg\"></p>', 4, '0', '', '2025-07-07 14:55:29', '', NULL, NULL, '[{\"name\":\"基础信息\",\"description\":\"\",\"fields\":[{\"label\":\"企业全称\",\"name\":\"field_652408\",\"type\":\"input\",\"required\":true,\"options\":\"\",\"placeholder\":\"请输入\",\"staticContent\":\"\"},{\"label\":\"行业标签\",\"name\":\"field_720944\",\"type\":\"select\",\"required\":true,\"options\":\"新能源,硬科技\",\"placeholder\":\"请选择\",\"staticContent\":\"\"},{\"label\":\"所在地区\",\"name\":\"field_756673\",\"type\":\"select\",\"required\":true,\"options\":\"西青,江西\",\"placeholder\":\"请选择\",\"staticContent\":\"\"},{\"label\":\"联系人\",\"name\":\"contact_name\",\"type\":\"input\",\"required\":true,\"options\":\"\",\"placeholder\":\"请输入\",\"staticContent\":\"\"},{\"label\":\"职位\",\"name\":\"position\",\"type\":\"input\",\"required\":true,\"options\":\"\",\"placeholder\":\"请输入\",\"staticContent\":\"\"},{\"label\":\"电话\",\"name\":\"phone\",\"type\":\"tel\",\"required\":true,\"options\":\"\",\"placeholder\":\"请输入\",\"staticContent\":\"\"},{\"label\":\"官网\",\"name\":\"field_840720\",\"type\":\"input\",\"required\":false,\"options\":\"\",\"placeholder\":\"请输入\",\"staticContent\":\"\"},{\"label\":\"公众号/视频号\",\"name\":\"field_866127\",\"type\":\"input\",\"required\":false,\"options\":\"\",\"placeholder\":\"请输入\",\"staticContent\":\"\"}],\"icon\":\"http://************:8080/profile/upload/2025/07/22/20250722-100228_20250722100403A004.png\"},{\"name\":\"传播诉求（可多选）\",\"description\":\"\",\"fields\":[{\"label\":\"需求类型（可多选）\",\"name\":\"field_904177\",\"type\":\"checkbox\",\"required\":true,\"options\":\"投资机构,产业链合作伙伴,终端客户,行业专家,高水平人才\",\"placeholder\":\"请输入\",\"staticContent\":\"\"},{\"label\":\"当前传播短板（可多选）\",\"name\":\"field_040280\",\"type\":\"checkbox\",\"required\":true,\"options\":\"技术价值说不清,行业认知度低,缺乏发声通道,品牌故事薄弱\",\"placeholder\":\"请输入\",\"staticContent\":\"\"},{\"label\":\"希望强化的内容方向（可多选）\",\"name\":\"field_149229\",\"type\":\"checkbox\",\"required\":true,\"options\":\"技术解析（专利/原理）,应用场景案例,创始人故事,行业趋势洞察,融资/合作需求\",\"placeholder\":\"请输入\",\"staticContent\":\"\"}],\"icon\":\"http://************:8080/profile/upload/2025/07/22/20250722-100236_20250722100422A005.png\"},{\"name\":\"资源需求（可多选）\",\"description\":\"\",\"fields\":[{\"label\":\"资源需求（可多选）\",\"name\":\"field_321414\",\"type\":\"checkbox\",\"required\":true,\"options\":\"融资对接,产业链合作,媒体曝光,行业论坛发声,技术专家背书,人才招募支持,其他\",\"placeholder\":\"请输入\",\"staticContent\":\"\"}],\"icon\":\"http://************:8080/profile/upload/2025/07/22/20250722-100240_20250722100431A006.png\"},{\"name\":\"企业亮点\",\"description\":\"\",\"fields\":[{\"label\":\"所突破的技术壁垒\",\"name\":\"field_425137\",\"type\":\"input\",\"required\":true,\"options\":\"\",\"placeholder\":\"请用非专业人士能理解的语言描述，例：“全球首个实现XX材料低温合成技术，成本降低50%”\",\"staticContent\":\"\"},{\"label\":\"所解决的行业核心痛点\",\"name\":\"field_566277\",\"type\":\"input\",\"required\":true,\"options\":\"\",\"placeholder\":\"请输入，例：“解决新能源电池低温续航衰减30%的难题”\",\"staticContent\":\"\"},{\"label\":\"差异化优势\",\"name\":\"field_707577\",\"type\":\"input\",\"required\":true,\"options\":\"\",\"placeholder\":\"请用非专业人士能理解的语言描述，例：“独家专利算法，精准度超行业标准2倍”\",\"staticContent\":\"\"},{\"label\":\"关键数据\",\"name\":\"field_810378\",\"type\":\"input\",\"required\":true,\"options\":\"\",\"placeholder\":\"订单/合作/技术指标，例：“已获3家上市公司订单，2024营收突破2000万”\",\"staticContent\":\"\"},{\"label\":\"重要背书\",\"name\":\"field_901660\",\"type\":\"input\",\"required\":true,\"options\":\"\",\"placeholder\":\"奖项/专家评价/头部客户，例：“入选国家XX计划，获院士团队技术认证”\",\"staticContent\":\"\"}],\"icon\":\"http://************:8080/profile/upload/2025/07/22/20250722-100244_20250722100451A008.png\"},{\"name\":\"其他材料补充\",\"description\":\"\",\"fields\":[{\"label\":\"上传附件\",\"name\":\"field_989222\",\"type\":\"file\",\"required\":false,\"options\":\"\",\"placeholder\":\"未选择任何文件\",\"staticContent\":\"\"},{\"label\":\"邮件提交至\",\"name\":\"field_227969\",\"type\":\"static\",\"required\":false,\"options\":\"\",\"placeholder\":\"请输入\",\"staticContent\":\"<EMAIL>(文件名：【企业曝光申请】-企业/项目名）\"}],\"icon\":\"http://************:8080/profile/upload/2025/07/22/20250722-100247_20250722100459A009.png\"}]');
INSERT INTO `mini_demand_category` VALUES (8, '管理咨询', 'consult', '管理', 'http://************:8086/profile/upload/2025/07/28/img_v3_02ok_3ee0dc97-f297-4a7f-a576-2f3ad48e444g_20250728110616A019.png', '<p>大师傅士大夫士大夫士大夫</p>', 7, '0', '', '2025-07-07 14:55:32', '', NULL, NULL, '[{\"name\":\"基础信息\",\"description\":\"\",\"fields\":[{\"label\":\"企业全称\",\"name\":\"field_652408\",\"type\":\"input\",\"required\":true,\"options\":\"\",\"placeholder\":\"请输入\",\"staticContent\":\"\",\"hidden\":false},{\"label\":\"行业标签\",\"name\":\"field_720944\",\"type\":\"select\",\"required\":true,\"options\":\"新能源,硬科技\",\"placeholder\":\"请选择\",\"staticContent\":\"\",\"hidden\":false},{\"label\":\"所在地区\",\"name\":\"field_756673\",\"type\":\"select\",\"required\":true,\"options\":\"西青,江西\",\"placeholder\":\"请选择\",\"staticContent\":\"\",\"hidden\":false},{\"label\":\"联系人\",\"name\":\"contact_name\",\"type\":\"input\",\"required\":true,\"options\":\"\",\"placeholder\":\"请输入\",\"staticContent\":\"\",\"hidden\":false},{\"label\":\"职位\",\"name\":\"position\",\"type\":\"input\",\"required\":true,\"options\":\"\",\"placeholder\":\"请输入\",\"staticContent\":\"\",\"hidden\":false},{\"label\":\"电话\",\"name\":\"phone\",\"type\":\"tel\",\"required\":true,\"options\":\"\",\"placeholder\":\"请输入\",\"staticContent\":\"\",\"hidden\":false},{\"label\":\"官网\",\"name\":\"field_840720\",\"type\":\"input\",\"required\":false,\"options\":\"\",\"placeholder\":\"请输入\",\"staticContent\":\"\",\"hidden\":false},{\"label\":\"公众号/视频号\",\"name\":\"field_866127\",\"type\":\"input\",\"required\":false,\"options\":\"\",\"placeholder\":\"请输入\",\"staticContent\":\"\",\"hidden\":false}],\"icon\":\"http://************:8080/profile/upload/2025/07/22/20250722-100228_20250722100403A004.png\"},{\"name\":\"传播诉求（可多选）\",\"description\":\"\",\"fields\":[{\"label\":\"需求类型（可多选）\",\"name\":\"field_904177\",\"type\":\"checkbox\",\"required\":true,\"options\":\"投资机构,产业链合作伙伴,终端客户,行业专家,高水平人才\",\"placeholder\":\"请输入\",\"staticContent\":\"\",\"hidden\":false},{\"label\":\"当前传播短板（可多选）\",\"name\":\"field_040280\",\"type\":\"checkbox\",\"required\":true,\"options\":\"技术价值说不清,行业认知度低,缺乏发声通道,品牌故事薄弱\",\"placeholder\":\"请输入\",\"staticContent\":\"\",\"hidden\":false},{\"label\":\"希望强化的内容方向（可多选）\",\"name\":\"field_149229\",\"type\":\"checkbox\",\"required\":true,\"options\":\"技术解析（专利/原理）,应用场景案例,创始人故事,行业趋势洞察,融资/合作需求\",\"placeholder\":\"请输入\",\"staticContent\":\"\",\"hidden\":false}],\"icon\":\"http://************:8080/profile/upload/2025/07/22/20250722-100236_20250722100422A005.png\"},{\"name\":\"资源需求（可多选）\",\"description\":\"\",\"fields\":[{\"label\":\"资源需求（可多选）\",\"name\":\"field_321414\",\"type\":\"checkbox\",\"required\":true,\"options\":\"融资对接,产业链合作,媒体曝光,行业论坛发声,技术专家背书,人才招募支持,其他\",\"placeholder\":\"请输入\",\"staticContent\":\"\",\"hidden\":false}],\"icon\":\"http://************:8080/profile/upload/2025/07/22/20250722-100240_20250722100431A006.png\"},{\"name\":\"企业亮点\",\"description\":\"\",\"fields\":[{\"label\":\"所突破的技术壁垒\",\"name\":\"field_425137\",\"type\":\"input\",\"required\":true,\"options\":\"\",\"placeholder\":\"请用非专业人士能理解的语言描述，例：“全球首个实现XX材料低温合成技术，成本降低50%”\",\"staticContent\":\"\",\"hidden\":false},{\"label\":\"所解决的行业核心痛点\",\"name\":\"field_566277\",\"type\":\"input\",\"required\":true,\"options\":\"\",\"placeholder\":\"请输入，例：“解决新能源电池低温续航衰减30%的难题”\",\"staticContent\":\"\",\"hidden\":false},{\"label\":\"差异化优势\",\"name\":\"field_707577\",\"type\":\"input\",\"required\":true,\"options\":\"\",\"placeholder\":\"请用非专业人士能理解的语言描述，例：“独家专利算法，精准度超行业标准2倍”\",\"staticContent\":\"\",\"hidden\":false},{\"label\":\"关键数据\",\"name\":\"field_810378\",\"type\":\"input\",\"required\":true,\"options\":\"\",\"placeholder\":\"订单/合作/技术指标，例：“已获3家上市公司订单，2024营收突破2000万”\",\"staticContent\":\"\",\"hidden\":false},{\"label\":\"重要背书\",\"name\":\"field_901660\",\"type\":\"input\",\"required\":true,\"options\":\"\",\"placeholder\":\"奖项/专家评价/头部客户，例：“入选国家XX计划，获院士团队技术认证”\",\"staticContent\":\"\",\"hidden\":false}],\"icon\":\"http://************:8080/profile/upload/2025/07/22/20250722-100244_20250722100451A008.png\"},{\"name\":\"其他材料补充\",\"description\":\"\",\"fields\":[{\"label\":\"上传附件\",\"name\":\"field_989222\",\"type\":\"file\",\"required\":false,\"options\":\"\",\"placeholder\":\"未选择任何文件\",\"staticContent\":\"\",\"hidden\":false},{\"label\":\"邮件提交至\",\"name\":\"field_227969\",\"type\":\"static\",\"required\":false,\"options\":\"\",\"placeholder\":\"请输入\",\"staticContent\":\"<EMAIL>(文件名：【企业曝光申请】-企业/项目名）\",\"hidden\":false}],\"icon\":\"http://************:8080/profile/upload/2025/07/22/20250722-100247_20250722100459A009.png\"}]');
INSERT INTO `mini_demand_category` VALUES (9, '技术合作', 'tech', '技术', 'http://************:8086/profile/upload/2025/07/28/img_v3_02ok_5da964dd-82d3-4e84-be35-52e11fff321g_20250728110534A013.png', '<p>测试</p>', 2, '0', '', NULL, '', NULL, '成果转化', '[{\"name\":\"填写说明\",\"description\":\"请仔细阅读以下说明后填写表单\",\"fields\":[{\"label\":\"温馨提示\",\"name\":\"field_055723\",\"type\":\"static\",\"required\":false,\"hidden\":false,\"options\":\"\",\"placeholder\":\"\",\"staticContent\":\"请如实填写以下信息，我们将在24小时内与您取得联系。带*号的为必填项。\"}],\"icon\":\"http://************:8086/profile/upload/2025/07/28/20250722-100228_20250728114101A022.png\"},{\"name\":\"基础信息\",\"description\":\"请填写需求的基本信息\",\"fields\":[{\"label\":\"需求标题\",\"name\":\"field_055723\",\"type\":\"input\",\"required\":true,\"hidden\":false,\"options\":\"\",\"placeholder\":\"请输入需求标题\",\"staticContent\":\"\"},{\"label\":\"需求描述\",\"name\":\"description\",\"type\":\"textarea\",\"required\":true,\"hidden\":false,\"options\":\"\",\"placeholder\":\"请详细描述您的需求\",\"staticContent\":\"\"}],\"icon\":\"http://************:8086/profile/upload/2025/07/28/20250722-100244_20250728114107A023.png\"},{\"name\":\"联系方式\",\"description\":\"请填写您的联系方式，以便我们与您取得联系\",\"fields\":[{\"label\":\"联系人\",\"name\":\"contact_name\",\"type\":\"input\",\"required\":true,\"hidden\":false,\"options\":\"\",\"placeholder\":\"请输入联系人姓名\",\"staticContent\":\"\"},{\"label\":\"联系电话\",\"name\":\"phone\",\"type\":\"tel\",\"required\":true,\"hidden\":false,\"options\":\"\",\"placeholder\":\"请输入手机号码\",\"staticContent\":\"\"}],\"icon\":\"http://************:8086/profile/upload/2025/07/28/20250722-100228_20250728114116A024.png\"}]');
INSERT INTO `mini_demand_category` VALUES (10, '其他需求', 'other', '其他', 'http://************:8086/profile/upload/2025/07/30/img_v3_02ok_3ee0dc97-f297-4a7f-a576-2f3ad48e444g_20250730093231A005.png', '<p>非得发十分士大夫大师傅</p>', 8, '0', '', NULL, '', NULL, NULL, '[{\"name\":\"提示\",\"description\":\"请仔细阅读以下说明后填写表单\",\"fields\":[{\"label\":\"温馨提示\",\"name\":\"field_195938\",\"type\":\"static\",\"required\":false,\"hidden\":false,\"options\":\"\",\"placeholder\":\"\",\"staticContent\":\"请如实填写以下信息，我们将在24小时内与您取得联系。带*号的为必填项。\"}],\"icon\":\"http://************:8086/profile/upload/2025/07/30/img_v3_02ok_3ee0dc97-f297-4a7f-a576-2f3ad48e444g_20250730093323A006.png\"}]');

-- ----------------------------
-- Table structure for mini_demand_docking
-- ----------------------------
DROP TABLE IF EXISTS `mini_demand_docking`;
CREATE TABLE `mini_demand_docking`  (
  `docking_id` bigint NOT NULL AUTO_INCREMENT COMMENT '对接ID',
  `demand_id` bigint NOT NULL COMMENT '需求ID',
  `user_id` bigint NOT NULL COMMENT '对接用户ID（揭榜人）',
  `user_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '对接用户姓名',
  `user_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '对接用户手机号',
  `user_company` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '对接用户公司',
  `user_position` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '对接用户职位',
  `docking_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '对接时间',
  `contact_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '获取到的联系人姓名',
  `contact_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '获取到的联系人手机号',
  `contact_source` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '0' COMMENT '联系方式来源（0后台配置 1需求发布人）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '0' COMMENT '状态（0正常 1已取消）',
  `is_contacted` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '0' COMMENT '是否已联系（0未联系 1已联系）',
  `contact_result` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '联系结果（成功、失败、无回应等）',
  `contact_notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '联系备注',
  `contact_time` datetime NULL DEFAULT NULL COMMENT '联系时间',
  `qr_code_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '联系二维码URL',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '备注',
  PRIMARY KEY (`docking_id`) USING BTREE,
  UNIQUE INDEX `uk_demand_user`(`demand_id` ASC, `user_id` ASC) USING BTREE,
  INDEX `idx_demand_id`(`demand_id` ASC) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_docking_time`(`docking_time` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '需求对接表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of mini_demand_docking
-- ----------------------------

-- ----------------------------
-- Table structure for mini_enterprise
-- ----------------------------
DROP TABLE IF EXISTS `mini_enterprise`;
CREATE TABLE `mini_enterprise`  (
  `enterprise_id` bigint NOT NULL AUTO_INCREMENT COMMENT '企业ID',
  `enterprise_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '企业名称',
  `legal_person` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '法人',
  `address` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '企业地址',
  `contact_person` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系人',
  `contact_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系电话',
  `contact_email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系邮箱',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '企业简介',
  `scale` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '企业规模(small/medium/large)',
  `founding_date` date NULL DEFAULT NULL COMMENT '成立日期',
  `website` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '官网地址',
  `logo_url` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '企业logo',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`enterprise_id`) USING BTREE,
  INDEX `idx_enterprise_name`(`enterprise_name` ASC) USING BTREE,
  INDEX `idx_enterprise_status`(`status` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '企业表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of mini_enterprise
-- ----------------------------

-- ----------------------------
-- Table structure for mini_enterprise_industry
-- ----------------------------
DROP TABLE IF EXISTS `mini_enterprise_industry`;
CREATE TABLE `mini_enterprise_industry`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `enterprise_id` bigint NOT NULL COMMENT '企业ID',
  `industry_tree_id` bigint NOT NULL COMMENT '产业树节点ID',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_enterprise_tree`(`enterprise_id` ASC, `industry_tree_id` ASC) USING BTREE,
  INDEX `idx_enterprise_id`(`enterprise_id` ASC) USING BTREE,
  INDEX `idx_industry_tree_id`(`industry_tree_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 121 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci COMMENT = '企业产业关联表（新）' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of mini_enterprise_industry
-- ----------------------------

-- ----------------------------
-- Table structure for mini_event
-- ----------------------------
DROP TABLE IF EXISTS `mini_event`;
CREATE TABLE `mini_event`  (
  `event_id` bigint NOT NULL AUTO_INCREMENT COMMENT '活动ID',
  `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '活动标题',
  `cover_image` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '活动封面图',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL COMMENT '活动描述',
  `location` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '活动地点',
  `start_time` datetime NOT NULL COMMENT '活动开始时间',
  `end_time` datetime NOT NULL COMMENT '活动结束时间',
  `registration_deadline` datetime NULL DEFAULT NULL COMMENT '报名截止时间',
  `form_fields` json NULL COMMENT '报名表单字段配置（JSON格式）',
  `sort_order` int NULL DEFAULT 0 COMMENT '排序（数字越小越靠前）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `event_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT 'activity' COMMENT '活动类型（activity=活动，guidance=项目指导）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`event_id`) USING BTREE,
  INDEX `idx_sort_order`(`sort_order` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_start_time`(`start_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci COMMENT = '活动报名表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of mini_event
-- ----------------------------
INSERT INTO `mini_event` VALUES (1, '创赛8.0即将发布', 'http://************:8080/profile/upload/2025/07/23/6d1ffde61ff84984a225945dd3383da0_20250723085708A001.jpg', '创赛8.0即将发布创赛8.0即将发布创赛8.0即将发布', '方志敏公园', '2025-07-05 00:00:00', '2025-07-25 00:00:00', '2025-07-10 00:00:00', '[{\"name\": \"name\", \"type\": \"input\", \"label\": \"姓名\", \"options\": \"\", \"required\": true}, {\"name\": \"phone\", \"type\": \"tel\", \"label\": \"联系电话\", \"options\": \"\", \"required\": true}, {\"name\": \"email\", \"type\": \"email\", \"label\": \"邮箱地址\", \"options\": \"\", \"required\": false}]', 0, '0', 'activity', '', '2025-07-04 18:11:26', '', '2025-07-23 08:57:10', NULL);
INSERT INTO `mini_event` VALUES (2, 'jojo的奇妙冒险', 'http://************:8080/profile/upload/2025/07/23/7e4afde0d80946dea5898e2dfa559b81_20250723085716A002.jpg', '神父的救赎', '天堂制造', '2025-07-02 00:00:00', '2025-07-17 00:00:00', '2025-07-31 00:00:00', '[{\"name\": \"name\", \"type\": \"input\", \"label\": \"姓名\", \"options\": \"\", \"required\": true}]', 0, '0', 'activity', '', '2025-07-07 18:33:39', '', '2025-07-23 08:57:17', NULL);
INSERT INTO `mini_event` VALUES (3, '项目指导活动测试', 'http://************:8080/profile/upload/2025/07/22/4fe5221d26e6490b89b0974899647f4621_20250722153706A001.jpg', '这是一个项目指导活动测试', '北京市海淀区', '2024-02-01 10:00:00', '2024-02-01 18:00:00', '2024-01-30 23:59:59', '[{\"name\": \"name\", \"type\": \"input\", \"label\": \"姓名\", \"options\": \"\", \"required\": true}, {\"name\": \"phone\", \"type\": \"tel\", \"label\": \"联系电话\", \"options\": \"\", \"required\": true}, {\"name\": \"email\", \"type\": \"email\", \"label\": \"邮箱地址\", \"options\": \"\", \"required\": false}]', 1, '0', 'guidance', '', '2025-07-22 15:34:21', '', '2025-07-22 15:37:13', NULL);
INSERT INTO `mini_event` VALUES (4, '测试', 'http://************:8080/profile/upload/2025/07/22/066ba870048b40098b3db2b9a9072812_20250722153955A001.jpg', '发生的方法士大夫撒旦发顺丰', '一样2', '2025-07-22 00:00:00', '2025-07-30 00:00:00', '2025-07-25 00:00:00', '[{\"name\": \"name\", \"type\": \"input\", \"label\": \"姓名\", \"options\": \"\", \"required\": true}, {\"name\": \"phone\", \"type\": \"tel\", \"label\": \"联系电话\", \"options\": \"\", \"required\": true}, {\"name\": \"email\", \"type\": \"email\", \"label\": \"邮箱地址\", \"options\": \"\", \"required\": true}, {\"name\": \"company\", \"type\": \"input\", \"label\": \"所在公司\", \"options\": \"\", \"required\": false}, {\"name\": \"tech_direction\", \"type\": \"select\", \"label\": \"技术方向\", \"options\": \"前端开发,后端开发,移动开发,人工智能,数据分析\", \"required\": true}, {\"name\": \"experience\", \"type\": \"radio\", \"label\": \"工作年限\", \"options\": \"1年以内,1-3年,3-5年,5年以上\", \"required\": true}, {\"name\": \"expectations\", \"type\": \"textarea\", \"label\": \"期望收获\", \"options\": \"\", \"required\": false}]', 0, '0', 'guidance', '', '2025-07-22 15:40:09', '', NULL, NULL);

-- ----------------------------
-- Table structure for mini_event_registration
-- ----------------------------
DROP TABLE IF EXISTS `mini_event_registration`;
CREATE TABLE `mini_event_registration`  (
  `registration_id` bigint NOT NULL AUTO_INCREMENT COMMENT '报名ID',
  `event_id` bigint NOT NULL COMMENT '活动ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `form_data` json NULL COMMENT '报名表单数据（JSON格式）',
  `registration_time` datetime NOT NULL COMMENT '报名时间',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`registration_id`) USING BTREE,
  UNIQUE INDEX `uk_event_user`(`event_id` ASC, `user_id` ASC) USING BTREE,
  INDEX `idx_event_id`(`event_id` ASC) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_registration_time`(`registration_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci COMMENT = '用户报名记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of mini_event_registration
-- ----------------------------

-- ----------------------------
-- Table structure for mini_expert_matrix
-- ----------------------------
DROP TABLE IF EXISTS `mini_expert_matrix`;
CREATE TABLE `mini_expert_matrix`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '专家ID',
  `expert_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '专家姓名',
  `expert_title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '专家职位/头衔',
  `expert_company` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '所属公司/机构',
  `expert_intro` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '专家简介',
  `avatar_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '头像URL',
  `expertise_fields` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '专业领域（JSON格式）',
  `years_experience` int NULL DEFAULT 0 COMMENT '从业年限',
  `notable_achievements` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '主要成就',
  `contact_info` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '联系方式（JSON格式）',
  `detailed_description` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '专家详细描述(富文本)',
  `sort_order` int NULL DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) NULL DEFAULT 0 COMMENT '状态：1-启用，0-禁用',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_mini_expert_matrix_status_sort`(`status` ASC, `sort_order` ASC) USING BTREE,
  INDEX `idx_mini_expert_matrix_expert_name`(`expert_name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '专家矩阵表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of mini_expert_matrix
-- ----------------------------
INSERT INTO `mini_expert_matrix` VALUES (1, '狗猫', '宠物', '江西宇悦科技有限公司', '狗和猫', 'http://************:8086/profile/upload/2025/07/29/wallhaven-3k62ov_1920x1080_20250729094642A002.png', '宠物', 5, '无', '12123', '<p><br></p><p><img src=\"/dev-api/profile/upload/2025/07/11/47482bcb3a294e868546d6c73f1e7ca8_20250711171347A002.jpg\"></p>', 0, 0, '2025-07-08 16:32:37', '2025-07-29 01:46:44');
INSERT INTO `mini_expert_matrix` VALUES (2, '陈星宇', NULL, NULL, NULL, 'http://************:8086/profile/upload/2025/07/29/wallhaven-5wrwq9_1920x1080_20250729094711A003.png', NULL, 0, NULL, NULL, '<p><br></p>', 0, 0, '2025-07-29 01:47:13', '2025-07-29 01:47:13');
INSERT INTO `mini_expert_matrix` VALUES (3, '夏宇杰', NULL, NULL, NULL, 'http://************:8086/profile/upload/2025/07/29/wallhaven-6ljo17_20250729094731A004.jpg', NULL, 0, NULL, NULL, NULL, 0, 0, '2025-07-29 01:47:33', '2025-07-29 01:47:33');
INSERT INTO `mini_expert_matrix` VALUES (4, 'zhilin', NULL, NULL, NULL, 'http://************:8086/profile/upload/2025/07/29/af2d069bee6042e8a9e18107ab5eefc521_20250729094819A006.jpg', NULL, 0, NULL, NULL, NULL, 0, 0, '2025-07-29 01:48:21', '2025-07-29 01:48:21');
INSERT INTO `mini_expert_matrix` VALUES (5, 'hdf', NULL, NULL, NULL, 'http://************:8086/profile/upload/2025/07/29/5c23d52f880511ebb6edd017c2d2eca2_20250729094920A007.jpg', NULL, 0, NULL, NULL, NULL, 0, 0, '2025-07-29 01:49:21', '2025-07-29 01:49:21');

-- ----------------------------
-- Table structure for mini_guidance_registration
-- ----------------------------
DROP TABLE IF EXISTS `mini_guidance_registration`;
CREATE TABLE `mini_guidance_registration`  (
  `registration_id` bigint NOT NULL AUTO_INCREMENT COMMENT '报名ID',
  `guidance_id` bigint NOT NULL COMMENT '辅导ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `form_data` json NULL COMMENT '报名表单数据（JSON格式）',
  `registration_time` datetime NOT NULL COMMENT '报名时间',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`registration_id`) USING BTREE,
  UNIQUE INDEX `uk_mini_guidance_user`(`guidance_id` ASC, `user_id` ASC) USING BTREE,
  INDEX `idx_mini_guidance_registration_guidance_id`(`guidance_id` ASC) USING BTREE,
  INDEX `idx_mini_guidance_registration_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_mini_guidance_registration_time`(`registration_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 13 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci COMMENT = '项目辅导报名记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of mini_guidance_registration
-- ----------------------------

-- ----------------------------
-- Table structure for mini_haitang_carousel
-- ----------------------------
DROP TABLE IF EXISTS `mini_haitang_carousel`;
CREATE TABLE `mini_haitang_carousel`  (
  `carousel_id` bigint NOT NULL AUTO_INCREMENT COMMENT '轮播图ID',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '轮播图标题',
  `image_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '图片地址',
  `link_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '跳转链接',
  `sort_order` int NULL DEFAULT 0 COMMENT '排序（数字越小越靠前）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`carousel_id`) USING BTREE,
  INDEX `idx_mini_haitang_carousel_sort_order`(`sort_order` ASC) USING BTREE,
  INDEX `idx_mini_haitang_carousel_status`(`status` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci COMMENT = '海棠杯轮播图表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of mini_haitang_carousel
-- ----------------------------
INSERT INTO `mini_haitang_carousel` VALUES (1, '海棠杯创新创业大赛宣传', 'http://************:8080/profile/upload/2025/07/21/481465568ef442c490780bf8b0151c83_20250721174721A001.jpg', 'https://example.com/haitang/info', 1, '0', 'admin', '2025-07-07 16:12:59', '', '2025-07-21 17:47:25', '海棠杯创新创业大赛主页轮播图');
INSERT INTO `mini_haitang_carousel` VALUES (2, '海棠杯报名通道', '/profile/upload/2025/07/18/78ecf2f1-55e5-49e7-98c6-7db0aad32b0b_20250718091130A002.jpg', 'https://example.com/haitang/register', 2, '1', 'admin', '2025-07-07 16:12:59', '', '2025-07-21 17:47:01', '海棠杯报名入口轮播图');
INSERT INTO `mini_haitang_carousel` VALUES (3, '海棠杯获奖名单', '/profile/upload/2025/07/18/8f0cea6d-e3cb-4b75-8d57-82b1e1a9f4a3_20250718091136A003.png', 'https://example.com/haitang/winners', 3, '1', 'admin', '2025-07-07 16:12:59', '', '2025-07-21 17:47:04', '海棠杯获奖名单展示轮播图');

-- ----------------------------
-- Table structure for mini_industry_tree
-- ----------------------------
DROP TABLE IF EXISTS `mini_industry_tree`;
CREATE TABLE `mini_industry_tree`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `parent_id` bigint NULL DEFAULT 0 COMMENT '父节点ID，0表示根节点',
  `node_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '节点编码（保留原ID）',
  `node_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '节点名称',
  `node_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL COMMENT '节点描述',
  `node_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '节点类型：type/position/segment',
  `node_level` int NOT NULL COMMENT '节点层级：1=产业类型, 2=产业位置, 3=产业细分',
  `stream_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '产业链位置：upstream/midstream/downstream',
  `has_stream_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '0' COMMENT '是否有上中下游(0-否 1-是)',
  `sort_order` int NULL DEFAULT 0 COMMENT '排序',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `deleted` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '0' COMMENT '删除标志(0-正常 1-删除)',
  `node_path` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '节点路径，如：1/2/3',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_parent_id`(`parent_id` ASC) USING BTREE,
  INDEX `idx_node_type`(`node_type` ASC) USING BTREE,
  INDEX `idx_node_level`(`node_level` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_sort_order`(`sort_order` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 114 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci COMMENT = '产业树状结构表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of mini_industry_tree
-- ----------------------------
INSERT INTO `mini_industry_tree` VALUES (1, 0, NULL, '新能源', '', 'type', 1, '', '1', 0, '0', '0', NULL, 'admin', '2025-07-11 15:30:08', 'admin', '2025-07-16 09:43:33', NULL);
INSERT INTO `mini_industry_tree` VALUES (2, 0, NULL, '新材料', '', 'type', 1, '', '1', 0, '0', '0', NULL, 'admin', '2025-07-11 15:30:22', '', '2025-07-11 15:30:21', NULL);
INSERT INTO `mini_industry_tree` VALUES (3, 0, NULL, '医疗大健康', '', 'type', 1, '', '1', 0, '0', '0', NULL, 'admin', '2025-07-11 15:30:36', '', '2025-07-11 15:30:35', NULL);
INSERT INTO `mini_industry_tree` VALUES (4, 0, NULL, '人工智能', '', 'type', 1, '', '1', 0, '0', '0', NULL, 'admin', '2025-07-11 15:30:44', '', '2025-07-11 15:30:43', NULL);
INSERT INTO `mini_industry_tree` VALUES (5, 0, NULL, '生物制造', '', 'type', 1, '', '1', 0, '0', '0', NULL, 'admin', '2025-07-11 15:30:54', '', '2025-07-11 15:30:54', NULL);
INSERT INTO `mini_industry_tree` VALUES (6, 0, NULL, '硬科技', '', 'type', 1, '', '0', 0, '0', '0', NULL, 'admin', '2025-07-11 15:31:02', '', '2025-07-11 15:31:02', NULL);
INSERT INTO `mini_industry_tree` VALUES (7, 0, NULL, '服务/金融行业', '', 'type', 1, '', '0', 0, '0', '0', NULL, 'admin', '2025-07-11 15:31:10', '', '2025-07-11 15:31:10', NULL);
INSERT INTO `mini_industry_tree` VALUES (8, 1, NULL, '矿产资源开发', '', 'position', 2, 'upstream', '0', 0, '0', '0', NULL, 'admin', '2025-07-11 15:31:23', '', '2025-07-16 09:43:32', NULL);
INSERT INTO `mini_industry_tree` VALUES (9, 1, NULL, '基础材料制备', '', 'position', 2, 'upstream', '0', 0, '0', '0', NULL, 'admin', '2025-07-11 15:31:31', '', '2025-07-16 09:43:32', NULL);
INSERT INTO `mini_industry_tree` VALUES (10, 8, NULL, '金属矿产', '锂、钴、镍等动力电池关键原料的开采（如赣锋锂业、天齐锂业），以及稀土永磁材料（如北方稀土）的加工，支撑锂电池和风电设备制造', 'segment', 3, '', '0', 0, '0', '0', NULL, 'admin', '2025-07-11 15:32:00', '', '2025-07-16 09:43:32', NULL);
INSERT INTO `mini_industry_tree` VALUES (11, 8, NULL, ' 非金属矿产', '硅、多晶硅的生产（如通威股份、大全能源），是光伏产业的基石', 'segment', 3, '', '0', 0, '0', '0', NULL, 'admin', '2025-07-11 15:32:22', '', '2025-07-16 09:43:32', NULL);
INSERT INTO `mini_industry_tree` VALUES (12, 8, NULL, ' 氢能原料', '电解水制氢所需的纯水、化石燃料重整制氢的天然气 / 煤炭，以及工业副产氢（如焦炉煤气、氯碱尾气）的回收利用', 'segment', 3, '', '0', 0, '0', '0', NULL, 'admin', '2025-07-11 15:32:35', '', '2025-07-16 09:43:32', NULL);
INSERT INTO `mini_industry_tree` VALUES (13, 9, NULL, '光伏材料', '三氯氢硅（三孚股份）、工业硅（合盛硅业）等用于多晶硅生产，单晶硅棒 / 片（隆基绿能、中环股份）的加工', 'segment', 3, '', '0', 0, '0', '0', NULL, 'admin', '2025-07-11 15:32:57', '', '2025-07-16 09:43:32', NULL);
INSERT INTO `mini_industry_tree` VALUES (14, 9, NULL, ' 电池材料', '正极材料（如容百科技的三元材料）、负极材料（如贝特瑞的石墨负极）、电解液（天赐材料）、隔膜等锂电池核心材料，以及钠离子电池的钠盐、硬碳负极（中科海钠）', 'segment', 3, '', '0', 0, '0', '0', NULL, 'admin', '2025-07-11 15:33:25', '', '2025-07-16 09:43:32', NULL);
INSERT INTO `mini_industry_tree` VALUES (15, 9, NULL, ' 氢能材料', '储氢合金（如安泰科技）、碳纤维（中复神鹰）用于高压储氢罐，质子交换膜（东岳集团）用于燃料电池', 'segment', 3, '', '0', 0, '0', '0', NULL, 'admin', '2025-07-11 15:33:37', '', '2025-07-16 09:43:32', NULL);
INSERT INTO `mini_industry_tree` VALUES (16, 1, NULL, '新能源发电设备', '', 'position', 2, 'midstream', '0', 0, '0', '0', NULL, 'admin', '2025-07-11 15:36:51', '', '2025-07-16 09:43:32', NULL);
INSERT INTO `mini_industry_tree` VALUES (17, 1, NULL, '储能与电力系统', '', 'position', 2, 'midstream', '0', 0, '0', '0', NULL, 'admin', '2025-07-11 15:37:03', '', '2025-07-16 09:43:32', NULL);
INSERT INTO `mini_industry_tree` VALUES (18, 1, NULL, '氢能产业链', '', 'position', 2, 'midstream', '0', 0, '0', '0', NULL, 'admin', '2025-07-11 15:37:18', '', '2025-07-16 09:43:32', NULL);
INSERT INTO `mini_industry_tree` VALUES (19, 1, NULL, '发电与电网', '', 'position', 2, 'downstream', '0', 0, '0', '0', NULL, 'admin', '2025-07-11 15:37:30', '', '2025-07-16 09:43:32', NULL);
INSERT INTO `mini_industry_tree` VALUES (20, 1, NULL, '交通运输', '', 'position', 2, 'downstream', '0', 0, '0', '0', NULL, 'admin', '2025-07-11 15:37:44', '', '2025-07-16 09:43:32', NULL);
INSERT INTO `mini_industry_tree` VALUES (21, 1, NULL, '工业与民用领域', '', 'position', 2, 'downstream', '0', 0, '0', '0', NULL, 'admin', '2025-07-11 15:37:53', '', '2025-07-16 09:43:32', NULL);
INSERT INTO `mini_industry_tree` VALUES (22, 1, NULL, '电力交易', '', 'position', 2, 'downstream', '0', 0, '0', '0', NULL, 'admin', '2025-07-11 15:38:05', '', '2025-07-16 09:43:32', NULL);
INSERT INTO `mini_industry_tree` VALUES (23, 2, NULL, '矿产资源与基础化工原料', '', 'position', 2, 'upstream', '0', 0, '0', '0', NULL, 'admin', '2025-07-11 15:38:21', '', '2025-07-11 15:38:21', NULL);
INSERT INTO `mini_industry_tree` VALUES (24, 2, NULL, '特种原材料制备', '', 'position', 2, 'upstream', '0', 0, '0', '0', NULL, 'admin', '2025-07-11 15:38:39', '', '2025-07-11 15:38:38', NULL);
INSERT INTO `mini_industry_tree` VALUES (25, 2, NULL, '核心设备与技术', '', 'position', 2, 'upstream', '0', 0, '0', '0', NULL, 'admin', '2025-07-11 15:39:00', '', '2025-07-11 15:38:59', NULL);
INSERT INTO `mini_industry_tree` VALUES (26, 6, NULL, '半导体', '', 'position', 2, '', '0', 0, '0', '0', NULL, 'admin', '2025-07-11 15:40:22', '', '2025-07-11 15:40:21', NULL);
INSERT INTO `mini_industry_tree` VALUES (27, 6, NULL, '量子领域', '', 'position', 2, '', '0', 0, '0', '0', NULL, 'admin', '2025-07-11 15:40:30', '', '2025-07-11 15:40:30', NULL);
INSERT INTO `mini_industry_tree` VALUES (28, 6, NULL, '高端装备制造', '', 'position', 2, '', '0', 0, '0', '0', NULL, 'admin', '2025-07-11 15:40:38', '', '2025-07-11 15:40:38', NULL);
INSERT INTO `mini_industry_tree` VALUES (30, 6, NULL, '航空航天', '', 'position', 2, '', '0', 0, '0', '0', NULL, 'admin', '2025-07-11 15:41:06', '', '2025-07-11 15:41:05', NULL);
INSERT INTO `mini_industry_tree` VALUES (31, 6, NULL, '传感', '', 'position', 2, '', '0', 0, '0', '0', NULL, 'admin', '2025-07-11 15:41:14', '', '2025-07-11 15:41:13', NULL);
INSERT INTO `mini_industry_tree` VALUES (32, 2, NULL, '功能材料制造', '包括电子材料、光电材料、磁性材料、超导材料等功能性新材料制造', 'position', 2, 'midstream', '0', 4, '0', '0', NULL, 'admin', '2025-07-11 15:50:32', '', '2025-07-11 15:50:32', NULL);
INSERT INTO `mini_industry_tree` VALUES (33, 2, NULL, '结构材料制造', '包括高强度钢、钛合金、碳纤维、陶瓷材料等结构性新材料制造', 'position', 2, 'midstream', '0', 5, '0', '0', NULL, 'admin', '2025-07-11 15:50:32', '', '2025-07-11 15:50:32', NULL);
INSERT INTO `mini_industry_tree` VALUES (34, 2, NULL, '复合材料制造', '包括纤维增强复合材料、金属基复合材料、陶瓷基复合材料等', 'position', 2, 'midstream', '0', 6, '0', '0', NULL, 'admin', '2025-07-11 15:50:32', '', '2025-07-11 15:50:32', NULL);
INSERT INTO `mini_industry_tree` VALUES (35, 2, NULL, '材料应用集成', '包括航空航天、电子信息、新能源、生物医药等领域的材料应用', 'position', 2, 'downstream', '0', 7, '0', '0', NULL, 'admin', '2025-07-11 15:50:32', '', '2025-07-11 15:50:32', NULL);
INSERT INTO `mini_industry_tree` VALUES (36, 2, NULL, '材料检测与服务', '包括材料性能测试、质量检验、标准认证、技术咨询等服务', 'position', 2, 'downstream', '0', 8, '0', '0', NULL, 'admin', '2025-07-11 15:50:32', '', '2025-07-11 15:50:32', NULL);
INSERT INTO `mini_industry_tree` VALUES (37, 3, NULL, '原料药与中间体', '包括化学原料药、生物原料药、药用辅料、中间体等基础原料', 'position', 2, 'upstream', '0', 1, '0', '0', NULL, 'admin', '2025-07-11 15:50:47', '', '2025-07-11 15:50:47', NULL);
INSERT INTO `mini_industry_tree` VALUES (38, 3, NULL, '医药研发服务', '包括药物发现、临床前研究、临床试验、药物注册等研发外包服务', 'position', 2, 'upstream', '0', 2, '0', '0', NULL, 'admin', '2025-07-11 15:50:47', '', '2025-07-11 15:50:47', NULL);
INSERT INTO `mini_industry_tree` VALUES (39, 3, NULL, '医疗器械核心器件', '包括传感器、芯片、精密机械部件等医疗器械核心组件', 'position', 2, 'upstream', '0', 3, '0', '0', NULL, 'admin', '2025-07-11 15:50:47', '', '2025-07-11 15:50:47', NULL);
INSERT INTO `mini_industry_tree` VALUES (40, 3, NULL, '药品制造', '包括化学药品、生物制品、中药制剂、疫苗等药品的生产制造', 'position', 2, 'midstream', '0', 4, '0', '0', NULL, 'admin', '2025-07-11 15:50:47', '', '2025-07-11 15:50:47', NULL);
INSERT INTO `mini_industry_tree` VALUES (41, 3, NULL, '医疗器械制造', '包括诊断设备、治疗设备、手术器械、体外诊断试剂等医疗器械', 'position', 2, 'midstream', '0', 5, '0', '0', NULL, 'admin', '2025-07-11 15:50:47', '', '2025-07-11 15:50:47', NULL);
INSERT INTO `mini_industry_tree` VALUES (42, 3, NULL, '数字医疗技术', '包括医疗AI、远程医疗、医疗大数据、精准医疗等数字化医疗技术', 'position', 2, 'midstream', '0', 6, '0', '0', NULL, 'admin', '2025-07-11 15:50:47', '', '2025-07-11 15:50:47', NULL);
INSERT INTO `mini_industry_tree` VALUES (43, 3, NULL, '医疗服务', '包括医院、诊所、第三方检验、康复护理等医疗服务机构', 'position', 2, 'downstream', '0', 7, '0', '0', NULL, 'admin', '2025-07-11 15:50:47', '', '2025-07-11 15:50:47', NULL);
INSERT INTO `mini_industry_tree` VALUES (44, 3, NULL, '药品流通', '包括药品批发、零售药店、医药电商、药品配送等流通环节', 'position', 2, 'downstream', '0', 8, '0', '0', NULL, 'admin', '2025-07-11 15:50:47', '', '2025-07-11 15:50:47', NULL);
INSERT INTO `mini_industry_tree` VALUES (45, 3, NULL, '健康管理', '包括体检中心、健康咨询、慢病管理、养老服务等健康管理服务', 'position', 2, 'downstream', '0', 9, '0', '0', NULL, 'admin', '2025-07-11 15:50:47', '', '2025-07-11 15:50:47', NULL);
INSERT INTO `mini_industry_tree` VALUES (46, 4, NULL, '算力基础设施', '包括AI芯片、GPU、TPU、量子计算等算力硬件基础设施', 'position', 2, 'upstream', '0', 1, '0', '0', NULL, 'admin', '2025-07-11 15:51:02', '', '2025-07-11 15:51:02', NULL);
INSERT INTO `mini_industry_tree` VALUES (47, 4, NULL, '数据资源', '包括数据采集、数据标注、数据清洗、数据集构建等数据资源服务', 'position', 2, 'upstream', '0', 2, '0', '0', NULL, 'admin', '2025-07-11 15:51:02', '', '2025-07-11 15:51:02', NULL);
INSERT INTO `mini_industry_tree` VALUES (48, 4, NULL, '算法框架', '包括深度学习框架、机器学习平台、AI开发工具等算法基础设施', 'position', 2, 'upstream', '0', 3, '0', '0', NULL, 'admin', '2025-07-11 15:51:02', '', '2025-07-11 15:51:02', NULL);
INSERT INTO `mini_industry_tree` VALUES (49, 4, NULL, '核心技术研发', '包括机器学习、深度学习、计算机视觉、自然语言处理等核心技术', 'position', 2, 'midstream', '0', 4, '0', '0', NULL, 'admin', '2025-07-11 15:51:02', '', '2025-07-11 15:51:02', NULL);
INSERT INTO `mini_industry_tree` VALUES (50, 4, NULL, 'AI平台与工具', '包括AI开发平台、模型训练平台、AI中台、MLOps等技术平台', 'position', 2, 'midstream', '0', 5, '0', '0', NULL, 'admin', '2025-07-11 15:51:02', '', '2025-07-11 15:51:02', NULL);
INSERT INTO `mini_industry_tree` VALUES (51, 4, NULL, '智能产品制造', '包括智能机器人、智能音箱、自动驾驶系统等AI产品制造', 'position', 2, 'midstream', '0', 6, '0', '0', NULL, 'admin', '2025-07-11 15:51:02', '', '2025-07-11 15:51:02', NULL);
INSERT INTO `mini_industry_tree` VALUES (52, 4, NULL, '行业应用', '包括智慧金融、智慧医疗、智慧教育、智慧城市等行业AI应用', 'position', 2, 'downstream', '0', 7, '0', '0', NULL, 'admin', '2025-07-11 15:51:02', '', '2025-07-11 15:51:02', NULL);
INSERT INTO `mini_industry_tree` VALUES (53, 4, NULL, '消费级应用', '包括智能手机、智能家居、娱乐游戏、个人助手等消费级AI应用', 'position', 2, 'downstream', '0', 8, '0', '0', NULL, 'admin', '2025-07-11 15:51:02', '', '2025-07-11 15:51:02', NULL);
INSERT INTO `mini_industry_tree` VALUES (54, 4, NULL, 'AI服务', '包括AI咨询、模型部署、技术支持、AI培训等专业服务', 'position', 2, 'downstream', '0', 9, '0', '0', NULL, 'admin', '2025-07-11 15:51:02', '', '2025-07-11 15:51:02', NULL);
INSERT INTO `mini_industry_tree` VALUES (55, 5, NULL, '生物资源', '包括微生物菌株、酶制剂、生物催化剂、基因工程菌等生物资源', 'position', 2, 'upstream', '0', 1, '0', '0', NULL, 'admin', '2025-07-11 15:52:02', '', '2025-07-11 15:52:02', NULL);
INSERT INTO `mini_industry_tree` VALUES (56, 5, NULL, '生物技术平台', '包括基因编辑、合成生物学、发酵工程、细胞培养等技术平台', 'position', 2, 'upstream', '0', 2, '0', '0', NULL, 'admin', '2025-07-11 15:52:02', '', '2025-07-11 15:52:02', NULL);
INSERT INTO `mini_industry_tree` VALUES (57, 5, NULL, '生产设备', '包括生物反应器、发酵罐、分离纯化设备等生物制造专用设备', 'position', 2, 'upstream', '0', 3, '0', '0', NULL, 'admin', '2025-07-11 15:52:02', '', '2025-07-11 15:52:02', NULL);
INSERT INTO `mini_industry_tree` VALUES (58, 5, NULL, '生物化工', '包括生物基化学品、生物塑料、生物燃料等生物基化工产品制造', 'position', 2, 'midstream', '0', 4, '0', '0', NULL, 'admin', '2025-07-11 15:52:02', '', '2025-07-11 15:52:02', NULL);
INSERT INTO `mini_industry_tree` VALUES (59, 5, NULL, '生物材料', '包括生物降解材料、生物医用材料、生物纤维等生物材料制造', 'position', 2, 'midstream', '0', 5, '0', '0', NULL, 'admin', '2025-07-11 15:52:02', '', '2025-07-11 15:52:02', NULL);
INSERT INTO `mini_industry_tree` VALUES (60, 5, NULL, '生物食品', '包括发酵食品、功能食品、营养补充剂、食品添加剂等生物食品', 'position', 2, 'midstream', '0', 6, '0', '0', NULL, 'admin', '2025-07-11 15:52:02', '', '2025-07-11 15:52:02', NULL);
INSERT INTO `mini_industry_tree` VALUES (61, 5, NULL, '终端应用', '包括日化用品、纺织服装、包装材料、农业应用等终端产品', 'position', 2, 'downstream', '0', 7, '0', '0', NULL, 'admin', '2025-07-11 15:52:02', '', '2025-07-11 15:52:02', NULL);
INSERT INTO `mini_industry_tree` VALUES (62, 5, NULL, '环保应用', '包括生物修复、废物处理、环境监测、碳捕获等环保应用', 'position', 2, 'downstream', '0', 8, '0', '0', NULL, 'admin', '2025-07-11 15:52:02', '', '2025-07-11 15:52:02', NULL);
INSERT INTO `mini_industry_tree` VALUES (63, 5, NULL, '技术服务', '包括工艺开发、技术转让、检测认证、咨询服务等专业服务', 'position', 2, 'downstream', '0', 9, '0', '0', NULL, 'admin', '2025-07-11 15:52:02', '', '2025-07-11 15:52:02', NULL);
INSERT INTO `mini_industry_tree` VALUES (64, 6, NULL, '光电技术', '包括激光技术、光通信、光电显示、光伏技术等光电子技术', 'position', 2, NULL, '0', 6, '0', '0', NULL, 'admin', '2025-07-11 15:52:12', '', '2025-07-11 15:52:12', NULL);
INSERT INTO `mini_industry_tree` VALUES (65, 6, NULL, '新能源技术', '包括核能技术、氢能技术、储能技术、能源互联网等新能源技术', 'position', 2, NULL, '0', 7, '0', '0', NULL, 'admin', '2025-07-11 15:52:12', '', '2025-07-11 15:52:12', NULL);
INSERT INTO `mini_industry_tree` VALUES (66, 6, NULL, '生物技术', '包括基因工程、细胞工程、蛋白质工程、生物信息学等生物技术', 'position', 2, NULL, '0', 8, '0', '0', NULL, 'admin', '2025-07-11 15:52:12', '', '2025-07-11 15:52:12', NULL);
INSERT INTO `mini_industry_tree` VALUES (67, 6, NULL, '新材料技术', '包括纳米材料、超材料、智能材料、生物材料等新材料技术', 'position', 2, NULL, '0', 9, '0', '0', NULL, 'admin', '2025-07-11 15:52:12', '', '2025-07-11 15:52:12', NULL);
INSERT INTO `mini_industry_tree` VALUES (68, 6, NULL, '信息技术', '包括云计算、大数据、物联网、区块链、边缘计算等信息技术', 'position', 2, NULL, '0', 10, '0', '0', NULL, 'admin', '2025-07-11 15:52:12', '', '2025-07-11 15:52:12', NULL);
INSERT INTO `mini_industry_tree` VALUES (69, 7, NULL, '金融服务', '包括银行、证券、保险、基金、信托等传统金融服务', 'position', 2, NULL, '0', 1, '0', '0', NULL, 'admin', '2025-07-11 15:52:24', '', '2025-07-11 15:52:24', NULL);
INSERT INTO `mini_industry_tree` VALUES (70, 7, NULL, '金融科技', '包括移动支付、数字货币、互联网银行、智能投顾等金融科技服务', 'position', 2, NULL, '0', 2, '0', '0', NULL, 'admin', '2025-07-11 15:52:24', '', '2025-07-11 15:52:24', NULL);
INSERT INTO `mini_industry_tree` VALUES (71, 7, NULL, '现代物流', '包括快递配送、仓储管理、供应链金融、智慧物流等物流服务', 'position', 2, NULL, '0', 3, '0', '0', NULL, 'admin', '2025-07-11 15:52:24', '', '2025-07-11 15:52:24', NULL);
INSERT INTO `mini_industry_tree` VALUES (72, 7, NULL, '电子商务', '包括B2B平台、B2C平台、跨境电商、社交电商等电商服务', 'position', 2, NULL, '0', 4, '0', '0', NULL, 'admin', '2025-07-11 15:52:24', '', '2025-07-11 15:52:24', NULL);
INSERT INTO `mini_industry_tree` VALUES (73, 7, NULL, '专业服务', '包括法律服务、会计服务、咨询服务、人力资源等专业服务', 'position', 2, NULL, '0', 5, '0', '0', NULL, 'admin', '2025-07-11 15:52:24', '', '2025-07-11 15:52:24', NULL);
INSERT INTO `mini_industry_tree` VALUES (74, 7, NULL, '文化创意', '包括影视制作、游戏开发、广告设计、文化旅游等创意服务', 'position', 2, NULL, '0', 6, '0', '0', NULL, 'admin', '2025-07-11 15:52:24', '', '2025-07-11 15:52:24', NULL);
INSERT INTO `mini_industry_tree` VALUES (75, 7, NULL, '教育培训', '包括在线教育、职业培训、企业培训、教育科技等教育服务', 'position', 2, NULL, '0', 7, '0', '0', NULL, 'admin', '2025-07-11 15:52:24', '', '2025-07-11 15:52:24', NULL);
INSERT INTO `mini_industry_tree` VALUES (76, 7, NULL, '生活服务', '包括餐饮服务、家政服务、美容健身、休闲娱乐等生活服务', 'position', 2, NULL, '0', 8, '0', '0', NULL, 'admin', '2025-07-11 15:52:24', '', '2025-07-11 15:52:24', NULL);
INSERT INTO `mini_industry_tree` VALUES (77, 16, NULL, '光伏设备', '包括太阳能电池板、逆变器、跟踪支架等光伏发电设备', 'segment', 3, NULL, '0', 1, '0', '0', NULL, 'admin', '2025-07-11 15:52:38', '', '2025-07-16 09:43:33', NULL);
INSERT INTO `mini_industry_tree` VALUES (78, 16, NULL, '风电设备', '包括风力发电机组、叶片、齿轮箱、发电机等风电设备', 'segment', 3, NULL, '0', 2, '0', '0', NULL, 'admin', '2025-07-11 15:52:38', '', '2025-07-16 09:43:33', NULL);
INSERT INTO `mini_industry_tree` VALUES (79, 16, NULL, '水电设备', '包括水轮机、发电机、调速器等水力发电设备', 'segment', 3, NULL, '0', 3, '0', '0', NULL, 'admin', '2025-07-11 15:52:38', '', '2025-07-16 09:43:33', NULL);
INSERT INTO `mini_industry_tree` VALUES (80, 17, NULL, '电化学储能', '包括锂离子电池、钠离子电池、液流电池等电化学储能系统', 'segment', 3, NULL, '0', 1, '0', '0', NULL, 'admin', '2025-07-11 15:52:38', '', '2025-07-16 09:43:33', NULL);
INSERT INTO `mini_industry_tree` VALUES (81, 17, NULL, '机械储能', '包括抽水蓄能、压缩空气储能、飞轮储能等机械储能系统', 'segment', 3, NULL, '0', 2, '0', '0', NULL, 'admin', '2025-07-11 15:52:38', '', '2025-07-16 09:43:33', NULL);
INSERT INTO `mini_industry_tree` VALUES (82, 17, NULL, '智能电网', '包括智能变电站、配电自动化、电力物联网等智能电网技术', 'segment', 3, NULL, '0', 3, '0', '0', NULL, 'admin', '2025-07-11 15:52:38', '', '2025-07-16 09:43:33', NULL);
INSERT INTO `mini_industry_tree` VALUES (83, 18, NULL, '氢气制备', '包括电解水制氢、天然气重整制氢、生物质制氢等制氢技术', 'segment', 3, NULL, '0', 1, '0', '0', NULL, 'admin', '2025-07-11 15:52:38', '', '2025-07-16 09:43:33', NULL);
INSERT INTO `mini_industry_tree` VALUES (84, 18, NULL, '氢气储运', '包括高压储氢、液氢储运、管道输氢等氢气储运技术', 'segment', 3, NULL, '0', 2, '0', '0', NULL, 'admin', '2025-07-11 15:52:38', '', '2025-07-16 09:43:33', NULL);
INSERT INTO `mini_industry_tree` VALUES (85, 18, NULL, '燃料电池', '包括质子交换膜燃料电池、固体氧化物燃料电池等燃料电池技术', 'segment', 3, NULL, '0', 3, '0', '0', NULL, 'admin', '2025-07-11 15:52:38', '', '2025-07-16 09:43:33', NULL);
INSERT INTO `mini_industry_tree` VALUES (86, 26, NULL, '集成电路设计', '包括CPU、GPU、FPGA、ASIC等集成电路的设计开发', 'segment', 3, NULL, '0', 1, '0', '0', NULL, 'admin', '2025-07-11 15:53:43', '', '2025-07-11 15:53:43', NULL);
INSERT INTO `mini_industry_tree` VALUES (87, 26, NULL, '芯片制造', '包括晶圆制造、封装测试、设备材料等芯片制造环节', 'segment', 3, NULL, '0', 2, '0', '0', NULL, 'admin', '2025-07-11 15:53:43', '', '2025-07-11 15:53:43', NULL);
INSERT INTO `mini_industry_tree` VALUES (88, 26, NULL, '功率器件', '包括IGBT、MOSFET、SiC、GaN等功率半导体器件', 'segment', 3, NULL, '0', 3, '0', '0', NULL, 'admin', '2025-07-11 15:53:43', '', '2025-07-11 15:53:43', NULL);
INSERT INTO `mini_industry_tree` VALUES (89, 27, NULL, '量子计算', '包括量子芯片、量子算法、量子软件等量子计算技术', 'segment', 3, NULL, '0', 1, '0', '0', NULL, 'admin', '2025-07-11 15:53:43', '', '2025-07-11 15:53:43', NULL);
INSERT INTO `mini_industry_tree` VALUES (90, 27, NULL, '量子通信', '包括量子密钥分发、量子网络、量子中继等量子通信技术', 'segment', 3, NULL, '0', 2, '0', '0', NULL, 'admin', '2025-07-11 15:53:43', '', '2025-07-11 15:53:43', NULL);
INSERT INTO `mini_industry_tree` VALUES (91, 27, NULL, '量子传感', '包括量子雷达、量子陀螺仪、量子磁力计等量子传感技术', 'segment', 3, NULL, '0', 3, '0', '0', NULL, 'admin', '2025-07-11 15:53:43', '', '2025-07-11 15:53:43', NULL);
INSERT INTO `mini_industry_tree` VALUES (92, 28, NULL, '工业机器人', '包括多关节机器人、协作机器人、特种机器人等工业机器人', 'segment', 3, NULL, '0', 1, '0', '0', NULL, 'admin', '2025-07-11 15:53:43', '', '2025-07-11 15:53:43', NULL);
INSERT INTO `mini_industry_tree` VALUES (93, 28, NULL, '数控机床', '包括五轴加工中心、精密车床、磨床等高端数控机床', 'segment', 3, NULL, '0', 2, '0', '0', NULL, 'admin', '2025-07-11 15:53:43', '', '2025-07-11 15:53:43', NULL);
INSERT INTO `mini_industry_tree` VALUES (94, 28, NULL, '3D打印设备', '包括金属3D打印、陶瓷3D打印、生物3D打印等增材制造设备', 'segment', 3, NULL, '0', 3, '0', '0', NULL, 'admin', '2025-07-11 15:53:43', '', '2025-07-11 15:53:43', NULL);
INSERT INTO `mini_industry_tree` VALUES (95, 40, NULL, '化学药品', '包括小分子化学药、仿制药、创新药等化学药品制造', 'segment', 3, NULL, '0', 1, '0', '0', NULL, 'admin', '2025-07-11 15:53:58', '', '2025-07-11 15:53:58', NULL);
INSERT INTO `mini_industry_tree` VALUES (96, 40, NULL, '生物制品', '包括单克隆抗体、疫苗、血液制品、基因治疗等生物制品', 'segment', 3, NULL, '0', 2, '0', '0', NULL, 'admin', '2025-07-11 15:53:58', '', '2025-07-11 15:53:58', NULL);
INSERT INTO `mini_industry_tree` VALUES (97, 40, NULL, '中药制剂', '包括中药饮片、中成药、中药配方颗粒等中药制剂', 'segment', 3, NULL, '0', 3, '0', '0', NULL, 'admin', '2025-07-11 15:53:58', '', '2025-07-11 15:53:58', NULL);
INSERT INTO `mini_industry_tree` VALUES (98, 41, NULL, '诊断设备', '包括CT、MRI、超声、内窥镜等医学影像和诊断设备', 'segment', 3, NULL, '0', 1, '0', '0', NULL, 'admin', '2025-07-11 15:53:58', '', '2025-07-11 15:53:58', NULL);
INSERT INTO `mini_industry_tree` VALUES (99, 41, NULL, '治疗设备', '包括手术机器人、放疗设备、激光治疗仪等治疗设备', 'segment', 3, NULL, '0', 2, '0', '0', NULL, 'admin', '2025-07-11 15:53:58', '', '2025-07-11 15:53:58', NULL);
INSERT INTO `mini_industry_tree` VALUES (100, 41, NULL, '体外诊断', '包括生化诊断、免疫诊断、分子诊断、POCT等体外诊断产品', 'segment', 3, NULL, '0', 3, '0', '0', NULL, 'admin', '2025-07-11 15:53:58', '', '2025-07-11 15:53:58', NULL);
INSERT INTO `mini_industry_tree` VALUES (101, 42, NULL, '医疗AI', '包括医学影像AI、药物发现AI、临床决策支持等医疗人工智能', 'segment', 3, NULL, '0', 1, '0', '0', NULL, 'admin', '2025-07-11 15:53:58', '', '2025-07-11 15:53:58', NULL);
INSERT INTO `mini_industry_tree` VALUES (102, 42, NULL, '远程医疗', '包括远程诊断、远程手术、远程监护等远程医疗技术', 'segment', 3, NULL, '0', 2, '0', '0', NULL, 'admin', '2025-07-11 15:53:58', '', '2025-07-11 15:53:58', NULL);
INSERT INTO `mini_industry_tree` VALUES (103, 42, NULL, '精准医疗', '包括基因检测、个性化用药、精准诊断等精准医疗技术', 'segment', 3, NULL, '0', 3, '0', '0', NULL, 'admin', '2025-07-11 15:53:58', '', '2025-07-11 15:53:58', NULL);
INSERT INTO `mini_industry_tree` VALUES (104, 70, NULL, '银行服务', '包括商业银行、投资银行、私人银行、数字银行等银行服务', 'segment', 3, NULL, '0', 1, '0', '0', NULL, 'admin', '2025-07-11 15:55:19', '', '2025-07-11 15:55:19', NULL);
INSERT INTO `mini_industry_tree` VALUES (105, 70, NULL, '证券服务', '包括证券经纪、投资银行、资产管理、研究咨询等证券服务', 'segment', 3, NULL, '0', 2, '0', '0', NULL, 'admin', '2025-07-11 15:55:19', '', '2025-07-11 15:55:19', NULL);
INSERT INTO `mini_industry_tree` VALUES (106, 70, NULL, '保险服务', '包括人寿保险、财产保险、健康保险、再保险等保险服务', 'segment', 3, NULL, '0', 3, '0', '0', NULL, 'admin', '2025-07-11 15:55:19', '', '2025-07-11 15:55:19', NULL);
INSERT INTO `mini_industry_tree` VALUES (107, 71, NULL, '支付科技', '包括移动支付、数字钱包、跨境支付、央行数字货币等支付技术', 'segment', 3, NULL, '0', 1, '0', '0', NULL, 'admin', '2025-07-11 15:55:19', '', '2025-07-11 15:55:19', NULL);
INSERT INTO `mini_industry_tree` VALUES (108, 71, NULL, '借贷科技', '包括P2P借贷、消费金融、供应链金融、智能风控等借贷技术', 'segment', 3, NULL, '0', 2, '0', '0', NULL, 'admin', '2025-07-11 15:55:19', '', '2025-07-11 15:55:19', NULL);
INSERT INTO `mini_industry_tree` VALUES (109, 71, NULL, '投资科技', '包括智能投顾、量化交易、区块链金融、数字资产等投资技术', 'segment', 3, NULL, '0', 3, '0', '0', NULL, 'admin', '2025-07-11 15:55:19', '', '2025-07-11 15:55:19', NULL);
INSERT INTO `mini_industry_tree` VALUES (110, 74, NULL, 'B2B电商', '包括大宗商品交易、工业品采购、跨境B2B等企业级电商平台', 'segment', 3, NULL, '0', 1, '0', '0', NULL, 'admin', '2025-07-11 15:55:19', '', '2025-07-11 15:55:19', NULL);
INSERT INTO `mini_industry_tree` VALUES (111, 74, NULL, 'B2C电商', '包括综合电商、垂直电商、社交电商、直播电商等消费级电商', 'segment', 3, NULL, '0', 2, '0', '0', NULL, 'admin', '2025-07-11 15:55:19', '', '2025-07-11 15:55:19', NULL);
INSERT INTO `mini_industry_tree` VALUES (112, 74, NULL, '跨境电商', '包括出口电商、进口电商、海外仓、跨境物流等跨境电商服务', 'segment', 3, NULL, '0', 3, '0', '0', NULL, 'admin', '2025-07-11 15:55:19', '', '2025-07-11 15:55:19', NULL);
INSERT INTO `mini_industry_tree` VALUES (113, 23, NULL, '其他', '', 'segment', 3, '', '0', 0, '0', '0', NULL, 'admin', '2025-07-24 09:28:55', '', '2025-07-24 09:28:54', NULL);

-- ----------------------------
-- Table structure for mini_job
-- ----------------------------
DROP TABLE IF EXISTS `mini_job`;
CREATE TABLE `mini_job`  (
  `job_id` bigint NOT NULL AUTO_INCREMENT COMMENT '职位ID',
  `job_title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '职位名称',
  `job_type_id` bigint NULL DEFAULT NULL COMMENT '职位类型ID',
  `company_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '公司名称',
  `company_scale` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '公司规模',
  `salary_range` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '薪资范围',
  `job_tags` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '职位标签（多个用逗号分隔）',
  `work_location` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '工作地点',
  `job_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL COMMENT '职位描述',
  `requirements` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL COMMENT '任职要求',
  `contact_info` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '联系方式',
  `sort_order` int NULL DEFAULT 0 COMMENT '排序（数字越小越靠前）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`job_id`) USING BTREE,
  INDEX `idx_sort_order`(`sort_order` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_create_time`(`create_time` ASC) USING BTREE,
  INDEX `idx_job_type_id`(`job_type_id` ASC) USING BTREE,
  CONSTRAINT `fk_job_type_id` FOREIGN KEY (`job_type_id`) REFERENCES `mini_job_type` (`job_type_id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci COMMENT = '招聘职位表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of mini_job
-- ----------------------------
INSERT INTO `mini_job` VALUES (1, 'Java开发工程师', NULL, '腾讯科技有限公司', '1000-5000人', '15K-25K', 'Java,Spring Boot,微服务,MySQL', '深圳', '负责后端系统开发，参与微服务架构设计，优化系统性能。', '1. 本科及以上学历，计算机相关专业；\n2. 3年以上Java开发经验；\n3. 熟悉Spring Boot、Spring Cloud等框架；\n4. 熟悉MySQL、Redis等数据库；\n5. 有微服务架构经验者优先。', '邮箱：<EMAIL>\n电话：0755-86013388', 1, '0', 'admin', '2025-07-07 16:06:24', '', NULL, '急招岗位，可提供竞争力薪酬');
INSERT INTO `mini_job` VALUES (2, '前端开发工程师', NULL, '字节跳动', '5000人以上', '12K-20K', 'Vue.js,React,JavaScript,TypeScript', '北京', '负责前端页面开发，与设计师、产品经理密切合作，打造优秀的用户体验。', '1. 本科及以上学历；\n2. 2年以上前端开发经验；\n3. 精通Vue.js或React框架；\n4. 熟悉JavaScript、TypeScript；\n5. 有移动端开发经验者优先。', '邮箱：<EMAIL>\n电话：010-85555888', 2, '0', 'admin', '2025-07-07 16:06:32', '', NULL, '福利待遇优厚，团队氛围好');
INSERT INTO `mini_job` VALUES (3, '产品经理', NULL, '阿里巴巴集团', '5000人以上', '18K-30K', '产品规划,用户体验,数据分析,原型设计', '杭州', '负责产品规划和设计，分析用户需求，制定产品发展策略，协调跨部门合作。', '1. 本科及以上学历；\n2. 3年以上产品经理经验；\n3. 具备良好的逻辑思维和沟通能力；\n4. 熟悉互联网产品开发流程；\n5. 有B端产品经验者优先。', '邮箱：<EMAIL>\n电话：0571-85022088', 3, '0', 'admin', '2025-07-07 16:06:43', '', NULL, '发展前景广阔，学习机会多');
INSERT INTO `mini_job` VALUES (4, 'UI/UX设计师', NULL, '小米科技', '1000-5000人', '10K-18K', 'UI设计,UX设计,Sketch,Figma,用户体验', '北京', '负责产品界面设计，用户体验优化，制作设计规范，与开发团队协作完成产品设计。', '1. 本科及以上学历，设计相关专业；\n2. 2年以上UI/UX设计经验；\n3. 精通Sketch、Figma等设计工具；\n4. 具备良好的审美和创意能力；\n5. 有移动端设计经验者优先。', '邮箱：<EMAIL>\n电话：010-56789012', 4, '0', 'admin', '2025-07-07 16:06:51', '', NULL, '设计团队专业，成长空间大');
INSERT INTO `mini_job` VALUES (5, '数据分析师', NULL, '美团', '5000人以上', '13K-22K', 'Python,SQL,数据挖掘,机器学习,Tableau', '上海', '负责业务数据分析，构建数据模型，为业务决策提供数据支持，优化算法模型。', '1. 本科及以上学历，统计学、数学等相关专业；\n2. 2年以上数据分析经验；\n3. 熟练使用Python、SQL等工具；\n4. 有机器学习项目经验；\n5. 具备良好的业务理解能力。', '邮箱：<EMAIL>\n电话：021-51234567', 5, '0', 'admin', '2025-07-07 16:06:59', '', NULL, '数据驱动决策，技术氛围浓厚');
INSERT INTO `mini_job` VALUES (6, '测试工程师', NULL, '京东集团', '5000人以上', '11K-18K', '软件测试,自动化测试,性能测试,测试工具', '北京', '负责软件产品测试，编写测试用例，执行功能测试和性能测试，保证产品质量。', '1. 本科及以上学历，计算机相关专业；\n2. 2年以上测试经验；\n3. 熟悉测试理论和方法；\n4. 有自动化测试经验者优先；\n5. 细心负责，有团队合作精神。', '邮箱：<EMAIL>\n电话：010-89898989', 6, '0', 'admin', '2025-07-07 16:07:17', '', NULL, '重视产品质量，测试团队专业');
INSERT INTO `mini_job` VALUES (7, 'DevOps工程师', NULL, '华为技术有限公司', '5000人以上', '16K-28K', 'Docker,Kubernetes,Jenkins,运维,云计算', '深圳', '负责CI/CD流水线建设，容器化部署，监控告警系统维护，云平台运维。', '1. 本科及以上学历；\n2. 3年以上运维或DevOps经验；\n3. 熟悉Docker、Kubernetes等容器技术；\n4. 有云平台使用经验；\n5. 具备自动化运维思维。', '邮箱：<EMAIL>\n电话：0755-28780808', 7, '0', 'admin', '2025-07-07 16:07:24', '', NULL, '技术先进，待遇优厚');
INSERT INTO `mini_job` VALUES (8, '售前工程师', NULL, '江西宇悦科技有限公司', '50', '6K-12K', '需求调研与分析,行业解决方案（金融/制造/零售/医疗）,竞品分析ROI/成本效益评估,售前建议书（RFP/RFI）,项目落地咨询', '江西', '售前工程师是连接技术和业务的桥梁，负责在客户决策阶段提供技术解决方案与方案演示，推动销售机会转化，确保交付团队能够顺利实施。', '本科及以上学历，计算机、通信、电子等相关专业优先\n\n2 年及以上 B2B/企业级 IT 解决方案售前或技术支持经验\n\n技术能力\n\n熟悉操作系统（Linux/Windows）、网络（TCP/IP、路由交换）、数据库（MySQL/Oracle）\n掌握一种或多种编程语言（Java、Python、Go、JavaScript 等）或脚本语言', '15720911797', 0, '0', '', '2025-07-09 15:58:17', '', '2025-07-09 16:23:35', NULL);

-- ----------------------------
-- Table structure for mini_job_type
-- ----------------------------
DROP TABLE IF EXISTS `mini_job_type`;
CREATE TABLE `mini_job_type`  (
  `job_type_id` bigint NOT NULL AUTO_INCREMENT COMMENT '职位类型ID',
  `type_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '类型名称',
  `description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '类型描述',
  `sort_order` int NULL DEFAULT 0 COMMENT '显示顺序',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`job_type_id`) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_sort_order`(`sort_order` ASC) USING BTREE,
  INDEX `idx_create_time`(`create_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '职位类型表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of mini_job_type
-- ----------------------------
INSERT INTO `mini_job_type` VALUES (1, '技术类', '软件开发、技术支持等技术相关职位', 1, '0', 'admin', '2025-07-07 16:29:40', '', '2025-07-16 09:07:04', NULL);
INSERT INTO `mini_job_type` VALUES (2, '产品类', '产品经理、产品设计等产品相关职位', 2, '0', 'admin', '2025-07-07 16:29:40', '', NULL, NULL);
INSERT INTO `mini_job_type` VALUES (3, '设计类', 'UI设计、视觉设计等设计相关职位', 3, '0', 'admin', '2025-07-07 16:29:40', '', NULL, NULL);
INSERT INTO `mini_job_type` VALUES (4, '市场类', '市场推广、品牌营销等市场相关职位', 4, '0', 'admin', '2025-07-07 16:29:40', '', NULL, NULL);
INSERT INTO `mini_job_type` VALUES (5, '运营类', '用户运营、内容运营等运营相关职位', 5, '0', 'admin', '2025-07-07 16:29:40', '', NULL, NULL);
INSERT INTO `mini_job_type` VALUES (6, '销售类', '销售代表、销售经理等销售相关职位', 6, '0', 'admin', '2025-07-07 16:29:40', '', NULL, NULL);
INSERT INTO `mini_job_type` VALUES (7, '管理类', '项目管理、团队管理等管理相关职位', 7, '0', 'admin', '2025-07-07 16:29:40', '', NULL, NULL);
INSERT INTO `mini_job_type` VALUES (8, '人事类', '人力资源、招聘等人事相关职位', 8, '0', 'admin', '2025-07-07 16:29:40', '', NULL, NULL);
INSERT INTO `mini_job_type` VALUES (9, '财务类', '会计、财务分析等财务相关职位', 9, '0', 'admin', '2025-07-07 16:29:40', '', NULL, NULL);
INSERT INTO `mini_job_type` VALUES (10, '其他类', '其他类型职位', 10, '0', 'admin', '2025-07-07 16:29:40', '', NULL, NULL);

-- ----------------------------
-- Table structure for mini_news_center
-- ----------------------------
DROP TABLE IF EXISTS `mini_news_center`;
CREATE TABLE `mini_news_center`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '新闻ID',
  `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '文章标题',
  `author` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '作者',
  `thumb_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '封面图片',
  `digest` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '文章摘要',
  `wechat_article_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '微信文章ID',
  `wechat_article_url` varchar(800) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '微信文章链接',
  `status` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态（0-正常，1-停用）',
  `wechat_create_time` timestamp NULL DEFAULT NULL COMMENT '微信文章创建时间',
  `sync_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '同步时间',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_mini_news_center_wechat_article_id`(`wechat_article_id` ASC) USING BTREE,
  INDEX `idx_mini_news_center_wechat_create_time`(`wechat_create_time` DESC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 727 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '新闻中心表（微信公众号文章同步）' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of mini_news_center
-- ----------------------------
INSERT INTO `mini_news_center` VALUES (723, '测试页', '吴工', 'http://mmecoa.qpic.cn/sz_mmecoa_jpg/pPkwklJGkOjIquLf969jCs7PibyfibbHFR3cKjjcZwRcTqkUhiae6rGAq0OcvoGKtmBDyTS9LeJnK0ZhkNiaZmOBtg/0?wx_fmt=jpeg', '测试1：？？？？？？。。。。。', '5Rne1KrITOUcetDKrjXdbkE5xkM7QD8QpqU525RUG5KcPjiXbvt6WKgrD2E40irt', 'http://mp.weixin.qq.com/s?__biz=MzkwMjg1MDQ0OA==&mid=2247483691&idx=1&sn=d174a5077d4e53936c141998a7559d12&chksm=c09e73d4f7e9fac205b4156971ce00a9f71e97e8f500dd4fddb176b610952f52349bffa1791e#rd', '0', '2025-07-16 11:17:37', '2025-07-23 10:38:06', '2025-07-23 10:38:06', '2025-07-23 10:38:06');
INSERT INTO `mini_news_center` VALUES (724, '江西宇悦科技有限公司', '吴工', 'http://mmecoa.qpic.cn/sz_mmecoa_jpg/pPkwklJGkOjIquLf969jCs7PibyfibbHFR3cKjjcZwRcTqkUhiae6rGAq0OcvoGKtmBDyTS9LeJnK0ZhkNiaZmOBtg/0?wx_fmt=jpeg', '江西宇悦科技有限公司，成立于2020-07-28，位于江西省上饶市，是一家以从事互联网和相关服务为主的企业。', '5Rne1KrITOUcetDKrjXdbh0cIgQKxFx5HWwXw8CSOie9c16CfkTLZCizxZAxfjVW', 'http://mp.weixin.qq.com/s?__biz=MzkwMjg1MDQ0OA==&mid=2247483686&idx=1&sn=1e1060f7efcb2d52eda01c30741caab5&chksm=c09e73d9f7e9facf47bb14bd85f59cd2aef27f271eb503eabd3708bea54cc07df78611043684#rd', '0', '2025-07-16 11:00:34', '2025-07-23 10:38:06', '2025-07-23 10:38:06', '2025-07-23 10:38:06');
INSERT INTO `mini_news_center` VALUES (725, '\\u58eb\\u5927\\u592b\\u53d1\\u751fs', '\\u5f52\\u5c5e', 'http://mmecoa.qpic.cn/sz_mmecoa_jpg/pPkwklJGkOjIquLf969jCs7PibyfibbHFRI4loxQIX2OkWgzm2Qz6Vze9gU9jcnrwc2XYVQhiadiahQob6TC5fia4FA/0?wx_fmt=jpeg', '\\u4e09\\u56fd\\u6740', '5Rne1KrITOUcetDKrjXdbosiB60-zAvqOGfyGSPpbI03YdFes0Fijl40FxmYjRS3', 'http://mp.weixin.qq.com/s?__biz=MzkwMjg1MDQ0OA==&mid=2247483681&idx=1&sn=bd8985930c85718086532a6eec2bd2b6&chksm=c09e73def7e9fac8f9902ecfe225e868a8789aaa0c19ade07e64e130b90afbe4d8412f476601#rd', '0', '2025-07-16 10:40:37', '2025-07-23 10:38:06', '2025-07-23 10:38:06', '2025-07-23 10:38:06');
INSERT INTO `mini_news_center` VALUES (726, '\\u963f\\u8fbe\\u5927\\u6492\\u5927\\u6492\\u5927\\u5927', '\\u963f\\u53d1', 'http://mmecoa.qpic.cn/sz_mmecoa_jpg/pPkwklJGkOjIquLf969jCs7PibyfibbHFRI4loxQIX2OkWgzm2Qz6Vze9gU9jcnrwc2XYVQhiadiahQob6TC5fia4FA/0?wx_fmt=jpeg', '\\u7684\\u53d1\\u751f\\u53d1\\u987a\\u4e30', '5Rne1KrITOUcetDKrjXdbtONvWPO29AoMO7Q5TR1WT2oWDS5ROe_a6tPsk4hTg5d', 'http://mp.weixin.qq.com/s?__biz=MzkwMjg1MDQ0OA==&mid=2247483676&idx=1&sn=3304ee2fc84a1999f136455b1702f373&chksm=c09e73e3f7e9faf57dbd1160b9c67fd3520b04d9c9e8a6f9fe0e9fb5746e5973e601e70cb5b8#rd', '0', '2025-07-16 10:33:57', '2025-07-23 10:38:06', '2025-07-23 10:38:06', '2025-07-23 10:38:06');

-- ----------------------------
-- Table structure for mini_notice
-- ----------------------------
DROP TABLE IF EXISTS `mini_notice`;
CREATE TABLE `mini_notice`  (
  `notice_id` bigint NOT NULL AUTO_INCREMENT COMMENT '通知ID',
  `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '' COMMENT '通知标题',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '通知内容（富文本）',
  `sort_order` int NULL DEFAULT 0 COMMENT '排序（数字越小越靠前）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`notice_id`) USING BTREE,
  INDEX `idx_sort_order`(`sort_order` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_title`(`title` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci COMMENT = '通知表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of mini_notice
-- ----------------------------
INSERT INTO `mini_notice` VALUES (1, '欢迎通知', '<p><img src=\"http://************:8080/profile/upload/2025/07/18/2300973acccb48c5920aa552179cb944_20250718141628A002.jpg\">欢迎使用天津海棠科技创新小程序！<img src=\"/dev-api/profile/upload/2025/07/18/09fa24b9a06f4f69b8284ca56b67b2b3_20250718112651A001.jpg\"></p>', 1, '0', 'admin', '2025-07-07 14:32:36', '', '2025-07-18 14:16:31', '欢迎通知');
INSERT INTO `mini_notice` VALUES (2, '活动提醒', '请及时关注最新活动信息', 2, '0', 'admin', '2025-07-07 14:32:36', '', NULL, '活动提醒');
INSERT INTO `mini_notice` VALUES (3, '维护通知', '系统将于每周日进行维护，请注意保存数据', 3, '0', 'admin', '2025-07-07 14:32:36', '', NULL, '维护通知');

-- ----------------------------
-- Table structure for mini_page_content
-- ----------------------------
DROP TABLE IF EXISTS `mini_page_content`;
CREATE TABLE `mini_page_content`  (
  `content_id` bigint NOT NULL AUTO_INCREMENT COMMENT '内容ID',
  `page_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '页面编码',
  `page_title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '页面标题',
  `page_content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL COMMENT '页面内容（支持富文本）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`content_id`) USING BTREE,
  UNIQUE INDEX `uk_page_code`(`page_code` ASC) USING BTREE,
  INDEX `idx_page_code`(`page_code` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci COMMENT = '页面内容管理表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of mini_page_content
-- ----------------------------

-- ----------------------------
-- Table structure for mini_park
-- ----------------------------
DROP TABLE IF EXISTS `mini_park`;
CREATE TABLE `mini_park`  (
  `park_id` bigint NOT NULL AUTO_INCREMENT COMMENT '园区ID',
  `park_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '园区名称',
  `park_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '园区编码',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '园区简介',
  `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '园区详细内容（富文本）',
  `cover_image` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '园区封面图片',
  `sort_order` int NULL DEFAULT 0 COMMENT '排序（数字越小越靠前）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`park_id`) USING BTREE,
  INDEX `idx_park_name`(`park_name` ASC) USING BTREE,
  INDEX `idx_park_code`(`park_code` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_sort_order`(`sort_order` ASC) USING BTREE,
  INDEX `idx_create_time`(`create_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '园区管理表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of mini_park
-- ----------------------------
INSERT INTO `mini_park` VALUES (1, '天津大学科技园（天开）', 'TIANKAI', '为入驻企业高质量发展提供全方位赋能', '<p>测试</p>', 'http://************:8086/profile/upload/2025/07/30/61ac5e440dac4d97a6624e05044c4074_20250730123233A001.jpg', 0, '0', '', '2025-07-30 04:32:44', '', '2025-07-30 04:38:42', NULL);
INSERT INTO `mini_park` VALUES (2, '天津大学科技园（南开）', 'NANKAI', '打造天津大学创新创业生态', '<p>测试</p>', 'http://************:8086/profile/upload/2025/07/30/0494e945880511ebb6edd017c2d2eca2_20250730123728A001.jpg', 1, '0', '', '2025-07-30 04:37:37', '', '2025-07-30 04:37:54', NULL);
INSERT INTO `mini_park` VALUES (3, '天津大学科技园（津南）', 'JINNAN', '推动天津大学“斯坦福+硅谷”模式的形成', '<p><br></p>', 'http://************:8086/profile/upload/2025/07/30/bbc58c3d998d4f389fdae649334873f721_20250730124111A002.jpg', 2, '0', '', '2025-07-30 04:41:16', '', '2025-07-30 04:42:46', NULL);
INSERT INTO `mini_park` VALUES (4, '天津大学科技园（西青）', 'XIQIN', '新能新材，“种”在西青', NULL, 'http://************:8086/profile/upload/2025/07/30/a05050278b4d45268de555032becdbb8_20250730124225A003.jpg', 3, '0', '', '2025-07-30 04:42:29', '', '2025-07-30 04:42:49', NULL);

-- ----------------------------
-- Table structure for mini_project_guidance
-- ----------------------------
DROP TABLE IF EXISTS `mini_project_guidance`;
CREATE TABLE `mini_project_guidance`  (
  `guidance_id` bigint NOT NULL AUTO_INCREMENT COMMENT '辅导ID',
  `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '辅导标题',
  `cover_image` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '辅导封面图',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL COMMENT '辅导描述',
  `location` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '辅导地点',
  `start_time` datetime NOT NULL COMMENT '辅导开始时间',
  `end_time` datetime NOT NULL COMMENT '辅导结束时间',
  `registration_deadline` datetime NULL DEFAULT NULL COMMENT '报名截止时间',
  `form_fields` json NULL COMMENT '报名表单字段配置（JSON格式）',
  `sort_order` int NULL DEFAULT 0 COMMENT '排序（数字越小越靠前）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`guidance_id`) USING BTREE,
  INDEX `idx_mini_project_guidance_sort_order`(`sort_order` ASC) USING BTREE,
  INDEX `idx_mini_project_guidance_status`(`status` ASC) USING BTREE,
  INDEX `idx_mini_project_guidance_start_time`(`start_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci COMMENT = '项目辅导表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of mini_project_guidance
-- ----------------------------
INSERT INTO `mini_project_guidance` VALUES (1, '创业项目路演指导', 'http://************:8080/profile/upload/2025/07/22/6d1ffde61ff84984a225945dd3383da0_20250722100721A010.jpg', '<p>为有创业想法的同学提供专业的项目路演指导，包括商业计划书撰写、融资策略、市场分析等内容。</p><p>本次指导活动邀请多位成功创业者和投资人分享经验。</p>', '天津大学北洋园校区创新创业中心', '2025-02-15 14:00:00', '2025-02-15 18:00:00', '2025-02-10 23:59:59', '[{\"name\": \"name\", \"type\": \"input\", \"label\": \"姓名\", \"options\": \"\", \"required\": true}, {\"name\": \"student_id\", \"type\": \"input\", \"label\": \"学号\", \"options\": \"\", \"required\": true}, {\"name\": \"phone\", \"type\": \"tel\", \"label\": \"联系电话\", \"options\": \"\", \"required\": true}, {\"name\": \"project_name\", \"type\": \"input\", \"label\": \"项目名称\", \"options\": \"\", \"required\": false}, {\"name\": \"project_description\", \"type\": \"textarea\", \"label\": \"项目简介\", \"options\": \"\", \"required\": false}]', 1, '0', 'admin', '2025-07-07 18:34:58', '', '2025-07-22 10:07:25', '创业项目路演指导活动');
INSERT INTO `mini_project_guidance` VALUES (2, '海棠杯项目孵化营', 'http://************:8080/profile/upload/2025/07/22/5848e4a37f5e4fb39f4c4384a9c27523_20250722100747A011.jpg', '<p>海棠杯创新创业大赛优秀项目专项孵化营，为获奖项目提供深度孵化服务。</p><p>包含导师一对一指导、资源对接、团队建设等内容。</p>', '天津大学科技园', '2025-03-01 09:00:00', '2025-03-03 17:00:00', '2025-02-25 23:59:59', '[{\"name\": \"team_name\", \"type\": \"input\", \"label\": \"团队名称\", \"options\": \"\", \"required\": true}, {\"name\": \"team_leader\", \"type\": \"input\", \"label\": \"团队负责人\", \"options\": \"\", \"required\": true}, {\"name\": \"contact\", \"type\": \"tel\", \"label\": \"联系方式\", \"options\": \"\", \"required\": true}, {\"name\": \"project_category\", \"type\": \"select\", \"label\": \"项目类别\", \"options\": \"科技创新,文化创意,社会服务,其他\", \"required\": true}, {\"name\": \"awards\", \"type\": \"textarea\", \"label\": \"获奖情况\", \"options\": \"\", \"required\": false}]', 2, '0', 'admin', '2025-07-07 18:34:58', '', '2025-07-22 10:07:50', '海棠杯项目孵化营');
INSERT INTO `mini_project_guidance` VALUES (3, '投融资对接会', 'http://************:8080/profile/upload/2025/07/22/dbb8acc172c446cda6fc9edde541712321_20250722100805A012.jpg', '<p>为优秀创业项目搭建与投资机构的对接平台，促进项目融资。</p><p>邀请知名投资机构、天使投资人参与，为项目提供融资机会。</p>', '天津大学国际会议中心', '2025-04-10 13:30:00', '2025-04-10 17:30:00', '2025-04-05 23:59:59', '[{\"name\": \"project_name\", \"type\": \"input\", \"label\": \"项目名称\", \"options\": \"\", \"required\": true}, {\"name\": \"leader_name\", \"type\": \"input\", \"label\": \"负责人姓名\", \"options\": \"\", \"required\": true}, {\"name\": \"phone\", \"type\": \"tel\", \"label\": \"联系电话\", \"options\": \"\", \"required\": true}, {\"name\": \"funding_needs\", \"type\": \"input\", \"label\": \"融资需求\", \"options\": \"\", \"required\": true}, {\"name\": \"project_stage\", \"type\": \"select\", \"label\": \"项目阶段\", \"options\": \"创意阶段,产品开发,市场验证,规模化发展\", \"required\": true}, {\"name\": \"project_introduction\", \"type\": \"textarea\", \"label\": \"项目介绍\", \"options\": \"\", \"required\": true}]', 3, '1', 'admin', '2025-07-07 18:34:58', '', '2025-07-22 10:08:16', '投融资对接会');

-- ----------------------------
-- Table structure for mini_project_registration
-- ----------------------------
DROP TABLE IF EXISTS `mini_project_registration`;
CREATE TABLE `mini_project_registration`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '报名ID',
  `project_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '项目名称',
  `team_size` int NOT NULL COMMENT '成员数',
  `city` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '所在城市',
  `competition_area` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '报名赛区',
  `industry` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '所在行业',
  `is_tju_alumni` tinyint(1) NOT NULL COMMENT '创始人是否为天大校友/老师：1-是，0-否',
  `project_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '项目介绍',
  `has_company` tinyint(1) NOT NULL COMMENT '是否成立公司：1-是，0-否',
  `company_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '企业名称',
  `last_year_revenue` decimal(15, 2) NULL DEFAULT NULL COMMENT '上年度营业收入（万元）',
  `project_valuation` decimal(15, 2) NULL DEFAULT NULL COMMENT '项目估值（万元）',
  `latest_funding_round` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '最新融资轮次',
  `investment_institution` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '投资机构',
  `company_logo` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '公司logo文件路径',
  `project_bp` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '项目BP文件路径',
  `recommender` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '参赛推荐方',
  `sponsor_unit` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '赞助单位图片URL',
  `contact_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '联系人姓名',
  `contact_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '联系电话',
  `contact_wechat` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '微信号',
  `contact_position` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '职务',
  `registration_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '报名时间',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态：1-待审核，2-审核通过，3-审核拒绝',
  `audit_time` timestamp NULL DEFAULT NULL COMMENT '审核时间',
  `audit_remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '审核备注',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_mini_project_registration_status`(`status` ASC) USING BTREE,
  INDEX `idx_mini_project_registration_phone`(`contact_phone` ASC) USING BTREE,
  INDEX `idx_mini_project_registration_time`(`registration_time` DESC) USING BTREE,
  INDEX `idx_mini_project_registration_area`(`competition_area` ASC) USING BTREE,
  INDEX `idx_mini_project_registration_industry`(`industry` ASC) USING BTREE,
  INDEX `idx_mini_project_registration_city`(`city` ASC) USING BTREE,
  INDEX `idx_mini_project_registration_tju_alumni`(`is_tju_alumni` ASC) USING BTREE,
  INDEX `idx_mini_project_registration_has_company`(`has_company` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '项目报名表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of mini_project_registration
-- ----------------------------

-- ----------------------------
-- Table structure for mini_tech_star
-- ----------------------------
DROP TABLE IF EXISTS `mini_tech_star`;
CREATE TABLE `mini_tech_star`  (
  `star_id` bigint NOT NULL AUTO_INCREMENT COMMENT '科技之星ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '姓名',
  `photo_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '照片地址',
  `top_image_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '顶图地址',
  `company_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '企业名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL COMMENT '简介',
  `founder_introduction` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL COMMENT '创始人介绍（富文本）',
  `company_introduction` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL COMMENT '公司介绍（富文本）',
  `sort_order` int NULL DEFAULT 0 COMMENT '排序（数字越小越靠前）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`star_id`) USING BTREE,
  INDEX `idx_sort_order`(`sort_order` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci COMMENT = '科技之星表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of mini_tech_star
-- ----------------------------
INSERT INTO `mini_tech_star` VALUES (1, '小陈', 'http://************:8086/profile/upload/2025/07/30/8373377e69c3499d904877fab8c6f329_20250730100631A003.jpg', 'http://************:8086/profile/upload/2025/07/30/3b16ee137013427c98a906b91048bc6621_20250730100636A004.jpg', '江西宇悦科技有限公司', '江西宇悦科技有限公司的Java高级开发', '<p>份地方撒地方</p>', '<p>的发生法大师傅</p>', 0, '0', '', '2025-07-07 16:17:39', '', '2025-07-30 10:06:45', NULL);
INSERT INTO `mini_tech_star` VALUES (2, '小吴', 'http://************:8086/profile/upload/2025/07/30/373fac74aed74ddabba1477085316015_20250730100450A001.jpg', 'http://************:8086/profile/upload/2025/07/30/0432a40b6ab749f8ae473d5c7e9ce12a_20250730100504A002.jpg', '江西宇悦科技有限公司', '江西宇悦科技有限公司', '<p>发达省份王夫人</p>', '<p>发发士大夫大师傅给</p>', 0, '0', '', '2025-07-18 14:34:34', '', '2025-07-30 10:05:42', NULL);
INSERT INTO `mini_tech_star` VALUES (4, '小张', 'http://************:8086/profile/upload/2025/07/30/c5366469b00940f5b8077c20c2c2d089_20250730110454A001.jpg', 'http://************:8086/profile/upload/2025/07/30/05671daab7d04086aaa594abb85e99ca_20250730110501A002.jpg', '江西宇悦', '测试', '<p>发范德萨发撒打发士大夫放大发到付da<img src=\"http://************:8086/profile/upload/2025/07/30/89fcad7d3a354016a293904bac76002511_20250730110535A003.png\"></p><p>放大飞洒地方士大夫的阿凡达发的的撒范德萨发生的afdsf</p>', '<p>放大范德萨发达十分士大夫的萨芬士大夫大师傅撒地方发放单飞<img src=\"http://************:8086/profile/upload/2025/07/30/3981173d6f7e4c808528488c7ec7e462_20250730110555A004.jpg\"></p><p>发的法大师傅的撒反对f</p>', 0, '0', '', '2025-07-30 11:05:58', '', '2025-07-30 11:22:00', NULL);

-- ----------------------------
-- Table structure for mini_top_image
-- ----------------------------
DROP TABLE IF EXISTS `mini_top_image`;
CREATE TABLE `mini_top_image`  (
  `top_image_id` bigint NOT NULL AUTO_INCREMENT COMMENT 'top图片ID',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '图片标题',
  `image_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '图片URL',
  `link_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '链接URL',
  `sort_order` int NULL DEFAULT 0 COMMENT '排序顺序',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `page_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'home' COMMENT '页面标识(home-首页,activity-活动页,news-新闻页,job-招聘页等)',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`top_image_id`) USING BTREE,
  INDEX `idx_sort_order`(`sort_order` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_page_code_status`(`page_code` ASC, `status` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'top图片管理表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of mini_top_image
-- ----------------------------
INSERT INTO `mini_top_image` VALUES (1, '首页top图', 'http://************:8086/profile/upload/2025/07/28/481465568ef442c490780bf8b0151c83_20250728163738A004.jpg', '', 0, '0', 'home', '', '2025-07-07 10:26:25', '', '2025-07-28 16:37:39', NULL);
INSERT INTO `mini_top_image` VALUES (2, '创赛路演top图', 'http://************:8086/profile/upload/2025/07/28/910563d1e10048208be6342670480064_20250728163728A003.jpg', '', 0, '0', 'roadshow', '', '2025-07-21 19:10:07', '', '2025-07-28 16:37:30', NULL);
INSERT INTO `mini_top_image` VALUES (3, '海棠杯top图', 'http://************:8086/profile/upload/2025/07/28/758029e9407d4f3e90050925dc4a8a86_20250728163719A002.jpg', '', 0, '0', 'htcup', '', '2025-07-21 19:10:24', '', '2025-07-28 16:37:19', NULL);
INSERT INTO `mini_top_image` VALUES (4, '项目报名top图', 'http://************:8086/profile/upload/2025/07/28/56340f0599074c038374613937e4b0e9_20250728163705A001.jpg', '', 0, '0', 'projectApply', '', '2025-07-22 09:45:39', '', '2025-07-28 16:37:06', '项目报名top图');
INSERT INTO `mini_top_image` VALUES (5, '园区top图', 'http://************:8086/profile/upload/2025/07/28/0432a40b6ab749f8ae473d5c7e9ce12a_20250728163747A005.jpg', '', 0, '0', 'garden', '', '2025-07-28 16:38:00', '', NULL, NULL);
INSERT INTO `mini_top_image` VALUES (6, '专家矩阵top图', 'http://************:8086/profile/upload/2025/07/29/a58cbbffd2aa49c1b1e99990be912f3021_20250729093725A001.jpg', '', 8, '0', 'expert', '', '2025-07-29 09:37:42', '', NULL, NULL);

-- ----------------------------
-- Table structure for mini_user_follow
-- ----------------------------
DROP TABLE IF EXISTS `mini_user_follow`;
CREATE TABLE `mini_user_follow`  (
  `follow_id` bigint NOT NULL AUTO_INCREMENT COMMENT '关注ID',
  `follower_id` bigint NOT NULL COMMENT '关注者用户ID',
  `followed_id` bigint NOT NULL COMMENT '被关注者用户ID',
  `follow_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '关注时间',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '0' COMMENT '状态（0正常 1取消关注）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`follow_id`) USING BTREE,
  UNIQUE INDEX `uk_follower_followed`(`follower_id` ASC, `followed_id` ASC) USING BTREE,
  INDEX `idx_follower_id`(`follower_id` ASC) USING BTREE,
  INDEX `idx_followed_id`(`followed_id` ASC) USING BTREE,
  INDEX `idx_follow_time`(`follow_time` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户关注表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of mini_user_follow
-- ----------------------------

-- ----------------------------
-- Table structure for mini_video_showcase
-- ----------------------------
DROP TABLE IF EXISTS `mini_video_showcase`;
CREATE TABLE `mini_video_showcase`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '视频ID',
  `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '视频标题',
  `video_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '视频URL',
  `sort_order` int NULL DEFAULT 0 COMMENT '排序',
  `status` tinyint NULL DEFAULT 0 COMMENT '状态：0-正常，1-停用',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_mini_video_showcase_status_sort`(`status` ASC, `sort_order` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '视频展播表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of mini_video_showcase
-- ----------------------------
INSERT INTO `mini_video_showcase` VALUES (1, '国债小知识', 'http://************:8086/profile/upload/2025/07/30/new1verson_ios_friendly_20250730091044A003.mp4', 0, 0, '2025-07-08 15:14:47', '2025-07-08 15:14:47');
INSERT INTO `mini_video_showcase` VALUES (2, '小诺亚奥特曼', 'http://************:8086/profile/upload/2025/07/30/ed_hd_512kb_20250730091019A002.mp4', 0, 0, '2025-07-09 16:43:13', '2025-07-09 16:43:13');

-- ----------------------------
-- Table structure for qrtz_blob_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_blob_triggers`;
CREATE TABLE `qrtz_blob_triggers`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '调度名称',
  `trigger_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT 'qrtz_triggers表trigger_name的外键',
  `trigger_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  `blob_data` blob NULL COMMENT '存放持久化Trigger对象',
  PRIMARY KEY (`sched_name`, `trigger_name`, `trigger_group`) USING BTREE,
  CONSTRAINT `qrtz_blob_triggers_ibfk_1` FOREIGN KEY (`sched_name`, `trigger_name`, `trigger_group`) REFERENCES `qrtz_triggers` (`sched_name`, `trigger_name`, `trigger_group`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci COMMENT = 'Blob类型的触发器表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of qrtz_blob_triggers
-- ----------------------------

-- ----------------------------
-- Table structure for qrtz_calendars
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_calendars`;
CREATE TABLE `qrtz_calendars`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '调度名称',
  `calendar_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '日历名称',
  `calendar` blob NOT NULL COMMENT '存放持久化calendar对象',
  PRIMARY KEY (`sched_name`, `calendar_name`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci COMMENT = '日历信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of qrtz_calendars
-- ----------------------------

-- ----------------------------
-- Table structure for qrtz_cron_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_cron_triggers`;
CREATE TABLE `qrtz_cron_triggers`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '调度名称',
  `trigger_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT 'qrtz_triggers表trigger_name的外键',
  `trigger_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  `cron_expression` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT 'cron表达式',
  `time_zone_id` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '时区',
  PRIMARY KEY (`sched_name`, `trigger_name`, `trigger_group`) USING BTREE,
  CONSTRAINT `qrtz_cron_triggers_ibfk_1` FOREIGN KEY (`sched_name`, `trigger_name`, `trigger_group`) REFERENCES `qrtz_triggers` (`sched_name`, `trigger_name`, `trigger_group`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci COMMENT = 'Cron类型的触发器表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of qrtz_cron_triggers
-- ----------------------------

-- ----------------------------
-- Table structure for qrtz_fired_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_fired_triggers`;
CREATE TABLE `qrtz_fired_triggers`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '调度名称',
  `entry_id` varchar(95) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '调度器实例id',
  `trigger_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT 'qrtz_triggers表trigger_name的外键',
  `trigger_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  `instance_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '调度器实例名',
  `fired_time` bigint NOT NULL COMMENT '触发的时间',
  `sched_time` bigint NOT NULL COMMENT '定时器制定的时间',
  `priority` int NOT NULL COMMENT '优先级',
  `state` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '状态',
  `job_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '任务名称',
  `job_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '任务组名',
  `is_nonconcurrent` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '是否并发',
  `requests_recovery` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '是否接受恢复执行',
  PRIMARY KEY (`sched_name`, `entry_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci COMMENT = '已触发的触发器表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of qrtz_fired_triggers
-- ----------------------------

-- ----------------------------
-- Table structure for qrtz_job_details
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_job_details`;
CREATE TABLE `qrtz_job_details`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '调度名称',
  `job_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '任务名称',
  `job_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '任务组名',
  `description` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '相关介绍',
  `job_class_name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '执行任务类名称',
  `is_durable` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '是否持久化',
  `is_nonconcurrent` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '是否并发',
  `is_update_data` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '是否更新数据',
  `requests_recovery` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '是否接受恢复执行',
  `job_data` blob NULL COMMENT '存放持久化job对象',
  PRIMARY KEY (`sched_name`, `job_name`, `job_group`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci COMMENT = '任务详细信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of qrtz_job_details
-- ----------------------------

-- ----------------------------
-- Table structure for qrtz_locks
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_locks`;
CREATE TABLE `qrtz_locks`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '调度名称',
  `lock_name` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '悲观锁名称',
  PRIMARY KEY (`sched_name`, `lock_name`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci COMMENT = '存储的悲观锁信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of qrtz_locks
-- ----------------------------

-- ----------------------------
-- Table structure for qrtz_paused_trigger_grps
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_paused_trigger_grps`;
CREATE TABLE `qrtz_paused_trigger_grps`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '调度名称',
  `trigger_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  PRIMARY KEY (`sched_name`, `trigger_group`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci COMMENT = '暂停的触发器表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of qrtz_paused_trigger_grps
-- ----------------------------

-- ----------------------------
-- Table structure for qrtz_scheduler_state
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_scheduler_state`;
CREATE TABLE `qrtz_scheduler_state`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '调度名称',
  `instance_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '实例名称',
  `last_checkin_time` bigint NOT NULL COMMENT '上次检查时间',
  `checkin_interval` bigint NOT NULL COMMENT '检查间隔时间',
  PRIMARY KEY (`sched_name`, `instance_name`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci COMMENT = '调度器状态表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of qrtz_scheduler_state
-- ----------------------------

-- ----------------------------
-- Table structure for qrtz_simple_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_simple_triggers`;
CREATE TABLE `qrtz_simple_triggers`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '调度名称',
  `trigger_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT 'qrtz_triggers表trigger_name的外键',
  `trigger_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  `repeat_count` bigint NOT NULL COMMENT '重复的次数统计',
  `repeat_interval` bigint NOT NULL COMMENT '重复的间隔时间',
  `times_triggered` bigint NOT NULL COMMENT '已经触发的次数',
  PRIMARY KEY (`sched_name`, `trigger_name`, `trigger_group`) USING BTREE,
  CONSTRAINT `qrtz_simple_triggers_ibfk_1` FOREIGN KEY (`sched_name`, `trigger_name`, `trigger_group`) REFERENCES `qrtz_triggers` (`sched_name`, `trigger_name`, `trigger_group`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci COMMENT = '简单触发器的信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of qrtz_simple_triggers
-- ----------------------------

-- ----------------------------
-- Table structure for qrtz_simprop_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_simprop_triggers`;
CREATE TABLE `qrtz_simprop_triggers`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '调度名称',
  `trigger_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT 'qrtz_triggers表trigger_name的外键',
  `trigger_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  `str_prop_1` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT 'String类型的trigger的第一个参数',
  `str_prop_2` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT 'String类型的trigger的第二个参数',
  `str_prop_3` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT 'String类型的trigger的第三个参数',
  `int_prop_1` int NULL DEFAULT NULL COMMENT 'int类型的trigger的第一个参数',
  `int_prop_2` int NULL DEFAULT NULL COMMENT 'int类型的trigger的第二个参数',
  `long_prop_1` bigint NULL DEFAULT NULL COMMENT 'long类型的trigger的第一个参数',
  `long_prop_2` bigint NULL DEFAULT NULL COMMENT 'long类型的trigger的第二个参数',
  `dec_prop_1` decimal(13, 4) NULL DEFAULT NULL COMMENT 'decimal类型的trigger的第一个参数',
  `dec_prop_2` decimal(13, 4) NULL DEFAULT NULL COMMENT 'decimal类型的trigger的第二个参数',
  `bool_prop_1` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT 'Boolean类型的trigger的第一个参数',
  `bool_prop_2` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT 'Boolean类型的trigger的第二个参数',
  PRIMARY KEY (`sched_name`, `trigger_name`, `trigger_group`) USING BTREE,
  CONSTRAINT `qrtz_simprop_triggers_ibfk_1` FOREIGN KEY (`sched_name`, `trigger_name`, `trigger_group`) REFERENCES `qrtz_triggers` (`sched_name`, `trigger_name`, `trigger_group`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci COMMENT = '同步机制的行锁表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of qrtz_simprop_triggers
-- ----------------------------

-- ----------------------------
-- Table structure for qrtz_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_triggers`;
CREATE TABLE `qrtz_triggers`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '调度名称',
  `trigger_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '触发器的名字',
  `trigger_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '触发器所属组的名字',
  `job_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT 'qrtz_job_details表job_name的外键',
  `job_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT 'qrtz_job_details表job_group的外键',
  `description` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '相关介绍',
  `next_fire_time` bigint NULL DEFAULT NULL COMMENT '上一次触发时间（毫秒）',
  `prev_fire_time` bigint NULL DEFAULT NULL COMMENT '下一次触发时间（默认为-1表示不触发）',
  `priority` int NULL DEFAULT NULL COMMENT '优先级',
  `trigger_state` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '触发器状态',
  `trigger_type` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '触发器的类型',
  `start_time` bigint NOT NULL COMMENT '开始时间',
  `end_time` bigint NULL DEFAULT NULL COMMENT '结束时间',
  `calendar_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '日程表名称',
  `misfire_instr` smallint NULL DEFAULT NULL COMMENT '补偿执行的策略',
  `job_data` blob NULL COMMENT '存放持久化job对象',
  PRIMARY KEY (`sched_name`, `trigger_name`, `trigger_group`) USING BTREE,
  INDEX `sched_name`(`sched_name` ASC, `job_name` ASC, `job_group` ASC) USING BTREE,
  CONSTRAINT `qrtz_triggers_ibfk_1` FOREIGN KEY (`sched_name`, `job_name`, `job_group`) REFERENCES `qrtz_job_details` (`sched_name`, `job_name`, `job_group`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci COMMENT = '触发器详细信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of qrtz_triggers
-- ----------------------------

-- ----------------------------
-- Table structure for sensitive_word
-- ----------------------------
DROP TABLE IF EXISTS `sensitive_word`;
CREATE TABLE `sensitive_word`  (
  `word_id` bigint NOT NULL AUTO_INCREMENT COMMENT '敏感词ID',
  `category_id` bigint NULL DEFAULT NULL COMMENT '分类ID',
  `word_content` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '敏感词内容',
  `word_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '1' COMMENT '词类型（1敏感词 2白名单）',
  `severity_level` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '1' COMMENT '严重程度（1轻微 2中等 3严重）',
  `replacement_char` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '*' COMMENT '替换字符',
  `is_regex` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '是否正则表达式（0否 1是）',
  `hit_count` bigint NULL DEFAULT 0 COMMENT '命中次数',
  `last_hit_time` datetime NULL DEFAULT NULL COMMENT '最后命中时间',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`word_id`) USING BTREE,
  UNIQUE INDEX `uk_word_content`(`word_content` ASC) USING BTREE,
  INDEX `idx_category_id`(`category_id` ASC) USING BTREE,
  INDEX `idx_word_type`(`word_type` ASC) USING BTREE,
  INDEX `idx_severity_level`(`severity_level` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_hit_count`(`hit_count` ASC) USING BTREE,
  CONSTRAINT `fk_sensitive_word_category` FOREIGN KEY (`category_id`) REFERENCES `sensitive_word_category` (`category_id`) ON DELETE SET NULL ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 25 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '敏感词表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sensitive_word
-- ----------------------------
INSERT INTO `sensitive_word` VALUES (1, 2, '色情', '1', '3', '*', '0', 0, NULL, '0', 'admin', '2025-07-17 12:22:53', '', NULL, NULL);
INSERT INTO `sensitive_word` VALUES (2, 2, '黄色', '1', '2', '*', '0', 0, NULL, '0', 'admin', '2025-07-17 12:22:53', '', NULL, NULL);
INSERT INTO `sensitive_word` VALUES (3, 3, '暴力', '1', '3', '*', '0', 0, NULL, '0', 'admin', '2025-07-17 12:22:53', '', NULL, NULL);
INSERT INTO `sensitive_word` VALUES (4, 4, '毒品', '1', '3', '*', '0', 0, NULL, '0', 'admin', '2025-07-17 12:22:53', '', NULL, NULL);
INSERT INTO `sensitive_word` VALUES (5, 5, '赌博', '1', '3', '*', '0', 2, '2025-07-17 15:01:42', '0', 'admin', '2025-07-17 12:22:53', '', NULL, NULL);
INSERT INTO `sensitive_word` VALUES (6, 6, '垃圾广告', '1', '2', '*', '0', 0, NULL, '0', 'admin', '2025-07-17 12:22:53', '', NULL, NULL);
INSERT INTO `sensitive_word` VALUES (7, 7, '测试敏感词', '1', '1', '*', '0', 0, NULL, '0', 'admin', '2025-07-17 12:22:53', '', NULL, NULL);
INSERT INTO `sensitive_word` VALUES (8, 7, '正常词汇', '2', '1', '*', '0', 0, NULL, '0', 'admin', '2025-07-17 12:22:53', '', NULL, NULL);
INSERT INTO `sensitive_word` VALUES (9, 7, '合法内容', '2', '1', '*', '0', 0, NULL, '0', 'admin', '2025-07-17 12:22:53', '', NULL, NULL);
INSERT INTO `sensitive_word` VALUES (10, 7, '傻逼', '1', '2', '*', '0', 0, NULL, '0', 'admin', '2025-07-17 12:58:28', '', NULL, NULL);
INSERT INTO `sensitive_word` VALUES (11, 7, '白痴', '1', '2', '*', '0', 3, '2025-07-17 14:37:07', '0', 'admin', '2025-07-17 12:58:28', '', NULL, NULL);
INSERT INTO `sensitive_word` VALUES (12, 7, '垃圾', '1', '1', '*', '0', 2, '2025-07-17 15:11:06', '0', 'admin', '2025-07-17 12:58:28', '', NULL, NULL);
INSERT INTO `sensitive_word` VALUES (13, 7, '死去', '1', '2', '*', '0', 0, NULL, '0', 'admin', '2025-07-17 12:58:28', '', NULL, NULL);
INSERT INTO `sensitive_word` VALUES (14, 2, '性感', '1', '1', '*', '0', 0, NULL, '0', 'admin', '2025-07-17 12:58:28', '', NULL, NULL);
INSERT INTO `sensitive_word` VALUES (15, 3, '杀死', '1', '3', '*', '0', 0, NULL, '0', 'admin', '2025-07-17 12:58:28', '', NULL, NULL);
INSERT INTO `sensitive_word` VALUES (16, 4, '走私', '1', '3', '*', '0', 0, NULL, '0', 'admin', '2025-07-17 12:58:28', '', NULL, NULL);
INSERT INTO `sensitive_word` VALUES (17, 5, '彩票', '1', '2', '*', '0', 2, '2025-07-24 09:11:00', '0', 'admin', '2025-07-17 12:58:28', '', NULL, NULL);

-- ----------------------------
-- Table structure for sensitive_word_category
-- ----------------------------
DROP TABLE IF EXISTS `sensitive_word_category`;
CREATE TABLE `sensitive_word_category`  (
  `category_id` bigint NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `category_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '分类名称',
  `category_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '分类编码',
  `description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '分类描述',
  `sort_order` int NULL DEFAULT 0 COMMENT '排序（数字越小越靠前）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`category_id`) USING BTREE,
  UNIQUE INDEX `uk_category_code`(`category_code` ASC) USING BTREE,
  INDEX `idx_category_name`(`category_name` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '敏感词分类表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sensitive_word_category
-- ----------------------------
INSERT INTO `sensitive_word_category` VALUES (1, '政治敏感', 'POLITICAL', '涉及政治相关的敏感词汇', 1, '0', 'admin', '2025-07-17 12:22:53', '', NULL, NULL);
INSERT INTO `sensitive_word_category` VALUES (2, '色情低俗', 'PORNOGRAPHIC', '涉及色情、低俗内容的敏感词汇', 2, '0', 'admin', '2025-07-17 12:22:53', '', NULL, NULL);
INSERT INTO `sensitive_word_category` VALUES (3, '暴力血腥', 'VIOLENT', '涉及暴力、血腥内容的敏感词汇', 3, '0', 'admin', '2025-07-17 12:22:53', '', NULL, NULL);
INSERT INTO `sensitive_word_category` VALUES (4, '违法犯罪', 'ILLEGAL', '涉及违法犯罪活动的敏感词汇', 4, '0', 'admin', '2025-07-17 12:22:53', '', NULL, NULL);
INSERT INTO `sensitive_word_category` VALUES (5, '赌博诈骗', 'GAMBLING', '涉及赌博、诈骗等非法活动的敏感词汇', 5, '0', 'admin', '2025-07-17 12:22:53', '', NULL, NULL);
INSERT INTO `sensitive_word_category` VALUES (6, '广告垃圾', 'SPAM', '垃圾广告、恶意推广等敏感词汇', 6, '0', 'admin', '2025-07-17 12:22:53', '', NULL, NULL);
INSERT INTO `sensitive_word_category` VALUES (7, '其他敏感', 'OTHER', '其他类型的敏感词汇', 99, '0', 'admin', '2025-07-17 12:22:53', '', NULL, NULL);

-- ----------------------------
-- Table structure for sensitive_word_log
-- ----------------------------
DROP TABLE IF EXISTS `sensitive_word_log`;
CREATE TABLE `sensitive_word_log`  (
  `log_id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `user_id` bigint NULL DEFAULT NULL COMMENT '用户ID',
  `user_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '用户名',
  `module_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '模块名称',
  `operation_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '操作类型（检测/过滤/替换）',
  `original_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '原始内容',
  `filtered_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '过滤后内容',
  `hit_words` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '命中的敏感词（JSON格式）',
  `hit_count` int NULL DEFAULT 0 COMMENT '命中数量',
  `client_ip` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '客户端IP',
  `user_agent` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '用户代理',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`log_id`) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_module_name`(`module_name` ASC) USING BTREE,
  INDEX `idx_operation_type`(`operation_type` ASC) USING BTREE,
  INDEX `idx_create_time`(`create_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 28 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '敏感词检测日志表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sensitive_word_log
-- ----------------------------
INSERT INTO `sensitive_word_log` VALUES (1, 109, 'TiAmo', '用户资料', '过滤', '夏宇杰', '***', '[\"夏宇杰\"]', 1, '*************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2503300 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/1346', '2025-07-17 16:09:44');
INSERT INTO `sensitive_word_log` VALUES (2, 109, 'TiAmo', '用户资料', '过滤', '19970326790', '***********', '[\"19970326790\"]', 1, '*************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2503300 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/1346', '2025-07-17 16:09:44');
INSERT INTO `sensitive_word_log` VALUES (3, 109, 'TiAmo', '用户资料', '过滤', 'http://************:8080/profile/upload/2025/07/16/iIoayDr0h3ffb01ed58bec4d8bdebd4d521592f010ea_20250716153807A003.png', 'http://************:8080/profile/upload/2025/07/16/iIoayDr0h3ffb01ed58bec4d8bdebd4d521592f010ea_**************A003.png', '[\"20250716153807\"]', 1, '*************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2503300 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/1346', '2025-07-17 16:09:44');
INSERT INTO `sensitive_word_log` VALUES (4, 109, 'TiAmo', '用户资料', '过滤', '我是个怪物习近平', '我是个怪物***', '[\"习近平\"]', 1, '*************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2503300 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/1351', '2025-07-17 16:19:59');
INSERT INTO `sensitive_word_log` VALUES (5, 1, 'admin', '弹幕管理', '拒绝', '习近平', '习近平', '[\"习近平\"]', 1, '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-17 16:37:52');
INSERT INTO `sensitive_word_log` VALUES (6, 1, 'admin', '弹幕管理', '替换', '习近平', '***', '[\"习近平\"]', 1, '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-17 17:16:58');
INSERT INTO `sensitive_word_log` VALUES (7, 1, 'admin', '弹幕管理', '替换', '习近平', '***', '[\"习近平\"]', 1, '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-17 17:16:59');
INSERT INTO `sensitive_word_log` VALUES (8, 1, 'admin', '弹幕管理', '拒绝', '习近平', '习近平', '[\"习近平\"]', 1, '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-17 17:17:53');
INSERT INTO `sensitive_word_log` VALUES (9, 1, 'admin', '弹幕管理', '拒绝', '习近平', '习近平', '[\"习近平\"]', 1, '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-17 17:18:50');
INSERT INTO `sensitive_word_log` VALUES (10, 109, 'TiAmo', '弹幕管理', '拒绝', 'http://************:8080/profile/upload/2025/07/16/NYxfGiWl3F8q9950e25ac58c9e0c863afe16fd668b76_20250716153637A002.jpeg', 'http://************:8080/profile/upload/2025/07/16/NYxfGiWl3F8q9950e25ac58c9e0c863afe16fd668b76_20250716153637A002.jpeg', '[\"20250716153637\"]', 1, '*************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2503300 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/1385', '2025-07-17 17:21:48');
INSERT INTO `sensitive_word_log` VALUES (11, 1, 'admin', '弹幕管理', '拒绝', '习近平', '习近平', '[\"习近平\"]', 1, '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-17 17:28:04');
INSERT INTO `sensitive_word_log` VALUES (12, 1, 'admin', '弹幕管理', '拒绝', '习近平', '习近平', '[\"习近平\"]', 1, '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-17 17:29:50');
INSERT INTO `sensitive_word_log` VALUES (13, 1, 'admin', '弹幕管理', '拒绝', '习近平', '习近平', '[\"习近平\"]', 1, '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-17 17:33:37');
INSERT INTO `sensitive_word_log` VALUES (14, 1, 'admin', '弹幕管理', '拒绝', '习近平', '习近平', '[\"习近平\"]', 1, '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-17 17:50:48');
INSERT INTO `sensitive_word_log` VALUES (15, 1, 'admin', '弹幕管理', '拒绝', '我是习近平', '我是习近平', '[\"习近平\"]', 1, '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-17 17:51:01');
INSERT INTO `sensitive_word_log` VALUES (16, 1, 'admin', '弹幕管理', '拒绝', '我是习近平', '我是习近平', '[\"习近平\"]', 1, '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-17 17:51:02');
INSERT INTO `sensitive_word_log` VALUES (17, 1, 'admin', '弹幕管理', '拒绝', '我是习进平', '我是习进平', '[\"习进平\"]', 1, '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-17 17:51:10');
INSERT INTO `sensitive_word_log` VALUES (18, 1, 'admin', '弹幕管理', '拒绝', '我是习进平', '我是习进平', '[\"习进平\"]', 1, '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-17 17:52:16');
INSERT INTO `sensitive_word_log` VALUES (19, 109, 'TiAmo', '弹幕管理', '拒绝', '习近平', '习近平', '[\"习近平\"]', 1, '*************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2503300 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/1425', '2025-07-17 18:41:33');
INSERT INTO `sensitive_word_log` VALUES (20, 109, 'TiAmo', '弹幕管理', '拒绝', '习近平', '习近平', '[\"习近平\"]', 1, '*************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2503300 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/1425', '2025-07-17 18:41:36');
INSERT INTO `sensitive_word_log` VALUES (21, 109, 'TiAmo', '弹幕管理', '拒绝', '习近平', '习近平', '[\"习近平\"]', 1, '*************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2503300 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/1425', '2025-07-17 18:41:37');
INSERT INTO `sensitive_word_log` VALUES (22, 109, 'TiAmo', '弹幕管理', '拒绝', '十三点', '十三点', '[\"十三点\"]', 1, '*************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2503300 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/1425', '2025-07-17 18:42:46');
INSERT INTO `sensitive_word_log` VALUES (23, 109, 'TiAmo', '弹幕管理', '拒绝', '十三点', '十三点', '[\"十三点\"]', 1, '*************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2503300 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/1425', '2025-07-17 18:42:51');
INSERT INTO `sensitive_word_log` VALUES (24, 109, 'TiAmo', '弹幕管理', '拒绝', '毛泽东', '毛泽东', '[\"毛泽东\"]', 1, '*************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2503300 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/1433', '2025-07-17 18:52:55');
INSERT INTO `sensitive_word_log` VALUES (25, 109, 'TiAmo', '弹幕管理', '拒绝', '毛主席', '毛主席', '[\"毛主席\"]', 1, '*************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2503300 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/1433', '2025-07-17 18:52:59');
INSERT INTO `sensitive_word_log` VALUES (26, 109, 'TiAmo', '弹幕管理', '拒绝', '八嘎雅鹿', '八嘎雅鹿', '[\"八嘎\"]', 1, '*************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2503300 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/1433', '2025-07-17 18:53:04');
INSERT INTO `sensitive_word_log` VALUES (27, 1, 'admin', '通知管理', '替换', '<p>欢迎使用天津海棠科技创新小程序！<img src=\"/dev-api/profile/upload/2025/07/18/09fa24b9a06f4f69b8284ca56b67b2b3_20250718112452A001.jpg\"></p>', '<p>欢迎使用天津海棠科技创新小程序！<img src=\"/dev-api/profile/upload/2025/07/18/09fa24b9a06f4f69b8284ca56b67b**3_20250718112452A001.jpg\"></p>', '[\"2b\"]', 1, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-18 11:24:58');

-- ----------------------------
-- Table structure for sys_config
-- ----------------------------
DROP TABLE IF EXISTS `sys_config`;
CREATE TABLE `sys_config`  (
  `config_id` int NOT NULL AUTO_INCREMENT COMMENT '参数主键',
  `config_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '参数名称',
  `config_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '参数键名',
  `config_value` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '参数键值',
  `config_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT 'N' COMMENT '系统内置（Y是 N否）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`config_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 125 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci COMMENT = '参数配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_config
-- ----------------------------
INSERT INTO `sys_config` VALUES (1, '主框架页-默认皮肤样式名称', 'sys.index.skinName', 'skin-blue', 'Y', 'admin', '2025-07-04 16:01:55', '', NULL, '蓝色 skin-blue、绿色 skin-green、紫色 skin-purple、红色 skin-red、黄色 skin-yellow');
INSERT INTO `sys_config` VALUES (2, '用户管理-账号初始密码', 'sys.user.initPassword', '123456', 'Y', 'admin', '2025-07-04 16:01:55', '', NULL, '初始化密码 123456');
INSERT INTO `sys_config` VALUES (3, '主框架页-侧边栏主题', 'sys.index.sideTheme', 'theme-dark', 'Y', 'admin', '2025-07-04 16:01:55', '', NULL, '深色主题theme-dark，浅色主题theme-light');
INSERT INTO `sys_config` VALUES (4, '账号自助-验证码开关', 'sys.account.captchaEnabled', 'true', 'Y', 'admin', '2025-07-04 16:01:55', '', NULL, '是否开启验证码功能（true开启，false关闭）');
INSERT INTO `sys_config` VALUES (5, '账号自助-是否开启用户注册功能', 'sys.account.registerUser', 'false', 'Y', 'admin', '2025-07-04 16:01:55', '', NULL, '是否开启注册用户功能（true开启，false关闭）');
INSERT INTO `sys_config` VALUES (6, '用户登录-黑名单列表', 'sys.login.blackIPList', '', 'Y', 'admin', '2025-07-04 16:01:55', '', NULL, '设置登录IP黑名单限制，多个匹配项以;分隔，支持匹配（*通配、网段）');
INSERT INTO `sys_config` VALUES (7, '用户管理-初始密码修改策略', 'sys.account.initPasswordModify', '1', 'Y', 'admin', '2025-07-04 16:01:55', '', NULL, '0：初始密码修改策略关闭，没有任何提示，1：提醒用户，如果未修改初始密码，则在登录时就会提醒修改密码对话框');
INSERT INTO `sys_config` VALUES (8, '用户管理-账号密码更新周期', 'sys.account.passwordValidateDays', '0', 'Y', 'admin', '2025-07-04 16:01:55', '', NULL, '密码更新周期（填写数字，数据初始化值为0不限制，若修改必须为大于0小于365的正整数），如果超过这个周期登录系统时，则在登录时就会提醒修改密码对话框');
INSERT INTO `sys_config` VALUES (104, '弹幕行数', 'danmaku.display.rows', '3', 'Y', 'admin', '2025-07-18 09:37:02', 'admin', '2025-07-23 12:38:02', '键值代表行数,最小值为1，最大值为3');
INSERT INTO `sys_config` VALUES (105, '弹幕速度', 'danmaku.scroll.speed', '11', 'Y', 'admin', '2025-07-18 09:42:10', 'admin', '2025-07-23 12:38:02', '弹幕从右侧滚动到左侧的时间，不能为负数，数值越小，速度越快');
INSERT INTO `sys_config` VALUES (106, '弹幕间隔', 'danmaku.send.interval', '5', 'Y', 'admin', '2025-07-18 09:49:12', 'admin', '2025-07-23 12:38:02', '弹幕的发送时间间隔，时间越小，发送的越块');
INSERT INTO `sys_config` VALUES (111, '新闻中心-微信公众号AppID', 'news.wechat.appid', 'wx9d4c3e709806efdd', 'Y', 'admin', '2025-07-18 14:37:46', 'admin', '2025-07-18 17:02:23', '新闻中心使用的微信公众号AppID，为空时使用全局配置');
INSERT INTO `sys_config` VALUES (112, '新闻中心-微信公众号Secret', 'news.wechat.secret', '4cf07c974d153811dac3f8725224c7f5', 'Y', 'admin', '2025-07-18 14:37:46', 'admin', '2025-07-18 17:02:34', '新闻中心使用的微信公众号Secret，为空时使用全局配置');
INSERT INTO `sys_config` VALUES (115, '微信公众号活动配置', 'wechat.activity.configs', '[{\"appId\":\"wx9d4c3e709806efdd\",\"appSecret\":\"4cf07c974d153811dac3f8725224c7f5\",\"configId\":\"627865e6-2353-4833-8b6e-b65c562fc210\",\"enabled\":true,\"lastSyncTime\":\"2025-07-29 15:28:23\",\"name\":\"宇悦灵识\"}]', 'N', 'admin', '2025-07-18 16:50:22', 'admin', '2025-07-29 07:28:23', '微信公众号活动同步配置，JSON格式存储多个公众号配置');
INSERT INTO `sys_config` VALUES (116, '微信同步最大页数', 'wechat.sync.max_pages', '50', 'N', 'admin', '2025-07-18 17:32:20', '', NULL, '微信文章同步时的最大页数限制，每页20条，防止同步过多文章');
INSERT INTO `sys_config` VALUES (118, '微信同步启用状态', 'wechat.sync.enabled', 'true', 'N', 'admin', '2025-07-18 17:32:20', '', NULL, '是否启用微信文章自动同步功能');
INSERT INTO `sys_config` VALUES (119, '项目报名赞助商图片', 'miniapp.project.sponsor.image', 'http://localhost:8086/profile/upload/2025/07/28/wallhaven-5w6gx1_2560x1440_20250728113902A002.png', 'N', '', '2025-07-22 15:03:16', '', '2025-07-28 03:39:05', '项目报名模块的赞助商图片URL');
INSERT INTO `sys_config` VALUES (120, '需求对接联系方式开关', 'demand.contact.use.admin', 'true', 'N', 'admin', '2025-07-27 05:29:10', '', NULL, '控制需求对接时是否使用后台配置的联系方式。true-使用后台配置，false-使用需求发布人联系方式');
INSERT INTO `sys_config` VALUES (121, '需求对接联系人姓名', 'demand.contact.admin.name', '客服小助手', 'N', 'admin', '2025-07-27 05:29:15', '', NULL, '需求对接时显示的后台配置联系人姓名');
INSERT INTO `sys_config` VALUES (122, '需求对接联系人手机', 'demand.contact.admin.phone', '************', 'N', 'admin', '2025-07-27 05:29:20', '', NULL, '需求对接时显示的后台配置联系人手机号');
INSERT INTO `sys_config` VALUES (123, '园区简介图片', 'miniapp.park.intro.image', 'http://************:8086/profile/upload/2025/07/28/3b16ee137013427c98a906b91048bc6621_20250728175310A003.jpg', 'N', 'admin', '2025-07-28 03:01:57', '', '2025-07-28 09:53:13', '园区管理模块的简介图片URL');

-- ----------------------------
-- Table structure for sys_dept
-- ----------------------------
DROP TABLE IF EXISTS `sys_dept`;
CREATE TABLE `sys_dept`  (
  `dept_id` bigint NOT NULL AUTO_INCREMENT COMMENT '部门id',
  `parent_id` bigint NULL DEFAULT 0 COMMENT '父部门id',
  `ancestors` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '祖级列表',
  `dept_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '部门名称',
  `order_num` int NULL DEFAULT 0 COMMENT '显示顺序',
  `leader` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '负责人',
  `phone` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '联系电话',
  `email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '邮箱',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '0' COMMENT '部门状态（0正常 1停用）',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`dept_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 200 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci COMMENT = '部门表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_dept
-- ----------------------------
INSERT INTO `sys_dept` VALUES (100, 0, '0', '天大海棠创新创业中心', 0, '刘璐', '15888888888', '', '0', '0', 'admin', '2025-07-04 16:01:55', 'admin', '2025-07-09 17:35:40');
INSERT INTO `sys_dept` VALUES (101, 100, '0,100', '开发部', 1, '', '15888888888', '', '0', '0', 'admin', '2025-07-04 16:01:55', 'admin', '2025-07-10 11:42:07');
INSERT INTO `sys_dept` VALUES (102, 100, '0,100', '投资部', 2, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', '2025-07-04 16:01:55', 'admin', '2025-07-10 11:42:13');
INSERT INTO `sys_dept` VALUES (103, 101, '0,100,101', '研发部门', 1, '若依', '15888888888', '<EMAIL>', '0', '2', 'admin', '2025-07-04 16:01:55', '', NULL);
INSERT INTO `sys_dept` VALUES (104, 101, '0,100,101', '市场部门', 2, '若依', '15888888888', '<EMAIL>', '0', '2', 'admin', '2025-07-04 16:01:55', '', NULL);
INSERT INTO `sys_dept` VALUES (105, 101, '0,100,101', '测试部门', 3, '若依', '15888888888', '<EMAIL>', '0', '2', 'admin', '2025-07-04 16:01:55', '', NULL);
INSERT INTO `sys_dept` VALUES (106, 101, '0,100,101', '财务部门', 4, '若依', '15888888888', '<EMAIL>', '0', '2', 'admin', '2025-07-04 16:01:55', '', NULL);
INSERT INTO `sys_dept` VALUES (107, 101, '0,100,101', '运维部门', 5, '若依', '15888888888', '<EMAIL>', '0', '2', 'admin', '2025-07-04 16:01:55', '', NULL);
INSERT INTO `sys_dept` VALUES (108, 102, '0,100,102', '市场部门', 1, '若依', '15888888888', '<EMAIL>', '0', '2', 'admin', '2025-07-04 16:01:55', '', NULL);
INSERT INTO `sys_dept` VALUES (109, 102, '0,100,102', '财务部门', 2, '若依', '15888888888', '<EMAIL>', '0', '2', 'admin', '2025-07-04 16:01:55', '', NULL);

-- ----------------------------
-- Table structure for sys_dict_data
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict_data`;
CREATE TABLE `sys_dict_data`  (
  `dict_code` bigint NOT NULL AUTO_INCREMENT COMMENT '字典编码',
  `dict_sort` int NULL DEFAULT 0 COMMENT '字典排序',
  `dict_label` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '字典标签',
  `dict_value` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '字典键值',
  `dict_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '字典类型',
  `css_class` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '样式属性（其他样式扩展）',
  `list_class` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '表格回显样式',
  `is_default` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT 'N' COMMENT '是否默认（Y是 N否）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`dict_code`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 124 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci COMMENT = '字典数据表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_dict_data
-- ----------------------------
INSERT INTO `sys_dict_data` VALUES (1, 1, '男', '0', 'sys_user_sex', '', '', 'Y', '0', 'admin', '2025-07-04 16:01:55', '', NULL, '性别男');
INSERT INTO `sys_dict_data` VALUES (2, 2, '女', '1', 'sys_user_sex', '', '', 'N', '0', 'admin', '2025-07-04 16:01:55', '', NULL, '性别女');
INSERT INTO `sys_dict_data` VALUES (3, 3, '未知', '2', 'sys_user_sex', '', '', 'N', '0', 'admin', '2025-07-04 16:01:55', '', NULL, '性别未知');
INSERT INTO `sys_dict_data` VALUES (4, 1, '显示', '0', 'sys_show_hide', '', 'primary', 'Y', '0', 'admin', '2025-07-04 16:01:55', '', NULL, '显示菜单');
INSERT INTO `sys_dict_data` VALUES (5, 2, '隐藏', '1', 'sys_show_hide', '', 'danger', 'N', '0', 'admin', '2025-07-04 16:01:55', '', NULL, '隐藏菜单');
INSERT INTO `sys_dict_data` VALUES (6, 1, '正常', '0', 'sys_normal_disable', '', 'success', 'Y', '0', 'admin', '2025-07-04 16:01:55', '', NULL, '正常状态');
INSERT INTO `sys_dict_data` VALUES (7, 2, '停用', '1', 'sys_normal_disable', '', 'danger', 'N', '0', 'admin', '2025-07-04 16:01:55', '', NULL, '停用状态');
INSERT INTO `sys_dict_data` VALUES (8, 1, '正常', '0', 'sys_job_status', '', 'primary', 'Y', '0', 'admin', '2025-07-04 16:01:55', '', NULL, '正常状态');
INSERT INTO `sys_dict_data` VALUES (9, 2, '暂停', '1', 'sys_job_status', '', 'danger', 'N', '0', 'admin', '2025-07-04 16:01:55', '', NULL, '停用状态');
INSERT INTO `sys_dict_data` VALUES (10, 1, '默认', 'DEFAULT', 'sys_job_group', '', '', 'Y', '0', 'admin', '2025-07-04 16:01:55', '', NULL, '默认分组');
INSERT INTO `sys_dict_data` VALUES (11, 2, '系统', 'SYSTEM', 'sys_job_group', '', '', 'N', '0', 'admin', '2025-07-04 16:01:55', '', NULL, '系统分组');
INSERT INTO `sys_dict_data` VALUES (12, 1, '是', 'Y', 'sys_yes_no', '', 'primary', 'Y', '0', 'admin', '2025-07-04 16:01:55', '', NULL, '系统默认是');
INSERT INTO `sys_dict_data` VALUES (13, 2, '否', 'N', 'sys_yes_no', '', 'danger', 'N', '0', 'admin', '2025-07-04 16:01:55', '', NULL, '系统默认否');
INSERT INTO `sys_dict_data` VALUES (14, 1, '通知', '1', 'sys_notice_type', '', 'warning', 'Y', '0', 'admin', '2025-07-04 16:01:55', '', NULL, '通知');
INSERT INTO `sys_dict_data` VALUES (15, 2, '公告', '2', 'sys_notice_type', '', 'success', 'N', '0', 'admin', '2025-07-04 16:01:55', '', NULL, '公告');
INSERT INTO `sys_dict_data` VALUES (16, 1, '正常', '0', 'sys_notice_status', '', 'primary', 'Y', '0', 'admin', '2025-07-04 16:01:55', '', NULL, '正常状态');
INSERT INTO `sys_dict_data` VALUES (17, 2, '关闭', '1', 'sys_notice_status', '', 'danger', 'N', '0', 'admin', '2025-07-04 16:01:55', '', NULL, '关闭状态');
INSERT INTO `sys_dict_data` VALUES (18, 99, '其他', '0', 'sys_oper_type', '', 'info', 'N', '0', 'admin', '2025-07-04 16:01:55', '', NULL, '其他操作');
INSERT INTO `sys_dict_data` VALUES (19, 1, '新增', '1', 'sys_oper_type', '', 'info', 'N', '0', 'admin', '2025-07-04 16:01:55', '', NULL, '新增操作');
INSERT INTO `sys_dict_data` VALUES (20, 2, '修改', '2', 'sys_oper_type', '', 'info', 'N', '0', 'admin', '2025-07-04 16:01:55', '', NULL, '修改操作');
INSERT INTO `sys_dict_data` VALUES (21, 3, '删除', '3', 'sys_oper_type', '', 'danger', 'N', '0', 'admin', '2025-07-04 16:01:55', '', NULL, '删除操作');
INSERT INTO `sys_dict_data` VALUES (22, 4, '授权', '4', 'sys_oper_type', '', 'primary', 'N', '0', 'admin', '2025-07-04 16:01:55', '', NULL, '授权操作');
INSERT INTO `sys_dict_data` VALUES (23, 5, '导出', '5', 'sys_oper_type', '', 'warning', 'N', '0', 'admin', '2025-07-04 16:01:55', '', NULL, '导出操作');
INSERT INTO `sys_dict_data` VALUES (24, 6, '导入', '6', 'sys_oper_type', '', 'warning', 'N', '0', 'admin', '2025-07-04 16:01:55', '', NULL, '导入操作');
INSERT INTO `sys_dict_data` VALUES (25, 7, '强退', '7', 'sys_oper_type', '', 'danger', 'N', '0', 'admin', '2025-07-04 16:01:55', '', NULL, '强退操作');
INSERT INTO `sys_dict_data` VALUES (26, 8, '生成代码', '8', 'sys_oper_type', '', 'warning', 'N', '0', 'admin', '2025-07-04 16:01:55', '', NULL, '生成操作');
INSERT INTO `sys_dict_data` VALUES (27, 9, '清空数据', '9', 'sys_oper_type', '', 'danger', 'N', '0', 'admin', '2025-07-04 16:01:55', '', NULL, '清空操作');
INSERT INTO `sys_dict_data` VALUES (28, 1, '成功', '0', 'sys_common_status', '', 'primary', 'N', '0', 'admin', '2025-07-04 16:01:55', '', NULL, '正常状态');
INSERT INTO `sys_dict_data` VALUES (29, 2, '失败', '1', 'sys_common_status', '', 'danger', 'N', '0', 'admin', '2025-07-04 16:01:55', '', NULL, '停用状态');
INSERT INTO `sys_dict_data` VALUES (100, 1, '首页', 'home', 'miniapp_page_type', '', 'primary', 'Y', '0', 'admin', '2025-07-07 14:18:09', '', NULL, '小程序首页');
INSERT INTO `sys_dict_data` VALUES (101, 2, '创赛路演', 'roadshow', 'miniapp_page_type', '', 'success', 'N', '0', 'admin', '2025-07-07 14:18:09', 'admin', '2025-07-28 08:40:35', '创赛top图');
INSERT INTO `sys_dict_data` VALUES (102, 3, '海棠杯', 'htcup', 'miniapp_page_type', '', 'info', 'N', '0', 'admin', '2025-07-07 14:18:09', 'admin', '2025-07-21 19:06:46', '海棠杯top图');
INSERT INTO `sys_dict_data` VALUES (105, 6, '项目报名', 'projectApply', 'miniapp_page_type', '', 'success', 'N', '0', 'admin', '2025-07-07 14:18:09', 'admin', '2025-07-22 14:38:06', '项目报名页面');
INSERT INTO `sys_dict_data` VALUES (106, 1, '系统用户', '00', 'sys_user_type', '', 'primary', 'Y', '0', 'admin', '2025-07-09 16:35:24', '', NULL, '系统管理用户');
INSERT INTO `sys_dict_data` VALUES (107, 2, '小程序用户', '01', 'sys_user_type', '', 'success', 'N', '0', 'admin', '2025-07-09 16:35:24', '', NULL, '微信小程序用户');
INSERT INTO `sys_dict_data` VALUES (108, 3, '混合用户', '02', 'sys_user_type', '', 'info', 'N', '0', 'admin', '2025-07-09 16:35:24', '', NULL, '既是系统用户又是小程序用户');
INSERT INTO `sys_dict_data` VALUES (109, 1, '高中及以下', 'high_school', 'education_level', '', '', 'N', '0', 'admin', '2025-07-09 16:35:44', '', NULL, '高中及以下学历');
INSERT INTO `sys_dict_data` VALUES (110, 2, '专科', 'college', 'education_level', '', '', 'N', '0', 'admin', '2025-07-09 16:35:44', '', NULL, '专科学历');
INSERT INTO `sys_dict_data` VALUES (111, 3, '本科', 'bachelor', 'education_level', '', '', 'Y', '0', 'admin', '2025-07-09 16:35:44', '', NULL, '本科学历');
INSERT INTO `sys_dict_data` VALUES (112, 4, '硕士', 'master', 'education_level', '', '', 'N', '0', 'admin', '2025-07-09 16:35:44', '', NULL, '硕士学历');
INSERT INTO `sys_dict_data` VALUES (113, 5, '博士', 'doctor', 'education_level', '', '', 'N', '0', 'admin', '2025-07-09 16:35:44', '', NULL, '博士学历');
INSERT INTO `sys_dict_data` VALUES (114, 6, '博士后', 'postdoc', 'education_level', '', '', 'N', '0', 'admin', '2025-07-09 16:35:44', '', NULL, '博士后');
INSERT INTO `sys_dict_data` VALUES (115, 1, '融资需求', '1', 'mini_demand_type', '', 'primary', 'Y', '0', 'admin', '2025-07-23 15:16:55', '', NULL, '企业融资相关需求');
INSERT INTO `sys_dict_data` VALUES (116, 2, '技术合作', '2', 'mini_demand_type', '', 'success', 'N', '0', 'admin', '2025-07-23 15:16:55', '', NULL, '技术开发与合作需求');
INSERT INTO `sys_dict_data` VALUES (117, 3, '市场推广', '3', 'mini_demand_type', '', 'info', 'N', '0', 'admin', '2025-07-23 15:16:55', '', NULL, '市场营销推广需求');
INSERT INTO `sys_dict_data` VALUES (118, 4, '人才招聘', '4', 'mini_demand_type', '', 'warning', 'N', '0', 'admin', '2025-07-23 15:16:55', '', NULL, '人才招聘相关需求');
INSERT INTO `sys_dict_data` VALUES (119, 5, '商务合作', '5', 'mini_demand_type', '', 'danger', 'N', '0', 'admin', '2025-07-23 15:16:55', '', NULL, '商务合作伙伴需求');
INSERT INTO `sys_dict_data` VALUES (120, 6, '其他需求', '6', 'mini_demand_type', '', 'default', 'N', '0', 'admin', '2025-07-23 15:16:55', '', NULL, '其他类型需求');
INSERT INTO `sys_dict_data` VALUES (121, 7, '园区', 'garden', 'miniapp_page_type', NULL, 'info', 'N', '0', 'admin', '2025-07-28 08:35:52', 'admin', '2025-07-28 08:36:32', NULL);
INSERT INTO `sys_dict_data` VALUES (122, 8, '西青金种子top图', 'xq_golden', 'miniapp_page_type', NULL, 'info', 'N', '0', 'admin', '2025-07-28 08:43:18', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (123, 10, '专家矩阵', 'expert', 'miniapp_page_type', NULL, 'info', 'N', '0', 'admin', '2025-07-29 01:36:25', '', NULL, '专家矩阵页面');

-- ----------------------------
-- Table structure for sys_dict_type
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict_type`;
CREATE TABLE `sys_dict_type`  (
  `dict_id` bigint NOT NULL AUTO_INCREMENT COMMENT '字典主键',
  `dict_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '字典名称',
  `dict_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '字典类型',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`dict_id`) USING BTREE,
  UNIQUE INDEX `dict_type`(`dict_type` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 104 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci COMMENT = '字典类型表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_dict_type
-- ----------------------------
INSERT INTO `sys_dict_type` VALUES (1, '用户性别', 'sys_user_sex', '0', 'admin', '2025-07-04 16:01:55', 'admin', '2025-07-09 09:41:24', '用户性别列表');
INSERT INTO `sys_dict_type` VALUES (2, '菜单状态', 'sys_show_hide', '0', 'admin', '2025-07-04 16:01:55', '', NULL, '菜单状态列表');
INSERT INTO `sys_dict_type` VALUES (3, '系统开关', 'sys_normal_disable', '0', 'admin', '2025-07-04 16:01:55', 'admin', '2025-07-09 09:29:56', '系统开关列表');
INSERT INTO `sys_dict_type` VALUES (4, '任务状态', 'sys_job_status', '0', 'admin', '2025-07-04 16:01:55', '', NULL, '任务状态列表');
INSERT INTO `sys_dict_type` VALUES (5, '任务分组', 'sys_job_group', '0', 'admin', '2025-07-04 16:01:55', '', NULL, '任务分组列表');
INSERT INTO `sys_dict_type` VALUES (6, '系统是否', 'sys_yes_no', '0', 'admin', '2025-07-04 16:01:55', '', NULL, '系统是否列表');
INSERT INTO `sys_dict_type` VALUES (7, '通知类型', 'sys_notice_type', '0', 'admin', '2025-07-04 16:01:55', '', NULL, '通知类型列表');
INSERT INTO `sys_dict_type` VALUES (8, '通知状态', 'sys_notice_status', '0', 'admin', '2025-07-04 16:01:55', '', NULL, '通知状态列表');
INSERT INTO `sys_dict_type` VALUES (9, '操作类型', 'sys_oper_type', '0', 'admin', '2025-07-04 16:01:55', '', NULL, '操作类型列表');
INSERT INTO `sys_dict_type` VALUES (10, '系统状态', 'sys_common_status', '0', 'admin', '2025-07-04 16:01:55', '', NULL, '登录状态列表');
INSERT INTO `sys_dict_type` VALUES (100, '页面类型', 'miniapp_page_type', '0', 'admin', '2025-07-07 14:17:59', '', NULL, '小程序页面类型字典');
INSERT INTO `sys_dict_type` VALUES (101, '用户类型', 'sys_user_type', '0', 'admin', '2025-07-09 16:35:13', '', NULL, '系统用户类型列表');
INSERT INTO `sys_dict_type` VALUES (102, '学历层次', 'education_level', '0', 'admin', '2025-07-09 16:35:33', '', NULL, '学历层次列表');
INSERT INTO `sys_dict_type` VALUES (103, '需求类型', 'mini_demand_type', '0', 'admin', '2025-07-23 15:16:42', '', NULL, '小程序需求信息的类型分类');

-- ----------------------------
-- Table structure for sys_job
-- ----------------------------
DROP TABLE IF EXISTS `sys_job`;
CREATE TABLE `sys_job`  (
  `job_id` bigint NOT NULL AUTO_INCREMENT COMMENT '任务ID',
  `job_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '' COMMENT '任务名称',
  `job_group` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT 'DEFAULT' COMMENT '任务组名',
  `invoke_target` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '调用目标字符串',
  `cron_expression` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT 'cron执行表达式',
  `misfire_policy` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '3' COMMENT '计划执行错误策略（1立即执行 2执行一次 3放弃执行）',
  `concurrent` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '1' COMMENT '是否并发执行（0允许 1禁止）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '0' COMMENT '状态（0正常 1暂停）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '备注信息',
  PRIMARY KEY (`job_id`, `job_name`, `job_group`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 101 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci COMMENT = '定时任务调度表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_job
-- ----------------------------
INSERT INTO `sys_job` VALUES (1, '系统默认（无参）', 'DEFAULT', 'ryTask.ryNoParams', '0/10 * * * * ?', '3', '1', '1', 'admin', '2025-07-04 16:01:55', '', NULL, '');
INSERT INTO `sys_job` VALUES (2, '系统默认（有参）', 'DEFAULT', 'ryTask.ryParams(\'ry\')', '0/15 * * * * ?', '3', '1', '1', 'admin', '2025-07-04 16:01:55', '', NULL, '');
INSERT INTO `sys_job` VALUES (3, '系统默认（多参）', 'DEFAULT', 'ryTask.ryMultipleParams(\'ry\', true, 2000L, 316.50D, 100)', '0/20 * * * * ?', '3', '1', '1', 'admin', '2025-07-04 16:01:55', '', NULL, '');
INSERT INTO `sys_job` VALUES (100, '微信公众号文章同步', 'DEFAULT', 'wechatSyncTask.syncWechatArticles', '0 0 */2 * * ?', '3', '1', '1', 'admin', '2025-07-17 10:20:09', '', NULL, '每2小时同步一次微信公众号文章');

-- ----------------------------
-- Table structure for sys_job_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_job_log`;
CREATE TABLE `sys_job_log`  (
  `job_log_id` bigint NOT NULL AUTO_INCREMENT COMMENT '任务日志ID',
  `job_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '任务名称',
  `job_group` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '任务组名',
  `invoke_target` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '调用目标字符串',
  `job_message` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '日志信息',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '0' COMMENT '执行状态（0正常 1失败）',
  `exception_info` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '异常信息',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`job_log_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci COMMENT = '定时任务调度日志表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_job_log
-- ----------------------------

-- ----------------------------
-- Table structure for sys_logininfor
-- ----------------------------
DROP TABLE IF EXISTS `sys_logininfor`;
CREATE TABLE `sys_logininfor`  (
  `info_id` bigint NOT NULL AUTO_INCREMENT COMMENT '访问ID',
  `user_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '用户账号',
  `ipaddr` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '登录IP地址',
  `login_location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '登录地点',
  `browser` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '浏览器类型',
  `os` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '操作系统',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '0' COMMENT '登录状态（0成功 1失败）',
  `msg` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '提示消息',
  `login_time` datetime NULL DEFAULT NULL COMMENT '访问时间',
  PRIMARY KEY (`info_id`) USING BTREE,
  INDEX `idx_sys_logininfor_s`(`status` ASC) USING BTREE,
  INDEX `idx_sys_logininfor_lt`(`login_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 284 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci COMMENT = '系统访问记录' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_logininfor
-- ----------------------------

-- ----------------------------
-- Table structure for sys_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_menu`;
CREATE TABLE `sys_menu`  (
  `menu_id` bigint NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
  `menu_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '菜单名称',
  `parent_id` bigint NULL DEFAULT 0 COMMENT '父菜单ID',
  `order_num` int NULL DEFAULT 0 COMMENT '显示顺序',
  `path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '路由地址',
  `component` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '组件路径',
  `query` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '路由参数',
  `route_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '路由名称',
  `is_frame` int NULL DEFAULT 1 COMMENT '是否为外链（0是 1否）',
  `is_cache` int NULL DEFAULT 0 COMMENT '是否缓存（0缓存 1不缓存）',
  `menu_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '菜单类型（M目录 C菜单 F按钮）',
  `visible` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '0' COMMENT '菜单状态（0显示 1隐藏）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '0' COMMENT '菜单状态（0正常 1停用）',
  `perms` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '权限标识',
  `icon` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '#' COMMENT '菜单图标',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '备注',
  PRIMARY KEY (`menu_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 216553 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci COMMENT = '菜单权限表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_menu
-- ----------------------------
INSERT INTO `sys_menu` VALUES (1, '系统管理', 0, 1, 'system', NULL, '', '', 1, 0, 'M', '0', '0', '', 'system', 'admin', '2025-07-04 16:01:55', '', NULL, '系统管理目录');
INSERT INTO `sys_menu` VALUES (2, '系统监控', 0, 2, 'monitor', NULL, NULL, '', 1, 0, 'M', '0', '0', '', 'monitor', 'admin', '2025-07-10 10:27:37', '', NULL, '系统监控目录');
INSERT INTO `sys_menu` VALUES (3, '系统工具', 0, 2, 'tool', NULL, NULL, '', 1, 0, 'M', '0', '0', '', 'tool', 'admin', '2025-07-10 10:26:47', 'admin', '2025-07-14 14:49:35', '系统工具目录');
INSERT INTO `sys_menu` VALUES (100, '用户管理', 1, 1, 'user', 'system/user/index', '', '', 1, 0, 'C', '0', '0', 'system:user:list', 'user', 'admin', '2025-07-04 16:01:55', '', NULL, '用户管理菜单');
INSERT INTO `sys_menu` VALUES (101, '角色管理', 1, 2, 'role', 'system/role/index', '', '', 1, 0, 'C', '0', '0', 'system:role:list', 'peoples', 'admin', '2025-07-04 16:01:55', '', NULL, '角色管理菜单');
INSERT INTO `sys_menu` VALUES (102, '菜单管理', 1, 3, 'menu', 'system/menu/index', '', '', 1, 0, 'C', '0', '0', 'system:menu:list', 'tree-table', 'admin', '2025-07-04 16:01:55', '', NULL, '菜单管理菜单');
INSERT INTO `sys_menu` VALUES (103, '部门管理', 1, 4, 'dept', 'system/dept/index', NULL, '', 1, 0, 'C', '0', '0', 'system:dept:list', 'tree', 'admin', '2025-07-10 10:27:25', '', NULL, '部门管理菜单');
INSERT INTO `sys_menu` VALUES (104, '岗位管理', 1, 5, 'post', 'system/post/index', NULL, '', 1, 0, 'C', '0', '0', 'system:post:list', 'post', 'admin', '2025-07-10 10:27:11', '', NULL, '岗位管理菜单');
INSERT INTO `sys_menu` VALUES (105, '字典管理', 1, 6, 'dict', 'system/dict/index', '', '', 1, 0, 'C', '0', '0', 'system:dict:list', 'dict', 'admin', '2025-07-04 16:01:55', '', NULL, '字典管理菜单');
INSERT INTO `sys_menu` VALUES (106, '参数设置', 1, 7, 'config', 'system/config/index', '', '', 1, 0, 'C', '0', '0', 'system:config:list', 'edit', 'admin', '2025-07-04 16:01:55', '', NULL, '参数设置菜单');
INSERT INTO `sys_menu` VALUES (107, '通知公告', 1, 8, 'notice', 'system/notice/index', '', '', 1, 0, 'C', '0', '0', 'system:notice:list', 'message', 'admin', '2025-07-04 16:01:55', '', NULL, '通知公告菜单');
INSERT INTO `sys_menu` VALUES (108, '日志管理', 1, 9, 'log', '', '', '', 1, 0, 'M', '0', '0', '', 'log', 'admin', '2025-07-04 16:01:55', '', NULL, '日志管理菜单');
INSERT INTO `sys_menu` VALUES (109, '在线用户', 2, 1, 'online', 'monitor/online/index', NULL, '', 1, 0, 'C', '0', '0', 'monitor:online:list', 'online', 'admin', '2025-07-10 10:27:48', '', NULL, '在线用户菜单');
INSERT INTO `sys_menu` VALUES (110, '定时任务', 2, 2, 'job', 'monitor/job/index', NULL, '', 1, 0, 'C', '0', '0', 'monitor:job:list', 'job', 'admin', '2025-07-10 10:27:48', '', NULL, '定时任务菜单');
INSERT INTO `sys_menu` VALUES (111, '数据监控', 2, 3, 'druid', 'monitor/druid/index', NULL, '', 1, 0, 'C', '0', '0', 'monitor:druid:list', 'druid', 'admin', '2025-07-10 10:27:48', '', NULL, '数据监控菜单');
INSERT INTO `sys_menu` VALUES (112, '服务监控', 2, 4, 'server', 'monitor/server/index', NULL, '', 1, 0, 'C', '0', '0', 'monitor:server:list', 'server', 'admin', '2025-07-10 10:27:48', '', NULL, '服务监控菜单');
INSERT INTO `sys_menu` VALUES (113, '缓存监控', 2, 5, 'cache', 'monitor/cache/index', NULL, '', 1, 0, 'C', '0', '0', 'monitor:cache:list', 'redis', 'admin', '2025-07-10 10:27:48', '', NULL, '缓存监控菜单');
INSERT INTO `sys_menu` VALUES (114, '缓存列表', 2, 6, 'cacheList', 'monitor/cache/list', NULL, '', 1, 0, 'C', '0', '0', 'monitor:cache:list', 'redis-list', 'admin', '2025-07-10 10:27:48', '', NULL, '缓存列表菜单');
INSERT INTO `sys_menu` VALUES (115, '表单构建', 3, 1, 'build', 'tool/build/index', NULL, '', 1, 0, 'C', '0', '0', 'tool:build:list', 'build', 'admin', '2025-07-10 10:26:55', '', NULL, '表单构建菜单');
INSERT INTO `sys_menu` VALUES (116, '代码生成', 3, 2, 'gen', 'tool/gen/index', NULL, '', 1, 0, 'C', '0', '0', 'tool:gen:list', 'code', 'admin', '2025-07-10 10:26:55', '', NULL, '代码生成菜单');
INSERT INTO `sys_menu` VALUES (117, '系统接口', 3, 3, 'swagger', 'tool/swagger/index', NULL, '', 1, 0, 'C', '0', '0', 'tool:swagger:list', 'swagger', 'admin', '2025-07-10 10:26:55', '', NULL, '系统接口菜单');
INSERT INTO `sys_menu` VALUES (500, '操作日志', 108, 1, 'operlog', 'monitor/operlog/index', '', '', 1, 0, 'C', '0', '0', 'monitor:operlog:list', 'form', 'admin', '2025-07-04 16:01:55', '', NULL, '操作日志菜单');
INSERT INTO `sys_menu` VALUES (501, '登录日志', 108, 2, 'logininfor', 'monitor/logininfor/index', '', '', 1, 0, 'C', '0', '0', 'monitor:logininfor:list', 'logininfor', 'admin', '2025-07-04 16:01:55', '', NULL, '登录日志菜单');
INSERT INTO `sys_menu` VALUES (1000, '用户查询', 100, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:query', '#', 'admin', '2025-07-04 16:01:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1001, '用户新增', 100, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:add', '#', 'admin', '2025-07-04 16:01:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1002, '用户修改', 100, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:edit', '#', 'admin', '2025-07-04 16:01:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1003, '用户删除', 100, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:remove', '#', 'admin', '2025-07-04 16:01:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1004, '用户导出', 100, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:export', '#', 'admin', '2025-07-04 16:01:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1005, '用户导入', 100, 6, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:import', '#', 'admin', '2025-07-04 16:01:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1006, '重置密码', 100, 7, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:resetPwd', '#', 'admin', '2025-07-04 16:01:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1007, '角色查询', 101, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:query', '#', 'admin', '2025-07-04 16:01:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1008, '角色新增', 101, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:add', '#', 'admin', '2025-07-04 16:01:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1009, '角色修改', 101, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:edit', '#', 'admin', '2025-07-04 16:01:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1010, '角色删除', 101, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:remove', '#', 'admin', '2025-07-04 16:01:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1011, '角色导出', 101, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:export', '#', 'admin', '2025-07-04 16:01:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1012, '菜单查询', 102, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:menu:query', '#', 'admin', '2025-07-04 16:01:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1013, '菜单新增', 102, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:menu:add', '#', 'admin', '2025-07-04 16:01:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1014, '菜单修改', 102, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:menu:edit', '#', 'admin', '2025-07-04 16:01:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1015, '菜单删除', 102, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:menu:remove', '#', 'admin', '2025-07-04 16:01:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1016, '部门查询', 103, 1, '', '', NULL, '', 1, 0, 'F', '0', '0', 'system:dept:query', '#', 'admin', '2025-07-10 10:27:31', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1017, '部门新增', 103, 2, '', '', NULL, '', 1, 0, 'F', '0', '0', 'system:dept:add', '#', 'admin', '2025-07-10 10:27:31', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1018, '部门修改', 103, 3, '', '', NULL, '', 1, 0, 'F', '0', '0', 'system:dept:edit', '#', 'admin', '2025-07-10 10:27:31', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1019, '部门删除', 103, 4, '', '', NULL, '', 1, 0, 'F', '0', '0', 'system:dept:remove', '#', 'admin', '2025-07-10 10:27:31', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1020, '岗位查询', 104, 1, '', '', NULL, '', 1, 0, 'F', '0', '0', 'system:post:query', '#', 'admin', '2025-07-10 10:27:18', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1021, '岗位新增', 104, 2, '', '', NULL, '', 1, 0, 'F', '0', '0', 'system:post:add', '#', 'admin', '2025-07-10 10:27:18', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1022, '岗位修改', 104, 3, '', '', NULL, '', 1, 0, 'F', '0', '0', 'system:post:edit', '#', 'admin', '2025-07-10 10:27:18', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1023, '岗位删除', 104, 4, '', '', NULL, '', 1, 0, 'F', '0', '0', 'system:post:remove', '#', 'admin', '2025-07-10 10:27:18', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1024, '岗位导出', 104, 5, '', '', NULL, '', 1, 0, 'F', '0', '0', 'system:post:export', '#', 'admin', '2025-07-10 10:27:18', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1025, '字典查询', 105, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:query', '#', 'admin', '2025-07-04 16:01:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1026, '字典新增', 105, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:add', '#', 'admin', '2025-07-04 16:01:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1027, '字典修改', 105, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:edit', '#', 'admin', '2025-07-04 16:01:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1028, '字典删除', 105, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:remove', '#', 'admin', '2025-07-04 16:01:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1029, '字典导出', 105, 5, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:export', '#', 'admin', '2025-07-04 16:01:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1030, '参数查询', 106, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:config:query', '#', 'admin', '2025-07-04 16:01:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1031, '参数新增', 106, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:config:add', '#', 'admin', '2025-07-04 16:01:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1032, '参数修改', 106, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:config:edit', '#', 'admin', '2025-07-04 16:01:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1033, '参数删除', 106, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:config:remove', '#', 'admin', '2025-07-04 16:01:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1034, '参数导出', 106, 5, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:config:export', '#', 'admin', '2025-07-04 16:01:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1035, '公告查询', 107, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:notice:query', '#', 'admin', '2025-07-04 16:01:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1036, '公告新增', 107, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:notice:add', '#', 'admin', '2025-07-04 16:01:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1037, '公告修改', 107, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:notice:edit', '#', 'admin', '2025-07-04 16:01:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1038, '公告删除', 107, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:notice:remove', '#', 'admin', '2025-07-04 16:01:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1039, '操作查询', 500, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:operlog:query', '#', 'admin', '2025-07-04 16:01:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1040, '操作删除', 500, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:operlog:remove', '#', 'admin', '2025-07-04 16:01:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1041, '日志导出', 500, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:operlog:export', '#', 'admin', '2025-07-04 16:01:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1042, '登录查询', 501, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:logininfor:query', '#', 'admin', '2025-07-04 16:01:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1043, '登录删除', 501, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:logininfor:remove', '#', 'admin', '2025-07-04 16:01:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1044, '日志导出', 501, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:logininfor:export', '#', 'admin', '2025-07-04 16:01:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1045, '账户解锁', 501, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:logininfor:unlock', '#', 'admin', '2025-07-04 16:01:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1046, '在线查询', 109, 1, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'monitor:online:query', '#', 'admin', '2025-07-10 10:28:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1047, '批量强退', 109, 2, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'monitor:online:batchLogout', '#', 'admin', '2025-07-10 10:28:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1048, '单条强退', 109, 3, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'monitor:online:forceLogout', '#', 'admin', '2025-07-10 10:28:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1049, '任务查询', 110, 1, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'monitor:job:query', '#', 'admin', '2025-07-10 10:28:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1050, '任务新增', 110, 2, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'monitor:job:add', '#', 'admin', '2025-07-10 10:28:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1051, '任务修改', 110, 3, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'monitor:job:edit', '#', 'admin', '2025-07-10 10:28:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1052, '任务删除', 110, 4, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'monitor:job:remove', '#', 'admin', '2025-07-10 10:28:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1053, '状态修改', 110, 5, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'monitor:job:changeStatus', '#', 'admin', '2025-07-10 10:28:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1054, '任务导出', 110, 6, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'monitor:job:export', '#', 'admin', '2025-07-10 10:28:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1055, '生成查询', 116, 1, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'tool:gen:query', '#', 'admin', '2025-07-10 10:27:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1056, '生成修改', 116, 2, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'tool:gen:edit', '#', 'admin', '2025-07-10 10:27:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1057, '生成删除', 116, 3, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'tool:gen:remove', '#', 'admin', '2025-07-10 10:27:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1058, '导入代码', 116, 4, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'tool:gen:import', '#', 'admin', '2025-07-10 10:27:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1059, '预览代码', 116, 5, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'tool:gen:preview', '#', 'admin', '2025-07-10 10:27:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1060, '生成代码', 116, 6, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'tool:gen:code', '#', 'admin', '2025-07-10 10:27:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2101, '数据统计', 0, 3, 'statistics', 'miniapp/statistics/index', NULL, '', 1, 0, 'C', '0', '0', 'miniapp:statistics:view', 'chart', 'admin', '2025-07-04 16:03:48', 'admin', '2025-07-10 09:44:25', '小程序数据统计');
INSERT INTO `sys_menu` VALUES (2110, '内容管理', 0, 4, 'content', NULL, NULL, '', 1, 0, 'M', '0', '0', '', 'edit', 'admin', '2025-07-04 16:03:48', 'admin', '2025-07-07 16:48:34', '内容管理目录');
INSERT INTO `sys_menu` VALUES (2111, '轮播图管理', 2110, 1, 'banner', 'miniapp/content/banner/index', NULL, '', 1, 0, 'C', '0', '0', 'miniapp:banner:list', 'star', 'admin', '2025-07-04 16:03:48', '', NULL, '轮播图管理');
INSERT INTO `sys_menu` VALUES (2112, '通知管理', 2110, 2, 'notice', 'miniapp/content/notice/index', NULL, '', 1, 0, 'C', '0', '0', 'miniapp:notice:list', 'message', 'admin', '2025-07-04 16:03:48', '', NULL, '通知管理');
INSERT INTO `sys_menu` VALUES (2113, '弹幕管理', 2110, 3, 'barrage', 'miniapp/content/barrage/index', NULL, '', 1, 0, 'C', '0', '0', 'miniapp:barrage:list', 'message', 'admin', '2025-07-04 16:03:48', '', NULL, '弹幕管理');
INSERT INTO `sys_menu` VALUES (2114, 'top图片管理', 2110, 4, 'topimage', 'miniapp/content/topimage/index', NULL, '', 1, 0, 'C', '0', '0', 'miniapp:topimage:list', 'picture', 'admin', '2025-07-07 10:17:01', '', NULL, 'top图片管理');
INSERT INTO `sys_menu` VALUES (2121, '精彩活动', 0, 9, 'activity', 'miniapp/business/activity/index', NULL, '', 1, 0, 'C', '0', '0', 'miniapp:activity:list', 'star', 'admin', '2025-07-04 16:03:48', 'admin', '2025-07-29 02:01:27', '精彩活动管理');
INSERT INTO `sys_menu` VALUES (2122, '活动报名', 0, 7, 'event', NULL, NULL, '', 1, 0, 'M', '0', '0', 'miniapp:event:list', 'form', 'admin', '2025-07-04 16:03:48', 'admin', '2025-07-29 01:59:54', '活动报名管理');
INSERT INTO `sys_menu` VALUES (2123, '需求广场', 0, 7, 'demand', NULL, NULL, '', 1, 0, 'M', '0', '0', 'miniapp:demand:list', 'shopping', 'admin', '2025-07-04 16:03:48', '', NULL, '需求对接管理');
INSERT INTO `sys_menu` VALUES (2124, '加入我们', 0, 10, 'job', 'miniapp/business/job/index', NULL, '', 1, 0, 'C', '0', '0', 'miniapp:job:list', 'user', 'admin', '2025-07-04 16:03:48', '', NULL, '招聘信息管理');
INSERT INTO `sys_menu` VALUES (2125, '科技之星', 0, 11, 'techstar', 'miniapp/business/techstar/index', NULL, '', 1, 0, 'C', '0', '0', 'miniapp:techstar:list', 'star', 'admin', '2025-07-04 16:03:48', '', NULL, '科技之星管理');
INSERT INTO `sys_menu` VALUES (2130, '配置管理', 0, 4, 'config', NULL, NULL, '', 1, 0, 'M', '0', '0', '', 'system', 'admin', '2025-07-04 16:03:48', '', NULL, '配置管理目录');
INSERT INTO `sys_menu` VALUES (2132, '页面内容', 2130, 4, 'page', 'miniapp/config/page/index', NULL, '', 1, 0, 'C', '0', '0', 'miniapp:page:list', 'documentation', 'admin', '2025-07-04 16:03:48', '', NULL, '页面内容管理');
INSERT INTO `sys_menu` VALUES (2140, '行业管理', 2130, 1, 'industry', 'miniapp/industry/index', NULL, '', 1, 0, 'C', '0', '0', 'miniapp:industry:list', 'tree', 'admin', '2025-07-05 11:49:09', '', NULL, '行业选择管理');
INSERT INTO `sys_menu` VALUES (21111, '轮播图查询', 2111, 1, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:banner:query', '#', 'admin', '2025-07-04 16:03:48', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21112, '轮播图新增', 2111, 2, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:banner:add', '#', 'admin', '2025-07-04 16:03:48', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21113, '轮播图修改', 2111, 3, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:banner:edit', '#', 'admin', '2025-07-04 16:03:48', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21114, '轮播图删除', 2111, 4, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:banner:remove', '#', 'admin', '2025-07-04 16:03:48', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21115, '轮播图导出', 2111, 5, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:banner:export', '#', 'admin', '2025-07-04 16:03:48', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21121, '通知查询', 2112, 1, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:notice:query', '#', 'admin', '2025-07-04 16:03:48', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21122, '通知新增', 2112, 2, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:notice:add', '#', 'admin', '2025-07-04 16:03:48', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21123, '通知修改', 2112, 3, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:notice:edit', '#', 'admin', '2025-07-04 16:03:48', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21124, '通知删除', 2112, 4, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:notice:remove', '#', 'admin', '2025-07-04 16:03:48', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21125, '通知导出', 2112, 5, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:notice:export', '#', 'admin', '2025-07-04 16:03:48', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21131, '弹幕查询', 2113, 1, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:barrage:query', '#', 'admin', '2025-07-04 16:03:48', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21132, '弹幕新增', 2113, 2, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:barrage:add', '#', 'admin', '2025-07-04 16:03:48', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21133, '弹幕修改', 2113, 3, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:barrage:edit', '#', 'admin', '2025-07-04 16:03:48', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21134, '弹幕删除', 2113, 4, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:barrage:remove', '#', 'admin', '2025-07-04 16:03:48', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21135, '弹幕导出', 2113, 5, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:barrage:export', '#', 'admin', '2025-07-04 16:03:48', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21136, '弹幕审核', 2113, 6, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:barrage:audit', '#', 'admin', '2025-07-04 16:03:48', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21141, 'top图片查询', 2114, 1, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:topimage:query', '#', 'admin', '2025-07-07 10:17:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21142, 'top图片新增', 2114, 2, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:topimage:add', '#', 'admin', '2025-07-07 10:17:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21143, 'top图片修改', 2114, 3, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:topimage:edit', '#', 'admin', '2025-07-07 10:17:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21144, 'top图片删除', 2114, 4, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:topimage:remove', '#', 'admin', '2025-07-07 10:17:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21145, 'top图片导出', 2114, 5, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:topimage:export', '#', 'admin', '2025-07-07 10:17:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21211, '活动查询', 2121, 1, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:activity:query', '#', 'admin', '2025-07-04 16:03:48', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21212, '活动新增', 2121, 2, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:activity:add', '#', 'admin', '2025-07-04 16:03:48', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21213, '活动修改', 2121, 3, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:activity:edit', '#', 'admin', '2025-07-04 16:03:48', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21214, '活动删除', 2121, 4, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:activity:remove', '#', 'admin', '2025-07-04 16:03:48', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21215, '活动导出', 2121, 5, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:activity:export', '#', 'admin', '2025-07-04 16:03:48', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21231, '需求查询', 21448, 1, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:demand:query', '#', 'admin', '2025-07-04 16:03:48', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21232, '需求新增', 21448, 2, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:demand:add', '#', 'admin', '2025-07-04 16:03:48', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21233, '需求修改', 21448, 3, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:demand:edit', '#', 'admin', '2025-07-04 16:03:48', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21234, '需求删除', 21448, 4, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:demand:remove', '#', 'admin', '2025-07-04 16:03:48', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21235, '需求导出', 21448, 5, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:demand:export', '#', 'admin', '2025-07-04 16:03:48', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21241, '招聘查询', 2124, 1, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:job:query', '#', 'admin', '2025-07-04 16:03:49', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21242, '招聘新增', 2124, 2, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:job:add', '#', 'admin', '2025-07-04 16:03:49', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21243, '招聘修改', 2124, 3, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:job:edit', '#', 'admin', '2025-07-04 16:03:49', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21244, '招聘删除', 2124, 4, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:job:remove', '#', 'admin', '2025-07-04 16:03:49', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21245, '招聘导出', 2124, 5, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:job:export', '#', 'admin', '2025-07-04 16:03:49', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21251, '科技之星查询', 2125, 1, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:techstar:query', '#', 'admin', '2025-07-04 16:03:49', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21252, '科技之星新增', 2125, 2, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:techstar:add', '#', 'admin', '2025-07-04 16:03:49', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21253, '科技之星修改', 2125, 3, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:techstar:edit', '#', 'admin', '2025-07-04 16:03:49', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21254, '科技之星删除', 2125, 4, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:techstar:remove', '#', 'admin', '2025-07-04 16:03:49', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21255, '科技之星导出', 2125, 5, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:techstar:export', '#', 'admin', '2025-07-04 16:03:49', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21321, '页面内容查询', 2132, 1, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:page:query', '#', 'admin', '2025-07-04 16:03:49', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21322, '页面内容新增', 2132, 2, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:page:add', '#', 'admin', '2025-07-04 16:03:49', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21323, '页面内容修改', 2132, 3, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:page:edit', '#', 'admin', '2025-07-04 16:03:49', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21324, '页面内容删除', 2132, 4, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:page:remove', '#', 'admin', '2025-07-04 16:03:49', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21325, '页面内容导出', 2132, 5, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:page:export', '#', 'admin', '2025-07-04 16:03:49', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21401, '行业类型查询', 2140, 1, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:industry:type:query', '#', 'admin', '2025-07-05 11:49:09', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21402, '行业类型新增', 2140, 2, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:industry:type:add', '#', 'admin', '2025-07-05 11:49:09', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21403, '行业类型修改', 2140, 3, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:industry:type:edit', '#', 'admin', '2025-07-05 11:49:09', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21404, '行业类型删除', 2140, 4, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:industry:type:remove', '#', 'admin', '2025-07-05 11:49:09', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21405, '产业定位查询', 2140, 5, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:industry:position:query', '#', 'admin', '2025-07-05 11:49:09', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21406, '产业定位新增', 2140, 6, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:industry:position:add', '#', 'admin', '2025-07-05 11:49:09', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21407, '产业定位修改', 2140, 7, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:industry:position:edit', '#', 'admin', '2025-07-05 11:49:09', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21408, '产业定位删除', 2140, 8, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:industry:position:remove', '#', 'admin', '2025-07-05 11:49:09', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21409, '细分领域查询', 2140, 9, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:industry:segment:query', '#', 'admin', '2025-07-05 11:49:09', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21410, '细分领域新增', 2140, 10, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:industry:segment:add', '#', 'admin', '2025-07-05 11:49:09', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21411, '细分领域修改', 2140, 11, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:industry:segment:edit', '#', 'admin', '2025-07-05 11:49:09', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21412, '细分领域删除', 2140, 12, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:industry:segment:remove', '#', 'admin', '2025-07-05 11:49:09', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21413, '用户行业选择查询', 2140, 13, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:industry:user:query', '#', 'admin', '2025-07-05 11:49:09', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21414, '用户行业选择导出', 2140, 14, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:industry:user:export', '#', 'admin', '2025-07-05 11:49:09', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21436, '活动报名管理', 2122, 1, 'event', 'miniapp/business/event/index', '', '', 1, 0, 'C', '0', '0', 'miniapp:event:list', 'form', 'admin', '2025-07-05 14:09:53', '', NULL, '活动报名管理菜单');
INSERT INTO `sys_menu` VALUES (21437, '用户报名记录', 2122, 2, 'registration', 'miniapp/registration/index', '', '', 1, 0, 'C', '0', '0', 'miniapp:registration:list', 'peoples', 'admin', '2025-07-05 14:09:53', '', NULL, '用户报名记录管理菜单');
INSERT INTO `sys_menu` VALUES (21438, '活动报名查询', 21436, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'miniapp:event:query', '#', 'admin', '2025-07-05 14:09:53', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21439, '活动报名新增', 21436, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'miniapp:event:add', '#', 'admin', '2025-07-05 14:09:53', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21440, '活动报名修改', 21436, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'miniapp:event:edit', '#', 'admin', '2025-07-05 14:09:53', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21441, '活动报名删除', 21436, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'miniapp:event:remove', '#', 'admin', '2025-07-05 14:09:53', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21442, '活动报名导出', 21436, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'miniapp:event:export', '#', 'admin', '2025-07-05 14:09:53', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21443, '用户报名记录查询', 21437, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'miniapp:registration:query', '#', 'admin', '2025-07-05 14:09:53', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21444, '用户报名记录新增', 21437, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'miniapp:registration:add', '#', 'admin', '2025-07-05 14:09:53', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21445, '用户报名记录修改', 21437, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'miniapp:registration:edit', '#', 'admin', '2025-07-05 14:09:53', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21446, '用户报名记录删除', 21437, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'miniapp:registration:remove', '#', 'admin', '2025-07-05 14:09:53', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21447, '用户报名记录导出', 21437, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'miniapp:registration:export', '#', 'admin', '2025-07-05 14:09:53', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21448, '需求管理', 2123, 1, 'demand', 'miniapp/business/demand/index', NULL, '', 1, 0, 'C', '0', '0', 'miniapp:demand:list', 'form', 'admin', '2025-07-07 14:42:54', '', NULL, '需求发布管理');
INSERT INTO `sys_menu` VALUES (21449, '需求类型管理', 2123, 2, 'demandcategory', 'miniapp/business/demandcategory/index', NULL, '', 1, 0, 'C', '0', '0', 'miniapp:demandcategory:list', 'dict', 'admin', '2025-07-07 14:45:13', '', NULL, '需求类型管理');
INSERT INTO `sys_menu` VALUES (21450, '需求类型查询', 21449, 1, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:demandcategory:query', '#', 'admin', '2025-07-07 14:45:21', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21451, '需求类型新增', 21449, 2, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:demandcategory:add', '#', 'admin', '2025-07-07 14:45:21', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21452, '需求类型修改', 21449, 3, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:demandcategory:edit', '#', 'admin', '2025-07-07 14:45:21', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21453, '需求类型删除', 21449, 4, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:demandcategory:remove', '#', 'admin', '2025-07-07 14:45:21', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21454, '需求类型导出', 21449, 5, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:demandcategory:export', '#', 'admin', '2025-07-07 14:45:21', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21455, '创赛路演', 0, 5, 'competition', NULL, NULL, '', 1, 0, 'M', '0', '0', NULL, 'star', 'admin', '2025-07-07 16:12:32', '', NULL, '创赛路演目录');
INSERT INTO `sys_menu` VALUES (21456, '天大海棠杯创赛专区', 21455, 1, 'haitang', NULL, NULL, '', 1, 0, 'M', '0', '0', '', 'tree', 'admin', '2025-07-07 16:12:32', 'admin', '2025-07-23 09:08:21', '海棠杯创新创业创赛专区');
INSERT INTO `sys_menu` VALUES (21463, '项目报名', 21456, 2, 'project', 'miniapp/haitang/project/index', NULL, '', 1, 0, 'C', '0', '0', 'miniapp:haitang:project:list', 'clipboard', 'admin', '2025-07-07 16:30:11', '', NULL, '项目报名菜单');
INSERT INTO `sys_menu` VALUES (21464, '项目报名查询', 21463, 1, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:haitang:project:query', '#', 'admin', '2025-07-07 16:30:11', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21467, '项目报名删除', 21463, 4, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:haitang:project:remove', '#', 'admin', '2025-07-07 16:30:11', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21468, '项目报名导出', 21463, 5, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:haitang:project:export', '#', 'admin', '2025-07-07 16:30:11', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21469, '项目报名审核', 21463, 6, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:haitang:project:audit', '#', 'admin', '2025-07-07 16:30:11', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21470, '职位类型管理', 2130, 2, 'jobType', 'miniapp/jobType/index', '', '', 1, 0, 'C', '0', '0', 'miniapp:jobType:list', 'post', 'admin', '2025-07-07 16:57:03', '', NULL, '职位类型管理菜单');
INSERT INTO `sys_menu` VALUES (21471, '职位类型查询', 21470, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'miniapp:jobType:query', '', 'admin', '2025-07-07 16:57:10', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21472, '职位类型新增', 21470, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'miniapp:jobType:add', '', 'admin', '2025-07-07 16:57:10', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21473, '职位类型修改', 21470, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'miniapp:jobType:edit', '', 'admin', '2025-07-07 16:57:10', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21474, '职位类型删除', 21470, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'miniapp:jobType:remove', '', 'admin', '2025-07-07 16:57:10', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21475, '职位类型导出', 21470, 5, '#', '', '', '', 1, 0, 'F', '0', '0', 'miniapp:jobType:export', '', 'admin', '2025-07-07 16:57:10', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21476, '企业管理', 2130, 3, 'enterprise', 'miniapp/config/enterprise/index', NULL, '', 1, 0, 'C', '0', '0', 'miniapp:enterprise:list', 'company', 'admin', '2025-07-07 17:41:17', '', NULL, '企业管理菜单');
INSERT INTO `sys_menu` VALUES (21477, '企业查询', 21476, 1, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:enterprise:query', '#', 'admin', '2025-07-07 17:41:27', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21478, '企业新增', 21476, 2, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:enterprise:add', '#', 'admin', '2025-07-07 17:41:27', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21479, '企业修改', 21476, 3, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:enterprise:edit', '#', 'admin', '2025-07-07 17:41:27', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21480, '企业删除', 21476, 4, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:enterprise:remove', '#', 'admin', '2025-07-07 17:41:27', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21481, '企业导出', 21476, 5, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:enterprise:export', '#', 'admin', '2025-07-07 17:41:27', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21482, '辅导活动', 21492, 1, 'guidance', 'miniapp/haitang/guidance/index', '', '', 1, 0, 'C', '0', '0', 'miniapp:haitang:guidance:list', 'guide', 'admin', '2025-07-07 18:16:20', 'admin', '2025-07-29 06:17:41', '项目指导活动菜单');
INSERT INTO `sys_menu` VALUES (21483, '项目指导活动查询', 21482, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'miniapp:haitang:guidance:query', '#', 'admin', '2025-07-07 18:16:28', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21484, '项目指导活动新增', 21482, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'miniapp:haitang:guidance:add', '#', 'admin', '2025-07-07 18:16:28', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21485, '项目指导活动修改', 21482, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'miniapp:haitang:guidance:edit', '#', 'admin', '2025-07-07 18:16:28', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21486, '项目指导活动删除', 21482, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'miniapp:haitang:guidance:remove', '#', 'admin', '2025-07-07 18:16:28', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21487, '项目指导活动导出', 21482, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'miniapp:haitang:guidance:export', '#', 'admin', '2025-07-07 18:16:28', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21488, '报名记录', 21492, 2, 'registration', 'miniapp/haitang/registration/index', '', '', 1, 0, 'C', '0', '0', 'miniapp:haitang:registration:list', 'form', 'admin', '2025-07-07 18:16:36', 'admin', '2025-07-29 06:17:54', '指导活动报名菜单');
INSERT INTO `sys_menu` VALUES (21489, '指导活动报名查询', 21488, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'miniapp:haitang:registration:query', '#', 'admin', '2025-07-07 18:16:45', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21490, '指导活动报名删除', 21488, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'miniapp:haitang:registration:remove', '#', 'admin', '2025-07-07 18:16:45', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21491, '指导活动报名导出', 21488, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'miniapp:haitang:registration:export', '#', 'admin', '2025-07-07 18:16:45', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21492, '赛前辅导', 21456, 3, 'guidance', NULL, NULL, '', 1, 0, 'M', '0', '0', '', 'guide', 'admin', '2025-07-07 18:46:53', 'admin', '2025-07-29 06:15:34', '项目辅导报名目录');
INSERT INTO `sys_menu` VALUES (21500, '大赛简介', 21456, 4, 'competition', 'miniapp/config/competition/index', '', '', 1, 0, 'C', '0', '0', 'miniapp:competition:list', 'build', 'admin', '2025-07-08 10:43:03', 'admin', '2025-07-29 06:19:11', '大赛介绍菜单');
INSERT INTO `sys_menu` VALUES (21501, '大赛介绍查询', 21500, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'miniapp:competition:query', '#', 'admin', '2025-07-08 10:43:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21502, '大赛介绍新增', 21500, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'miniapp:competition:add', '#', 'admin', '2025-07-08 10:43:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21503, '大赛介绍修改', 21500, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'miniapp:competition:edit', '#', 'admin', '2025-07-08 10:43:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21504, '大赛介绍删除', 21500, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'miniapp:competition:remove', '#', 'admin', '2025-07-08 10:43:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21505, '大赛介绍导出', 21500, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'miniapp:competition:export', '#', 'admin', '2025-07-08 10:43:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21506, '视频展播', 21456, 5, 'video', 'miniapp/config/video/index', '', '', 1, 0, 'C', '0', '0', 'miniapp:video:list', 'video', 'admin', '2025-07-08 11:22:35', '', NULL, '视频展播菜单');
INSERT INTO `sys_menu` VALUES (21507, '视频展播查询', 21506, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'miniapp:video:query', '#', 'admin', '2025-07-08 11:22:44', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21508, '视频展播新增', 21506, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'miniapp:video:add', '#', 'admin', '2025-07-08 11:22:44', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21509, '视频展播修改', 21506, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'miniapp:video:edit', '#', 'admin', '2025-07-08 11:22:44', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21510, '视频展播删除', 21506, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'miniapp:video:remove', '#', 'admin', '2025-07-08 11:22:44', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21511, '视频展播导出', 21506, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'miniapp:video:export', '#', 'admin', '2025-07-08 11:22:44', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21512, '专家矩阵', 21456, 3, 'expert', 'miniapp/haitang/expert/index', NULL, '', 1, 0, 'C', '0', '0', 'miniapp:expert:list', 'user', 'admin', '2025-07-08 16:12:59', '', NULL, '专家矩阵菜单');
INSERT INTO `sys_menu` VALUES (21513, '专家矩阵查询', 21512, 1, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:expert:query', '#', 'admin', '2025-07-08 16:13:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21514, '专家矩阵新增', 21512, 2, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:expert:add', '#', 'admin', '2025-07-08 16:13:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21515, '专家矩阵修改', 21512, 3, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:expert:edit', '#', 'admin', '2025-07-08 16:13:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21516, '专家矩阵删除', 21512, 4, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:expert:remove', '#', 'admin', '2025-07-08 16:13:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21517, '专家矩阵导出', 21512, 5, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:expert:export', '#', 'admin', '2025-07-08 16:13:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21520, '小程序用户管理', 0, 20, 'miniuser', 'miniapp/user/index', '', '', 1, 0, 'C', '0', '0', 'miniapp:user:list', 'peoples', 'admin', '2025-07-10 11:50:54', 'admin', '2025-07-29 02:00:29', '小程序用户管理目录');
INSERT INTO `sys_menu` VALUES (21539, '小程序用户查询', 21520, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'miniapp:user:query', '#', 'admin', '2025-07-10 12:31:00', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21541, '小程序用户修改', 21520, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'miniapp:user:edit', '#', 'admin', '2025-07-10 12:31:00', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21542, '小程序用户删除', 21520, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'miniapp:user:remove', '#', 'admin', '2025-07-10 12:31:00', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21543, '小程序用户导出', 21520, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'miniapp:user:export', '#', 'admin', '2025-07-10 12:31:00', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21607, '同步微信文章', 21604, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'miniapp:haitang:news:sync', '#', 'admin', '2025-07-16 17:02:33', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21615, '新闻中心', 21456, 8, 'news', 'miniapp/haitang/news/index', NULL, '', 1, 0, 'C', '0', '0', 'miniapp:news:list', 'documentation', 'admin', '2025-07-17 11:29:32', '', NULL, '新闻中心管理');
INSERT INTO `sys_menu` VALUES (21616, '新闻中心查询', 21615, 1, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:news:query', '#', 'admin', '2025-07-17 11:29:39', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21617, '新闻中心新增', 21615, 2, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:news:add', '#', 'admin', '2025-07-17 11:29:39', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21618, '新闻中心修改', 21615, 3, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:news:edit', '#', 'admin', '2025-07-17 11:29:39', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21619, '新闻中心删除', 21615, 4, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:news:remove', '#', 'admin', '2025-07-17 11:29:39', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21620, '新闻中心导出', 21615, 5, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:news:export', '#', 'admin', '2025-07-17 11:29:39', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21621, '新闻中心同步', 21615, 6, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:news:sync', '#', 'admin', '2025-07-17 11:29:39', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21622, '敏感词管理', 2130, 5, 'sensitiveWord', 'miniapp/sensitiveWord/index', NULL, '', 1, 0, 'M', '0', '0', 'miniapp:sensitiveWord:list', '敏感词', 'admin', '2025-07-17 14:24:31', 'admin', '2025-07-17 15:32:21', '敏感词管理菜单');
INSERT INTO `sys_menu` VALUES (21623, '敏感词查询', 21622, 1, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:sensitiveWord:query', '#', 'admin', '2025-07-17 14:24:49', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21624, '敏感词新增', 21622, 2, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:sensitiveWord:add', '#', 'admin', '2025-07-17 14:24:49', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21625, '敏感词修改', 21622, 3, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:sensitiveWord:edit', '#', 'admin', '2025-07-17 14:24:49', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21626, '敏感词删除', 21622, 4, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:sensitiveWord:remove', '#', 'admin', '2025-07-17 14:24:49', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21627, '敏感词导出', 21622, 5, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:sensitiveWord:export', '#', 'admin', '2025-07-17 14:24:49', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21628, '敏感词分类', 2130, 6, 'sensitiveWordCategory', 'miniapp/sensitiveWord/category', NULL, '', 1, 0, 'M', '0', '0', 'miniapp:sensitiveWordCategory:list', 'tree-table', 'admin', '2025-07-17 14:25:00', '', NULL, '敏感词分类管理菜单');
INSERT INTO `sys_menu` VALUES (21629, '分类查询', 21628, 1, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:sensitiveWordCategory:query', '#', 'admin', '2025-07-17 14:25:07', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21630, '分类新增', 21628, 2, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:sensitiveWordCategory:add', '#', 'admin', '2025-07-17 14:25:07', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21631, '分类修改', 21628, 3, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:sensitiveWordCategory:edit', '#', 'admin', '2025-07-17 14:25:07', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21632, '分类删除', 21628, 4, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:sensitiveWordCategory:remove', '#', 'admin', '2025-07-17 14:25:07', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21633, '分类导出', 21628, 5, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:sensitiveWordCategory:export', '#', 'admin', '2025-07-17 14:25:07', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21634, '敏感词日志', 2130, 7, 'sensitiveWordLog', 'miniapp/sensitiveWord/log', NULL, '', 1, 0, 'M', '0', '0', 'miniapp:sensitiveWordLog:list', 'log', 'admin', '2025-07-17 14:25:14', '', NULL, '敏感词检测日志菜单');
INSERT INTO `sys_menu` VALUES (21635, '日志查询', 21634, 1, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:sensitiveWordLog:query', '#', 'admin', '2025-07-17 14:25:25', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21636, '日志删除', 21634, 2, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:sensitiveWordLog:remove', '#', 'admin', '2025-07-17 14:25:25', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21637, '日志导出', 21634, 3, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:sensitiveWordLog:export', '#', 'admin', '2025-07-17 14:25:25', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21638, '日志清空', 21634, 4, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:sensitiveWordLog:clear', '#', 'admin', '2025-07-17 14:25:25', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21639, '学院管理', 2130, 6, 'college', 'miniapp/config/college/index', NULL, '', 1, 0, 'C', '0', '0', 'miniapp:college:list', 'education', 'admin', '2025-07-23 18:13:03', '', NULL, '学院管理菜单');
INSERT INTO `sys_menu` VALUES (21640, '学院查询', 21639, 1, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:college:query', '#', 'admin', '2025-07-23 18:13:12', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21641, '学院新增', 21639, 2, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:college:add', '#', 'admin', '2025-07-23 18:13:12', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21642, '学院修改', 21639, 3, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:college:edit', '#', 'admin', '2025-07-23 18:13:12', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21643, '学院删除', 21639, 4, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:college:remove', '#', 'admin', '2025-07-23 18:13:12', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21644, '学院导出', 21639, 5, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:college:export', '#', 'admin', '2025-07-23 18:13:12', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21645, '园区入驻', 0, 6, 'park', NULL, NULL, '', 1, 0, 'M', '0', '0', '', 'build', 'admin', '2025-07-25 10:10:52', 'admin', '2025-07-29 01:58:44', '园区管理目录');
INSERT INTO `sys_menu` VALUES (21646, '园区信息', 21645, 1, 'park', 'miniapp/park/index', NULL, '', 1, 0, 'C', '0', '0', 'miniapp:park:list', 'tree', 'admin', '2025-07-25 10:10:59', '', NULL, '园区信息菜单');
INSERT INTO `sys_menu` VALUES (21647, '园区查询', 21646, 1, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:park:query', '#', 'admin', '2025-07-25 10:11:09', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21648, '园区新增', 21646, 2, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:park:add', '#', 'admin', '2025-07-25 10:11:09', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21649, '园区修改', 21646, 3, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:park:edit', '#', 'admin', '2025-07-25 10:11:09', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21650, '园区删除', 21646, 4, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:park:remove', '#', 'admin', '2025-07-25 10:11:09', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21651, '园区导出', 21646, 5, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:park:export', '#', 'admin', '2025-07-25 10:11:09', '', NULL, '');
INSERT INTO `sys_menu` VALUES (21652, '联系人管理', 2110, 5, 'contact', 'miniapp/content/contact/index', NULL, '', 1, 0, 'C', '0', '0', 'miniapp:contact:list', 'phone', 'admin', '2025-07-28 04:45:30', '', NULL, '联系人管理菜单');
INSERT INTO `sys_menu` VALUES (216521, '联系人查询', 21652, 1, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:contact:query', '#', 'admin', '2025-07-28 04:45:39', '', NULL, '');
INSERT INTO `sys_menu` VALUES (216522, '联系人新增', 21652, 2, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:contact:add', '#', 'admin', '2025-07-28 04:45:39', '', NULL, '');
INSERT INTO `sys_menu` VALUES (216523, '联系人修改', 21652, 3, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:contact:edit', '#', 'admin', '2025-07-28 04:45:39', '', NULL, '');
INSERT INTO `sys_menu` VALUES (216524, '联系人删除', 21652, 4, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:contact:remove', '#', 'admin', '2025-07-28 04:45:39', '', NULL, '');
INSERT INTO `sys_menu` VALUES (216525, '联系人导出', 21652, 5, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:contact:export', '#', 'admin', '2025-07-28 04:45:39', '', NULL, '');
INSERT INTO `sys_menu` VALUES (216526, '西青金种子路演专区', 21455, 2, 'xiqing', NULL, NULL, '', 1, 0, 'M', '0', '0', '', 'star', 'admin', '2025-07-28 06:52:45', '', NULL, '西青金种子路演专区目录');
INSERT INTO `sys_menu` VALUES (216527, '专区活动管理', 216526, 1, 'activity', 'miniapp/xiqing/activity/index', NULL, '', 1, 0, 'C', '0', '0', 'miniapp:xiqing:activity:list', 'edit', 'admin', '2025-07-28 06:52:55', '', NULL, '专区活动管理菜单');
INSERT INTO `sys_menu` VALUES (216530, '专区活动查询', 216527, 1, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:xiqing:activity:query', '#', 'admin', '2025-07-28 06:53:23', '', NULL, '');
INSERT INTO `sys_menu` VALUES (216531, '专区活动编辑', 216527, 2, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:xiqing:activity:edit', '#', 'admin', '2025-07-28 06:53:23', '', NULL, '');
INSERT INTO `sys_menu` VALUES (216542, '路演活动配置', 216526, 2, 'activity-config', 'miniapp/xiqing/activity-config/index', NULL, '', 1, 0, 'C', '0', '0', 'miniapp:xiqing:activity:list', 'form', 'admin', '2025-07-28 07:52:25', '', NULL, '路演活动配置菜单');
INSERT INTO `sys_menu` VALUES (216543, '路演报名管理', 216526, 3, 'registration-manage', 'miniapp/xiqing/registration-manage/index', NULL, '', 1, 0, 'C', '0', '0', 'miniapp:xiqing:registration:list', 'peoples', 'admin', '2025-07-28 07:52:25', '', NULL, '路演报名管理菜单');
INSERT INTO `sys_menu` VALUES (216544, '路演活动查询', 216542, 1, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:xiqing:activity:query', '#', 'admin', '2025-07-28 07:52:39', '', NULL, '');
INSERT INTO `sys_menu` VALUES (216545, '路演活动新增', 216542, 2, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:xiqing:activity:add', '#', 'admin', '2025-07-28 07:52:39', '', NULL, '');
INSERT INTO `sys_menu` VALUES (216546, '路演活动修改', 216542, 3, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:xiqing:activity:edit', '#', 'admin', '2025-07-28 07:52:39', '', NULL, '');
INSERT INTO `sys_menu` VALUES (216547, '路演活动删除', 216542, 4, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:xiqing:activity:remove', '#', 'admin', '2025-07-28 07:52:39', '', NULL, '');
INSERT INTO `sys_menu` VALUES (216548, '路演活动导出', 216542, 5, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:xiqing:activity:export', '#', 'admin', '2025-07-28 07:52:39', '', NULL, '');
INSERT INTO `sys_menu` VALUES (216549, '路演报名查询', 216543, 1, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:xiqing:registration:query', '#', 'admin', '2025-07-28 07:52:39', '', NULL, '');
INSERT INTO `sys_menu` VALUES (216550, '路演报名删除', 216543, 2, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:xiqing:registration:remove', '#', 'admin', '2025-07-28 07:52:39', '', NULL, '');
INSERT INTO `sys_menu` VALUES (216551, '路演报名导出', 216543, 3, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:xiqing:registration:export', '#', 'admin', '2025-07-28 07:52:39', '', NULL, '');
INSERT INTO `sys_menu` VALUES (216552, '路演报名审核', 216543, 4, '', '', NULL, '', 1, 0, 'F', '0', '0', 'miniapp:xiqing:registration:audit', '#', 'admin', '2025-07-28 07:52:39', '', NULL, '');

-- ----------------------------
-- Table structure for sys_notice
-- ----------------------------
DROP TABLE IF EXISTS `sys_notice`;
CREATE TABLE `sys_notice`  (
  `notice_id` int NOT NULL AUTO_INCREMENT COMMENT '公告ID',
  `notice_title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '公告标题',
  `notice_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '公告类型（1通知 2公告）',
  `notice_content` longblob NULL COMMENT '公告内容',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '0' COMMENT '公告状态（0正常 1关闭）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`notice_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci COMMENT = '通知公告表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_notice
-- ----------------------------
INSERT INTO `sys_notice` VALUES (1, '温馨提醒：2018-07-01 新版本发布', '2', 0xE696B0E78988E69CACE58685E5AEB9, '0', 'admin', '2025-07-04 16:01:56', '', NULL, '管理员');
INSERT INTO `sys_notice` VALUES (2, '维护通知：2018-07-01 系统凌晨维护', '1', 0xE7BBB4E68AA4E58685E5AEB9, '0', 'admin', '2025-07-04 16:01:56', '', NULL, '管理员');

-- ----------------------------
-- Table structure for sys_oper_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_oper_log`;
CREATE TABLE `sys_oper_log`  (
  `oper_id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志主键',
  `title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '模块标题',
  `business_type` int NULL DEFAULT 0 COMMENT '业务类型（0其它 1新增 2修改 3删除）',
  `method` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '方法名称',
  `request_method` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '请求方式',
  `operator_type` int NULL DEFAULT 0 COMMENT '操作类别（0其它 1后台用户 2手机端用户）',
  `oper_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '操作人员',
  `dept_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '部门名称',
  `oper_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '请求URL',
  `oper_ip` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '主机地址',
  `oper_location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '操作地点',
  `oper_param` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '请求参数',
  `json_result` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '返回参数',
  `status` int NULL DEFAULT 0 COMMENT '操作状态（0正常 1异常）',
  `error_msg` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '错误消息',
  `oper_time` datetime NULL DEFAULT NULL COMMENT '操作时间',
  `cost_time` bigint NULL DEFAULT 0 COMMENT '消耗时间',
  PRIMARY KEY (`oper_id`) USING BTREE,
  INDEX `idx_sys_oper_log_bt`(`business_type` ASC) USING BTREE,
  INDEX `idx_sys_oper_log_s`(`status` ASC) USING BTREE,
  INDEX `idx_sys_oper_log_ot`(`oper_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 947 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci COMMENT = '操作日志记录' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_oper_log
-- ----------------------------

-- ----------------------------
-- Table structure for sys_post
-- ----------------------------
DROP TABLE IF EXISTS `sys_post`;
CREATE TABLE `sys_post`  (
  `post_id` bigint NOT NULL AUTO_INCREMENT COMMENT '岗位ID',
  `post_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '岗位编码',
  `post_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '岗位名称',
  `post_sort` int NOT NULL COMMENT '显示顺序',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`post_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci COMMENT = '岗位信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_post
-- ----------------------------
INSERT INTO `sys_post` VALUES (1, 'ceo', '董事长', 1, '0', 'admin', '2025-07-04 16:01:55', '', NULL, '');
INSERT INTO `sys_post` VALUES (2, 'se', '项目经理', 2, '0', 'admin', '2025-07-04 16:01:55', '', NULL, '');
INSERT INTO `sys_post` VALUES (3, 'hr', '人力资源', 3, '0', 'admin', '2025-07-04 16:01:55', '', NULL, '');
INSERT INTO `sys_post` VALUES (4, 'user', '普通员工', 4, '0', 'admin', '2025-07-04 16:01:55', '', NULL, '');

-- ----------------------------
-- Table structure for sys_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_role`;
CREATE TABLE `sys_role`  (
  `role_id` bigint NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `role_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '角色名称',
  `role_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '角色权限字符串',
  `role_sort` int NOT NULL COMMENT '显示顺序',
  `data_scope` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '1' COMMENT '数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）',
  `menu_check_strictly` tinyint(1) NULL DEFAULT 1 COMMENT '菜单树选择项是否关联显示',
  `dept_check_strictly` tinyint(1) NULL DEFAULT 1 COMMENT '部门树选择项是否关联显示',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '角色状态（0正常 1停用）',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`role_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 102 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci COMMENT = '角色信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_role
-- ----------------------------
INSERT INTO `sys_role` VALUES (1, '超级管理员', 'admin', 1, '1', 1, 1, '0', '0', 'admin', '2025-07-04 16:01:55', '', NULL, '超级管理员');
INSERT INTO `sys_role` VALUES (2, '普通管理员', 'commonAdmin', 2, '1', 1, 1, '0', '0', 'admin', '2025-07-04 16:01:55', 'admin', '2025-07-10 14:26:55', '普通角色');
INSERT INTO `sys_role` VALUES (101, '小程序用户', 'mini', 1, '1', 1, 1, '0', '0', 'admin', '2025-07-10 12:29:54', 'admin', '2025-07-29 08:28:09', NULL);

-- ----------------------------
-- Table structure for sys_role_dept
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_dept`;
CREATE TABLE `sys_role_dept`  (
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `dept_id` bigint NOT NULL COMMENT '部门ID',
  PRIMARY KEY (`role_id`, `dept_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci COMMENT = '角色和部门关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_role_dept
-- ----------------------------

-- ----------------------------
-- Table structure for sys_role_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_menu`;
CREATE TABLE `sys_role_menu`  (
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `menu_id` bigint NOT NULL COMMENT '菜单ID',
  PRIMARY KEY (`role_id`, `menu_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci COMMENT = '角色和菜单关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_role_menu
-- ----------------------------
INSERT INTO `sys_role_menu` VALUES (1, 2100);
INSERT INTO `sys_role_menu` VALUES (1, 2101);
INSERT INTO `sys_role_menu` VALUES (1, 2102);
INSERT INTO `sys_role_menu` VALUES (1, 2110);
INSERT INTO `sys_role_menu` VALUES (1, 2111);
INSERT INTO `sys_role_menu` VALUES (1, 2112);
INSERT INTO `sys_role_menu` VALUES (1, 2113);
INSERT INTO `sys_role_menu` VALUES (1, 2120);
INSERT INTO `sys_role_menu` VALUES (1, 2121);
INSERT INTO `sys_role_menu` VALUES (1, 2122);
INSERT INTO `sys_role_menu` VALUES (1, 2123);
INSERT INTO `sys_role_menu` VALUES (1, 2124);
INSERT INTO `sys_role_menu` VALUES (1, 2125);
INSERT INTO `sys_role_menu` VALUES (1, 2130);
INSERT INTO `sys_role_menu` VALUES (1, 2132);
INSERT INTO `sys_role_menu` VALUES (1, 2140);
INSERT INTO `sys_role_menu` VALUES (1, 21021);
INSERT INTO `sys_role_menu` VALUES (1, 21022);
INSERT INTO `sys_role_menu` VALUES (1, 21023);
INSERT INTO `sys_role_menu` VALUES (1, 21024);
INSERT INTO `sys_role_menu` VALUES (1, 21025);
INSERT INTO `sys_role_menu` VALUES (1, 21026);
INSERT INTO `sys_role_menu` VALUES (1, 21027);
INSERT INTO `sys_role_menu` VALUES (1, 21111);
INSERT INTO `sys_role_menu` VALUES (1, 21112);
INSERT INTO `sys_role_menu` VALUES (1, 21113);
INSERT INTO `sys_role_menu` VALUES (1, 21114);
INSERT INTO `sys_role_menu` VALUES (1, 21115);
INSERT INTO `sys_role_menu` VALUES (1, 21121);
INSERT INTO `sys_role_menu` VALUES (1, 21122);
INSERT INTO `sys_role_menu` VALUES (1, 21123);
INSERT INTO `sys_role_menu` VALUES (1, 21124);
INSERT INTO `sys_role_menu` VALUES (1, 21125);
INSERT INTO `sys_role_menu` VALUES (1, 21131);
INSERT INTO `sys_role_menu` VALUES (1, 21132);
INSERT INTO `sys_role_menu` VALUES (1, 21133);
INSERT INTO `sys_role_menu` VALUES (1, 21134);
INSERT INTO `sys_role_menu` VALUES (1, 21135);
INSERT INTO `sys_role_menu` VALUES (1, 21136);
INSERT INTO `sys_role_menu` VALUES (1, 21211);
INSERT INTO `sys_role_menu` VALUES (1, 21212);
INSERT INTO `sys_role_menu` VALUES (1, 21213);
INSERT INTO `sys_role_menu` VALUES (1, 21214);
INSERT INTO `sys_role_menu` VALUES (1, 21215);
INSERT INTO `sys_role_menu` VALUES (1, 21221);
INSERT INTO `sys_role_menu` VALUES (1, 21222);
INSERT INTO `sys_role_menu` VALUES (1, 21223);
INSERT INTO `sys_role_menu` VALUES (1, 21224);
INSERT INTO `sys_role_menu` VALUES (1, 21225);
INSERT INTO `sys_role_menu` VALUES (1, 21231);
INSERT INTO `sys_role_menu` VALUES (1, 21232);
INSERT INTO `sys_role_menu` VALUES (1, 21233);
INSERT INTO `sys_role_menu` VALUES (1, 21234);
INSERT INTO `sys_role_menu` VALUES (1, 21235);
INSERT INTO `sys_role_menu` VALUES (1, 21241);
INSERT INTO `sys_role_menu` VALUES (1, 21242);
INSERT INTO `sys_role_menu` VALUES (1, 21243);
INSERT INTO `sys_role_menu` VALUES (1, 21244);
INSERT INTO `sys_role_menu` VALUES (1, 21245);
INSERT INTO `sys_role_menu` VALUES (1, 21251);
INSERT INTO `sys_role_menu` VALUES (1, 21252);
INSERT INTO `sys_role_menu` VALUES (1, 21253);
INSERT INTO `sys_role_menu` VALUES (1, 21254);
INSERT INTO `sys_role_menu` VALUES (1, 21255);
INSERT INTO `sys_role_menu` VALUES (1, 21321);
INSERT INTO `sys_role_menu` VALUES (1, 21322);
INSERT INTO `sys_role_menu` VALUES (1, 21323);
INSERT INTO `sys_role_menu` VALUES (1, 21324);
INSERT INTO `sys_role_menu` VALUES (1, 21325);
INSERT INTO `sys_role_menu` VALUES (1, 21401);
INSERT INTO `sys_role_menu` VALUES (1, 21402);
INSERT INTO `sys_role_menu` VALUES (1, 21403);
INSERT INTO `sys_role_menu` VALUES (1, 21404);
INSERT INTO `sys_role_menu` VALUES (1, 21405);
INSERT INTO `sys_role_menu` VALUES (1, 21406);
INSERT INTO `sys_role_menu` VALUES (1, 21407);
INSERT INTO `sys_role_menu` VALUES (1, 21408);
INSERT INTO `sys_role_menu` VALUES (1, 21409);
INSERT INTO `sys_role_menu` VALUES (1, 21410);
INSERT INTO `sys_role_menu` VALUES (1, 21411);
INSERT INTO `sys_role_menu` VALUES (1, 21412);
INSERT INTO `sys_role_menu` VALUES (1, 21413);
INSERT INTO `sys_role_menu` VALUES (1, 21414);
INSERT INTO `sys_role_menu` VALUES (1, 21430);
INSERT INTO `sys_role_menu` VALUES (1, 21431);
INSERT INTO `sys_role_menu` VALUES (1, 21432);
INSERT INTO `sys_role_menu` VALUES (1, 21433);
INSERT INTO `sys_role_menu` VALUES (1, 21434);
INSERT INTO `sys_role_menu` VALUES (1, 21435);
INSERT INTO `sys_role_menu` VALUES (1, 21436);
INSERT INTO `sys_role_menu` VALUES (1, 21437);
INSERT INTO `sys_role_menu` VALUES (1, 21438);
INSERT INTO `sys_role_menu` VALUES (1, 21439);
INSERT INTO `sys_role_menu` VALUES (1, 21440);
INSERT INTO `sys_role_menu` VALUES (1, 21441);
INSERT INTO `sys_role_menu` VALUES (1, 21442);
INSERT INTO `sys_role_menu` VALUES (1, 21443);
INSERT INTO `sys_role_menu` VALUES (1, 21444);
INSERT INTO `sys_role_menu` VALUES (1, 21445);
INSERT INTO `sys_role_menu` VALUES (1, 21446);
INSERT INTO `sys_role_menu` VALUES (1, 21447);
INSERT INTO `sys_role_menu` VALUES (1, 21455);
INSERT INTO `sys_role_menu` VALUES (1, 21456);
INSERT INTO `sys_role_menu` VALUES (1, 21463);
INSERT INTO `sys_role_menu` VALUES (1, 21464);
INSERT INTO `sys_role_menu` VALUES (1, 21467);
INSERT INTO `sys_role_menu` VALUES (1, 21468);
INSERT INTO `sys_role_menu` VALUES (1, 21469);
INSERT INTO `sys_role_menu` VALUES (1, 21470);
INSERT INTO `sys_role_menu` VALUES (1, 21471);
INSERT INTO `sys_role_menu` VALUES (1, 21472);
INSERT INTO `sys_role_menu` VALUES (1, 21473);
INSERT INTO `sys_role_menu` VALUES (1, 21474);
INSERT INTO `sys_role_menu` VALUES (1, 21475);
INSERT INTO `sys_role_menu` VALUES (1, 21476);
INSERT INTO `sys_role_menu` VALUES (1, 21477);
INSERT INTO `sys_role_menu` VALUES (1, 21478);
INSERT INTO `sys_role_menu` VALUES (1, 21479);
INSERT INTO `sys_role_menu` VALUES (1, 21480);
INSERT INTO `sys_role_menu` VALUES (1, 21481);
INSERT INTO `sys_role_menu` VALUES (1, 21482);
INSERT INTO `sys_role_menu` VALUES (1, 21483);
INSERT INTO `sys_role_menu` VALUES (1, 21484);
INSERT INTO `sys_role_menu` VALUES (1, 21485);
INSERT INTO `sys_role_menu` VALUES (1, 21486);
INSERT INTO `sys_role_menu` VALUES (1, 21487);
INSERT INTO `sys_role_menu` VALUES (1, 21488);
INSERT INTO `sys_role_menu` VALUES (1, 21489);
INSERT INTO `sys_role_menu` VALUES (1, 21490);
INSERT INTO `sys_role_menu` VALUES (1, 21491);
INSERT INTO `sys_role_menu` VALUES (1, 21506);
INSERT INTO `sys_role_menu` VALUES (1, 21507);
INSERT INTO `sys_role_menu` VALUES (1, 21508);
INSERT INTO `sys_role_menu` VALUES (1, 21509);
INSERT INTO `sys_role_menu` VALUES (1, 21510);
INSERT INTO `sys_role_menu` VALUES (1, 21511);
INSERT INTO `sys_role_menu` VALUES (1, 21512);
INSERT INTO `sys_role_menu` VALUES (1, 21513);
INSERT INTO `sys_role_menu` VALUES (1, 21514);
INSERT INTO `sys_role_menu` VALUES (1, 21515);
INSERT INTO `sys_role_menu` VALUES (1, 21516);
INSERT INTO `sys_role_menu` VALUES (1, 21517);
INSERT INTO `sys_role_menu` VALUES (1, 21518);
INSERT INTO `sys_role_menu` VALUES (1, 21519);
INSERT INTO `sys_role_menu` VALUES (1, 21520);
INSERT INTO `sys_role_menu` VALUES (1, 21521);
INSERT INTO `sys_role_menu` VALUES (1, 21522);
INSERT INTO `sys_role_menu` VALUES (1, 21523);
INSERT INTO `sys_role_menu` VALUES (1, 21524);
INSERT INTO `sys_role_menu` VALUES (1, 21525);
INSERT INTO `sys_role_menu` VALUES (1, 21615);
INSERT INTO `sys_role_menu` VALUES (1, 21616);
INSERT INTO `sys_role_menu` VALUES (1, 21617);
INSERT INTO `sys_role_menu` VALUES (1, 21618);
INSERT INTO `sys_role_menu` VALUES (1, 21619);
INSERT INTO `sys_role_menu` VALUES (1, 21620);
INSERT INTO `sys_role_menu` VALUES (1, 21621);
INSERT INTO `sys_role_menu` VALUES (1, 21622);
INSERT INTO `sys_role_menu` VALUES (1, 21623);
INSERT INTO `sys_role_menu` VALUES (1, 21624);
INSERT INTO `sys_role_menu` VALUES (1, 21625);
INSERT INTO `sys_role_menu` VALUES (1, 21626);
INSERT INTO `sys_role_menu` VALUES (1, 21627);
INSERT INTO `sys_role_menu` VALUES (1, 21628);
INSERT INTO `sys_role_menu` VALUES (1, 21629);
INSERT INTO `sys_role_menu` VALUES (1, 21630);
INSERT INTO `sys_role_menu` VALUES (1, 21631);
INSERT INTO `sys_role_menu` VALUES (1, 21632);
INSERT INTO `sys_role_menu` VALUES (1, 21633);
INSERT INTO `sys_role_menu` VALUES (1, 21634);
INSERT INTO `sys_role_menu` VALUES (1, 21635);
INSERT INTO `sys_role_menu` VALUES (1, 21636);
INSERT INTO `sys_role_menu` VALUES (1, 21637);
INSERT INTO `sys_role_menu` VALUES (1, 21638);
INSERT INTO `sys_role_menu` VALUES (1, 21639);
INSERT INTO `sys_role_menu` VALUES (1, 21640);
INSERT INTO `sys_role_menu` VALUES (1, 21641);
INSERT INTO `sys_role_menu` VALUES (1, 21642);
INSERT INTO `sys_role_menu` VALUES (1, 21643);
INSERT INTO `sys_role_menu` VALUES (1, 21644);
INSERT INTO `sys_role_menu` VALUES (1, 21645);
INSERT INTO `sys_role_menu` VALUES (1, 21646);
INSERT INTO `sys_role_menu` VALUES (1, 21647);
INSERT INTO `sys_role_menu` VALUES (1, 21648);
INSERT INTO `sys_role_menu` VALUES (1, 21649);
INSERT INTO `sys_role_menu` VALUES (1, 21650);
INSERT INTO `sys_role_menu` VALUES (1, 21651);
INSERT INTO `sys_role_menu` VALUES (1, 21652);
INSERT INTO `sys_role_menu` VALUES (1, 216521);
INSERT INTO `sys_role_menu` VALUES (1, 216522);
INSERT INTO `sys_role_menu` VALUES (1, 216523);
INSERT INTO `sys_role_menu` VALUES (1, 216524);
INSERT INTO `sys_role_menu` VALUES (1, 216525);
INSERT INTO `sys_role_menu` VALUES (1, 216526);
INSERT INTO `sys_role_menu` VALUES (1, 216527);
INSERT INTO `sys_role_menu` VALUES (1, 216530);
INSERT INTO `sys_role_menu` VALUES (1, 216531);
INSERT INTO `sys_role_menu` VALUES (1, 216542);
INSERT INTO `sys_role_menu` VALUES (1, 216543);
INSERT INTO `sys_role_menu` VALUES (1, 216544);
INSERT INTO `sys_role_menu` VALUES (1, 216545);
INSERT INTO `sys_role_menu` VALUES (1, 216546);
INSERT INTO `sys_role_menu` VALUES (1, 216547);
INSERT INTO `sys_role_menu` VALUES (1, 216548);
INSERT INTO `sys_role_menu` VALUES (1, 216549);
INSERT INTO `sys_role_menu` VALUES (1, 216550);
INSERT INTO `sys_role_menu` VALUES (1, 216551);
INSERT INTO `sys_role_menu` VALUES (1, 216552);
INSERT INTO `sys_role_menu` VALUES (2, 2101);
INSERT INTO `sys_role_menu` VALUES (2, 2110);
INSERT INTO `sys_role_menu` VALUES (2, 2111);
INSERT INTO `sys_role_menu` VALUES (2, 2112);
INSERT INTO `sys_role_menu` VALUES (2, 2113);
INSERT INTO `sys_role_menu` VALUES (2, 2114);
INSERT INTO `sys_role_menu` VALUES (2, 2120);
INSERT INTO `sys_role_menu` VALUES (2, 2121);
INSERT INTO `sys_role_menu` VALUES (2, 2122);
INSERT INTO `sys_role_menu` VALUES (2, 2123);
INSERT INTO `sys_role_menu` VALUES (2, 2124);
INSERT INTO `sys_role_menu` VALUES (2, 2125);
INSERT INTO `sys_role_menu` VALUES (2, 2130);
INSERT INTO `sys_role_menu` VALUES (2, 2132);
INSERT INTO `sys_role_menu` VALUES (2, 2140);
INSERT INTO `sys_role_menu` VALUES (2, 21111);
INSERT INTO `sys_role_menu` VALUES (2, 21112);
INSERT INTO `sys_role_menu` VALUES (2, 21113);
INSERT INTO `sys_role_menu` VALUES (2, 21114);
INSERT INTO `sys_role_menu` VALUES (2, 21115);
INSERT INTO `sys_role_menu` VALUES (2, 21121);
INSERT INTO `sys_role_menu` VALUES (2, 21122);
INSERT INTO `sys_role_menu` VALUES (2, 21123);
INSERT INTO `sys_role_menu` VALUES (2, 21124);
INSERT INTO `sys_role_menu` VALUES (2, 21125);
INSERT INTO `sys_role_menu` VALUES (2, 21131);
INSERT INTO `sys_role_menu` VALUES (2, 21132);
INSERT INTO `sys_role_menu` VALUES (2, 21133);
INSERT INTO `sys_role_menu` VALUES (2, 21134);
INSERT INTO `sys_role_menu` VALUES (2, 21135);
INSERT INTO `sys_role_menu` VALUES (2, 21136);
INSERT INTO `sys_role_menu` VALUES (2, 21141);
INSERT INTO `sys_role_menu` VALUES (2, 21142);
INSERT INTO `sys_role_menu` VALUES (2, 21143);
INSERT INTO `sys_role_menu` VALUES (2, 21144);
INSERT INTO `sys_role_menu` VALUES (2, 21145);
INSERT INTO `sys_role_menu` VALUES (2, 21211);
INSERT INTO `sys_role_menu` VALUES (2, 21212);
INSERT INTO `sys_role_menu` VALUES (2, 21213);
INSERT INTO `sys_role_menu` VALUES (2, 21214);
INSERT INTO `sys_role_menu` VALUES (2, 21215);
INSERT INTO `sys_role_menu` VALUES (2, 21231);
INSERT INTO `sys_role_menu` VALUES (2, 21232);
INSERT INTO `sys_role_menu` VALUES (2, 21233);
INSERT INTO `sys_role_menu` VALUES (2, 21234);
INSERT INTO `sys_role_menu` VALUES (2, 21235);
INSERT INTO `sys_role_menu` VALUES (2, 21241);
INSERT INTO `sys_role_menu` VALUES (2, 21242);
INSERT INTO `sys_role_menu` VALUES (2, 21243);
INSERT INTO `sys_role_menu` VALUES (2, 21244);
INSERT INTO `sys_role_menu` VALUES (2, 21245);
INSERT INTO `sys_role_menu` VALUES (2, 21251);
INSERT INTO `sys_role_menu` VALUES (2, 21252);
INSERT INTO `sys_role_menu` VALUES (2, 21253);
INSERT INTO `sys_role_menu` VALUES (2, 21254);
INSERT INTO `sys_role_menu` VALUES (2, 21255);
INSERT INTO `sys_role_menu` VALUES (2, 21321);
INSERT INTO `sys_role_menu` VALUES (2, 21322);
INSERT INTO `sys_role_menu` VALUES (2, 21323);
INSERT INTO `sys_role_menu` VALUES (2, 21324);
INSERT INTO `sys_role_menu` VALUES (2, 21325);
INSERT INTO `sys_role_menu` VALUES (2, 21401);
INSERT INTO `sys_role_menu` VALUES (2, 21402);
INSERT INTO `sys_role_menu` VALUES (2, 21403);
INSERT INTO `sys_role_menu` VALUES (2, 21404);
INSERT INTO `sys_role_menu` VALUES (2, 21405);
INSERT INTO `sys_role_menu` VALUES (2, 21406);
INSERT INTO `sys_role_menu` VALUES (2, 21407);
INSERT INTO `sys_role_menu` VALUES (2, 21408);
INSERT INTO `sys_role_menu` VALUES (2, 21409);
INSERT INTO `sys_role_menu` VALUES (2, 21410);
INSERT INTO `sys_role_menu` VALUES (2, 21411);
INSERT INTO `sys_role_menu` VALUES (2, 21412);
INSERT INTO `sys_role_menu` VALUES (2, 21413);
INSERT INTO `sys_role_menu` VALUES (2, 21414);
INSERT INTO `sys_role_menu` VALUES (2, 21436);
INSERT INTO `sys_role_menu` VALUES (2, 21437);
INSERT INTO `sys_role_menu` VALUES (2, 21438);
INSERT INTO `sys_role_menu` VALUES (2, 21439);
INSERT INTO `sys_role_menu` VALUES (2, 21440);
INSERT INTO `sys_role_menu` VALUES (2, 21441);
INSERT INTO `sys_role_menu` VALUES (2, 21442);
INSERT INTO `sys_role_menu` VALUES (2, 21443);
INSERT INTO `sys_role_menu` VALUES (2, 21444);
INSERT INTO `sys_role_menu` VALUES (2, 21445);
INSERT INTO `sys_role_menu` VALUES (2, 21446);
INSERT INTO `sys_role_menu` VALUES (2, 21447);
INSERT INTO `sys_role_menu` VALUES (2, 21448);
INSERT INTO `sys_role_menu` VALUES (2, 21449);
INSERT INTO `sys_role_menu` VALUES (2, 21450);
INSERT INTO `sys_role_menu` VALUES (2, 21451);
INSERT INTO `sys_role_menu` VALUES (2, 21452);
INSERT INTO `sys_role_menu` VALUES (2, 21453);
INSERT INTO `sys_role_menu` VALUES (2, 21454);
INSERT INTO `sys_role_menu` VALUES (2, 21455);
INSERT INTO `sys_role_menu` VALUES (2, 21456);
INSERT INTO `sys_role_menu` VALUES (2, 21463);
INSERT INTO `sys_role_menu` VALUES (2, 21464);
INSERT INTO `sys_role_menu` VALUES (2, 21467);
INSERT INTO `sys_role_menu` VALUES (2, 21468);
INSERT INTO `sys_role_menu` VALUES (2, 21469);
INSERT INTO `sys_role_menu` VALUES (2, 21470);
INSERT INTO `sys_role_menu` VALUES (2, 21471);
INSERT INTO `sys_role_menu` VALUES (2, 21472);
INSERT INTO `sys_role_menu` VALUES (2, 21473);
INSERT INTO `sys_role_menu` VALUES (2, 21474);
INSERT INTO `sys_role_menu` VALUES (2, 21475);
INSERT INTO `sys_role_menu` VALUES (2, 21476);
INSERT INTO `sys_role_menu` VALUES (2, 21477);
INSERT INTO `sys_role_menu` VALUES (2, 21478);
INSERT INTO `sys_role_menu` VALUES (2, 21479);
INSERT INTO `sys_role_menu` VALUES (2, 21480);
INSERT INTO `sys_role_menu` VALUES (2, 21481);
INSERT INTO `sys_role_menu` VALUES (2, 21482);
INSERT INTO `sys_role_menu` VALUES (2, 21483);
INSERT INTO `sys_role_menu` VALUES (2, 21484);
INSERT INTO `sys_role_menu` VALUES (2, 21485);
INSERT INTO `sys_role_menu` VALUES (2, 21486);
INSERT INTO `sys_role_menu` VALUES (2, 21487);
INSERT INTO `sys_role_menu` VALUES (2, 21488);
INSERT INTO `sys_role_menu` VALUES (2, 21489);
INSERT INTO `sys_role_menu` VALUES (2, 21490);
INSERT INTO `sys_role_menu` VALUES (2, 21491);
INSERT INTO `sys_role_menu` VALUES (2, 21492);
INSERT INTO `sys_role_menu` VALUES (2, 21500);
INSERT INTO `sys_role_menu` VALUES (2, 21501);
INSERT INTO `sys_role_menu` VALUES (2, 21502);
INSERT INTO `sys_role_menu` VALUES (2, 21503);
INSERT INTO `sys_role_menu` VALUES (2, 21504);
INSERT INTO `sys_role_menu` VALUES (2, 21505);
INSERT INTO `sys_role_menu` VALUES (2, 21506);
INSERT INTO `sys_role_menu` VALUES (2, 21507);
INSERT INTO `sys_role_menu` VALUES (2, 21508);
INSERT INTO `sys_role_menu` VALUES (2, 21509);
INSERT INTO `sys_role_menu` VALUES (2, 21510);
INSERT INTO `sys_role_menu` VALUES (2, 21511);
INSERT INTO `sys_role_menu` VALUES (2, 21512);
INSERT INTO `sys_role_menu` VALUES (2, 21513);
INSERT INTO `sys_role_menu` VALUES (2, 21514);
INSERT INTO `sys_role_menu` VALUES (2, 21515);
INSERT INTO `sys_role_menu` VALUES (2, 21516);
INSERT INTO `sys_role_menu` VALUES (2, 21517);
INSERT INTO `sys_role_menu` VALUES (2, 21520);
INSERT INTO `sys_role_menu` VALUES (2, 21539);
INSERT INTO `sys_role_menu` VALUES (2, 21540);
INSERT INTO `sys_role_menu` VALUES (2, 21541);
INSERT INTO `sys_role_menu` VALUES (2, 21542);
INSERT INTO `sys_role_menu` VALUES (2, 21543);
INSERT INTO `sys_role_menu` VALUES (2, 21544);
INSERT INTO `sys_role_menu` VALUES (101, 2110);
INSERT INTO `sys_role_menu` VALUES (101, 2111);
INSERT INTO `sys_role_menu` VALUES (101, 2112);
INSERT INTO `sys_role_menu` VALUES (101, 2113);
INSERT INTO `sys_role_menu` VALUES (101, 2114);
INSERT INTO `sys_role_menu` VALUES (101, 2121);
INSERT INTO `sys_role_menu` VALUES (101, 2122);
INSERT INTO `sys_role_menu` VALUES (101, 2123);
INSERT INTO `sys_role_menu` VALUES (101, 2124);
INSERT INTO `sys_role_menu` VALUES (101, 2125);
INSERT INTO `sys_role_menu` VALUES (101, 2130);
INSERT INTO `sys_role_menu` VALUES (101, 2132);
INSERT INTO `sys_role_menu` VALUES (101, 2140);
INSERT INTO `sys_role_menu` VALUES (101, 21111);
INSERT INTO `sys_role_menu` VALUES (101, 21121);
INSERT INTO `sys_role_menu` VALUES (101, 21131);
INSERT INTO `sys_role_menu` VALUES (101, 21132);
INSERT INTO `sys_role_menu` VALUES (101, 21141);
INSERT INTO `sys_role_menu` VALUES (101, 21211);
INSERT INTO `sys_role_menu` VALUES (101, 21231);
INSERT INTO `sys_role_menu` VALUES (101, 21232);
INSERT INTO `sys_role_menu` VALUES (101, 21233);
INSERT INTO `sys_role_menu` VALUES (101, 21234);
INSERT INTO `sys_role_menu` VALUES (101, 21241);
INSERT INTO `sys_role_menu` VALUES (101, 21242);
INSERT INTO `sys_role_menu` VALUES (101, 21251);
INSERT INTO `sys_role_menu` VALUES (101, 21321);
INSERT INTO `sys_role_menu` VALUES (101, 21401);
INSERT INTO `sys_role_menu` VALUES (101, 21402);
INSERT INTO `sys_role_menu` VALUES (101, 21436);
INSERT INTO `sys_role_menu` VALUES (101, 21437);
INSERT INTO `sys_role_menu` VALUES (101, 21438);
INSERT INTO `sys_role_menu` VALUES (101, 21439);
INSERT INTO `sys_role_menu` VALUES (101, 21442);
INSERT INTO `sys_role_menu` VALUES (101, 21443);
INSERT INTO `sys_role_menu` VALUES (101, 21444);
INSERT INTO `sys_role_menu` VALUES (101, 21448);
INSERT INTO `sys_role_menu` VALUES (101, 21449);
INSERT INTO `sys_role_menu` VALUES (101, 21450);
INSERT INTO `sys_role_menu` VALUES (101, 21455);
INSERT INTO `sys_role_menu` VALUES (101, 21456);
INSERT INTO `sys_role_menu` VALUES (101, 21463);
INSERT INTO `sys_role_menu` VALUES (101, 21464);
INSERT INTO `sys_role_menu` VALUES (101, 21470);
INSERT INTO `sys_role_menu` VALUES (101, 21471);
INSERT INTO `sys_role_menu` VALUES (101, 21476);
INSERT INTO `sys_role_menu` VALUES (101, 21477);
INSERT INTO `sys_role_menu` VALUES (101, 21478);
INSERT INTO `sys_role_menu` VALUES (101, 21482);
INSERT INTO `sys_role_menu` VALUES (101, 21483);
INSERT INTO `sys_role_menu` VALUES (101, 21488);
INSERT INTO `sys_role_menu` VALUES (101, 21489);
INSERT INTO `sys_role_menu` VALUES (101, 21492);
INSERT INTO `sys_role_menu` VALUES (101, 21500);
INSERT INTO `sys_role_menu` VALUES (101, 21501);
INSERT INTO `sys_role_menu` VALUES (101, 21502);
INSERT INTO `sys_role_menu` VALUES (101, 21506);
INSERT INTO `sys_role_menu` VALUES (101, 21507);
INSERT INTO `sys_role_menu` VALUES (101, 21512);
INSERT INTO `sys_role_menu` VALUES (101, 21513);
INSERT INTO `sys_role_menu` VALUES (101, 21520);
INSERT INTO `sys_role_menu` VALUES (101, 21539);
INSERT INTO `sys_role_menu` VALUES (101, 21615);
INSERT INTO `sys_role_menu` VALUES (101, 21616);
INSERT INTO `sys_role_menu` VALUES (101, 21617);
INSERT INTO `sys_role_menu` VALUES (101, 21652);
INSERT INTO `sys_role_menu` VALUES (101, 216521);

-- ----------------------------
-- Table structure for sys_user
-- ----------------------------
DROP TABLE IF EXISTS `sys_user`;
CREATE TABLE `sys_user`  (
  `user_id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `dept_id` bigint NULL DEFAULT NULL COMMENT '部门ID',
  `user_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '用户账号',
  `nick_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '用户昵称',
  `user_type` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '00' COMMENT '用户类型（00系统用户 01小程序用户 02混合用户）',
  `email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '用户邮箱',
  `phonenumber` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '手机号码',
  `sex` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '0' COMMENT '用户性别（0男 1女 2未知）',
  `avatar` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '用户头像',
  `password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '密码',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '0' COMMENT '账号状态（0正常 1停用）',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `login_ip` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '最后登录IP',
  `login_date` datetime NULL DEFAULT NULL COMMENT '最后登录时间',
  `pwd_update_date` datetime NULL DEFAULT NULL COMMENT '密码最后更新时间',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '备注',
  `weixin_nickname` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '微信昵称',
  `weixin_avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '微信头像',
  `openid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '微信OpenID',
  `unionid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '微信UnionID',
  `session_key` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '微信会话密钥',
  `real_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '姓名',
  `portrait_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '形象照',
  `birth_date` date NULL DEFAULT NULL COMMENT '出生年月日',
  `region` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '籍贯',
  `graduate_school` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '毕业院校',
  `graduation_year` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '毕业年份',
  `major` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '专业',
  `college` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '学院',
  `current_company` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '目前所属企业名称',
  `industry_field` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '行业领域',
  `position_title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '职位名称',
  `personal_introduction` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL COMMENT '个人介绍',
  `total_points` int NULL DEFAULT 0 COMMENT '总积分',
  `last_login_time` datetime NULL DEFAULT NULL COMMENT '最后登录时间',
  `profile_completion_rate` int NULL DEFAULT 0 COMMENT '资料完成度百分比(0-100)',
  `follower_count` int NULL DEFAULT 0 COMMENT '粉丝数量',
  `following_count` int NULL DEFAULT 0 COMMENT '关注数量',
  PRIMARY KEY (`user_id`) USING BTREE,
  UNIQUE INDEX `uk_openid`(`openid` ASC) USING BTREE COMMENT '微信OpenID唯一索引',
  INDEX `idx_user_type`(`user_type` ASC) USING BTREE COMMENT '用户类型索引',
  INDEX `idx_real_name`(`real_name` ASC) USING BTREE COMMENT '真实姓名索引',
  INDEX `idx_graduate_school`(`graduate_school` ASC) USING BTREE COMMENT '毕业院校索引',
  INDEX `idx_current_company`(`current_company` ASC) USING BTREE COMMENT '当前公司索引',
  INDEX `idx_total_points`(`total_points` ASC) USING BTREE COMMENT '积分索引',
  INDEX `idx_follower_count`(`follower_count` ASC) USING BTREE,
  INDEX `idx_following_count`(`following_count` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 304 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci COMMENT = '用户信息表（支持系统用户和小程序用户）' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_user
-- ----------------------------
INSERT INTO `sys_user` VALUES (1, 101, 'admin', 'wll', '00', '<EMAIL>', '15888888888', '1', '/profile/avatar/2025/07/23/a5a808fe4d1240c08bc8a26a7b87d405.png', '$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2', '0', '0', '127.0.0.1', '2025-07-30 11:46:02', '2025-07-04 16:01:55', 'admin', '2025-07-04 16:01:55', '', '2025-07-30 03:46:02', '管理员', NULL, NULL, NULL, NULL, '', '', NULL, NULL, '', '', '', '', '', '', '', '', NULL, 0, NULL, 0, 0, 0);

-- ----------------------------
-- Table structure for sys_user_post
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_post`;
CREATE TABLE `sys_user_post`  (
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `post_id` bigint NOT NULL COMMENT '岗位ID',
  PRIMARY KEY (`user_id`, `post_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci COMMENT = '用户与岗位关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_user_post
-- ----------------------------
INSERT INTO `sys_user_post` VALUES (1, 1);

-- ----------------------------
-- Table structure for sys_user_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_role`;
CREATE TABLE `sys_user_role`  (
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `role_id` bigint NOT NULL COMMENT '角色ID',
  PRIMARY KEY (`user_id`, `role_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci COMMENT = '用户和角色关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_user_role
-- ----------------------------
INSERT INTO `sys_user_role` VALUES (1, 1);

-- ----------------------------
-- Table structure for xiqing_activity_content
-- ----------------------------
DROP TABLE IF EXISTS `xiqing_activity_content`;
CREATE TABLE `xiqing_activity_content`  (
  `content_id` bigint NOT NULL AUTO_INCREMENT COMMENT '内容ID',
  `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '活动标题',
  `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '活动内容（富文本）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`content_id`) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_create_time`(`create_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '西青金种子专区活动内容管理表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of xiqing_activity_content
-- ----------------------------
INSERT INTO `xiqing_activity_content` VALUES (1, '西青金种子路演专区活动', '<p><img src=\"http://************:8086/profile/upload/2025/07/29/pahckcpk_20250729160849A001.png\"></p>', '0', 'admin', '2025-07-28 06:55:50', '', '2025-07-29 08:08:53', '嘻嘻。');

-- ----------------------------
-- Table structure for xiqing_roadshow_activity
-- ----------------------------
DROP TABLE IF EXISTS `xiqing_roadshow_activity`;
CREATE TABLE `xiqing_roadshow_activity`  (
  `activity_id` bigint NOT NULL AUTO_INCREMENT COMMENT '活动ID',
  `activity_title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '活动标题',
  `activity_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '活动描述',
  `start_time` datetime NULL DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime NULL DEFAULT NULL COMMENT '结束时间',
  `location` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '活动地点',
  `max_participants` int NULL DEFAULT NULL COMMENT '最大参与人数',
  `current_participants` int NULL DEFAULT 0 COMMENT '当前报名人数',
  `form_config` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '报名表单配置（JSON格式）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `sort_order` int NULL DEFAULT 0 COMMENT '排序',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`activity_id`) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_sort_order`(`sort_order` ASC) USING BTREE,
  INDEX `idx_create_time`(`create_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '西青金种子路演活动配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of xiqing_roadshow_activity
-- ----------------------------
INSERT INTO `xiqing_roadshow_activity` VALUES (1, '西青金种子路演大赛', '面向创新创业团队的路演展示活动，提供投资对接机会', '2025-02-01 09:00:00', '2025-02-28 18:00:00', '西青区创业园', 100, 6, '[{\"label\":\"联系人姓名\",\"name\":\"name\",\"type\":\"input\",\"required\":true,\"options\":\"\"},{\"label\":\"联系电话\",\"name\":\"phone\",\"type\":\"tel\",\"required\":true,\"options\":\"\"},{\"label\":\"公司名称\",\"name\":\"email\",\"type\":\"input\",\"required\":true,\"options\":\"\"},{\"label\":\"联系人职务\",\"name\":\"company\",\"type\":\"input\",\"required\":true,\"options\":\"\"},{\"label\":\"项目名称\",\"name\":\"project_name\",\"type\":\"input\",\"required\":true,\"options\":\"\"},{\"label\":\"所属行业\",\"name\":\"project_stage\",\"type\":\"input\",\"required\":true,\"options\":\"概念阶段,开发阶段,测试阶段,上线阶段\"},{\"label\":\"申报赛道\",\"name\":\"team_size\",\"type\":\"radio\",\"required\":true,\"options\":\"新能源新材料与节能环保,生物医药大健康,新一代信息技术与智能制造\"},{\"label\":\"公司/项目简介\",\"name\":\"funding_needs\",\"type\":\"input\",\"required\":true,\"options\":\"\"},{\"label\":\"项目来源\",\"name\":\"business_plan\",\"type\":\"radio_other\",\"required\":true,\"options\":\"社会,高校\"},{\"label\":\"公司注册时间及地点\",\"name\":\"project_description\",\"type\":\"input\",\"required\":true,\"options\":\"\"},{\"name\":\"注册资本金\",\"label\":\"注册资本金(万)\",\"type\":\"number\",\"required\":false,\"options\":\"\"},{\"name\":\"实缴资金\",\"label\":\"实缴资金(万)\",\"type\":\"number\",\"required\":false,\"options\":\"\"},{\"name\":\"融资\",\"label\":\"融资阶段\",\"type\":\"input\",\"required\":true,\"options\":\"\"},{\"name\":\"资金\",\"label\":\"资金/厂房需求\",\"type\":\"input\",\"required\":true,\"options\":\"\"},{\"name\":\"company\",\"label\":\"公司/项目附加文字说明\",\"type\":\"textarea\",\"required\":false,\"options\":\"\"},{\"name\":\"company\",\"label\":\"公司/项目附加文件说明\",\"type\":\"file\",\"required\":false,\"options\":\"\"},{\"name\":\"玩不玩元神\",\"label\":\"玩不玩原神？\",\"type\":\"checkbox\",\"required\":false,\"options\":\"不玩,不是很想玩,无所谓,想玩,特别想玩,热爱\"},{\"name\":\"玩不玩\",\"label\":\"给我玩原神\",\"type\":\"select\",\"required\":false,\"options\":\"好,不好,我无所谓\"},{\"name\":\"启动\",\"label\":\"何时启动原神？\",\"type\":\"date\",\"required\":false,\"options\":\"\"},{\"name\":\"名太难\",\"label\":\"我要卸载原神\",\"type\":\"checkbox_other\",\"required\":false,\"options\":\"不想,想,我无所谓\"},{\"name\":\"我\",\"label\":\"原神🐕都不玩\",\"type\":\"select_other\",\"required\":false,\"options\":\"正确的,客观的,公平公正的\"}]', '0', 1, 'admin', '2025-07-28 07:56:25', '', '2025-07-29 07:21:27', NULL);

-- ----------------------------
-- Table structure for xiqing_roadshow_registration
-- ----------------------------
DROP TABLE IF EXISTS `xiqing_roadshow_registration`;
CREATE TABLE `xiqing_roadshow_registration`  (
  `registration_id` bigint NOT NULL AUTO_INCREMENT COMMENT '报名ID',
  `activity_id` bigint NOT NULL COMMENT '活动ID',
  `user_id` bigint NULL DEFAULT NULL COMMENT '用户ID',
  `form_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '表单数据（JSON格式）',
  `registration_time` datetime NULL DEFAULT NULL COMMENT '报名时间',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '审核状态（0待审核 1通过 2拒绝）',
  `audit_remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '审核备注',
  `audit_time` datetime NULL DEFAULT NULL COMMENT '审核时间',
  `audit_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '审核人',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`registration_id`) USING BTREE,
  INDEX `idx_activity_id`(`activity_id` ASC) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_registration_time`(`registration_time` ASC) USING BTREE,
  INDEX `idx_create_time`(`create_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '西青金种子路演报名记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of xiqing_roadshow_registration
-- ----------------------------

SET FOREIGN_KEY_CHECKS = 1;
