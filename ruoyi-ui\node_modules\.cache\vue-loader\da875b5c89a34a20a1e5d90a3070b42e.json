{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\xiqing\\activity-config\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\xiqing\\activity-config\\index.vue", "mtime": 1753758422831}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBnZXRBY3Rpdml0eUNvbmZpZywgdXBkYXRlQWN0aXZpdHlDb25maWcgfSBmcm9tICJAL2FwaS9taW5pYXBwL3hpcWluZy9hY3Rpdml0eS1jb25maWciOw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJYaXFpbmdBY3Rpdml0eUNvbmZpZyIsDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIC8vIOmBrue9qeWxgg0KICAgICAgbG9hZGluZzogdHJ1ZSwNCiAgICAgIC8vIOaYr+WQpuaYvuekuuihqOWNlemFjee9ruW8ueWHuuWxgg0KICAgICAgZm9ybUNvbmZpZ09wZW46IGZhbHNlLA0KICAgICAgLy8g5piv5ZCm5pi+56S66KGo5Y2V6aKE6KeI5by55Ye65bGCDQogICAgICBwcmV2aWV3RGlhbG9nVmlzaWJsZTogZmFsc2UsDQogICAgICAvLyDooajljZXlrZfmrrXphY3nva4NCiAgICAgIGZvcm1GaWVsZHM6IFtdLA0KICAgICAgLy8g5rS75YqoSUTvvIjlm7rlrprkuLox77yM5Zug5Li65Y+q5pyJ5LiA5Liq6Lev5ryU5rS75Yqo6YWN572u77yJDQogICAgICBhY3Rpdml0eUlkOiAxDQogICAgfTsNCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICB0aGlzLmxvYWRGb3JtQ29uZmlnKCk7DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICAvKiog5Yqg6L296KGo5Y2V6YWN572uICovDQogICAgbG9hZEZvcm1Db25maWcoKSB7DQogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOw0KICAgICAgZ2V0QWN0aXZpdHlDb25maWcodGhpcy5hY3Rpdml0eUlkKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEgJiYgcmVzcG9uc2UuZGF0YS5mb3JtQ29uZmlnKSB7DQogICAgICAgICAgdHJ5IHsNCiAgICAgICAgICAgIHRoaXMuZm9ybUZpZWxkcyA9IEpTT04ucGFyc2UocmVzcG9uc2UuZGF0YS5mb3JtQ29uZmlnKTsNCiAgICAgICAgICB9IGNhdGNoIChlKSB7DQogICAgICAgICAgICB0aGlzLmZvcm1GaWVsZHMgPSBbXTsNCiAgICAgICAgICB9DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy5mb3JtRmllbGRzID0gW107DQogICAgICAgIH0NCiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICB9KS5jYXRjaCgoKSA9PiB7DQogICAgICAgIHRoaXMuZm9ybUZpZWxkcyA9IFtdOw0KICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOihqOWNlemFjee9ruaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZUZvcm1Db25maWcoKSB7DQogICAgICB0aGlzLmZvcm1Db25maWdPcGVuID0gdHJ1ZTsNCiAgICB9LA0KICAgIC8qKiDmt7vliqDooajljZXlrZfmrrUgKi8NCiAgICBhZGRGb3JtRmllbGQoKSB7DQogICAgICB0aGlzLmZvcm1GaWVsZHMucHVzaCh7DQogICAgICAgIG5hbWU6ICcnLA0KICAgICAgICBsYWJlbDogJycsDQogICAgICAgIHR5cGU6ICdpbnB1dCcsDQogICAgICAgIHJlcXVpcmVkOiBmYWxzZSwNCiAgICAgICAgb3B0aW9uczogJycNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOWIoOmZpOihqOWNleWtl+autSAqLw0KICAgIHJlbW92ZUZvcm1GaWVsZChpbmRleCkgew0KICAgICAgdGhpcy5mb3JtRmllbGRzLnNwbGljZShpbmRleCwgMSk7DQogICAgfSwNCiAgICAvKiog56e75Yqo5a2X5q615L2N572uICovDQogICAgbW92ZUZpZWxkKGluZGV4LCBkaXJlY3Rpb24pIHsNCiAgICAgIGNvbnN0IG5ld0luZGV4ID0gaW5kZXggKyBkaXJlY3Rpb247DQogICAgICBpZiAobmV3SW5kZXggPj0gMCAmJiBuZXdJbmRleCA8IHRoaXMuZm9ybUZpZWxkcy5sZW5ndGgpIHsNCiAgICAgICAgY29uc3QgdGVtcCA9IHRoaXMuZm9ybUZpZWxkc1tpbmRleF07DQogICAgICAgIHRoaXMuJHNldCh0aGlzLmZvcm1GaWVsZHMsIGluZGV4LCB0aGlzLmZvcm1GaWVsZHNbbmV3SW5kZXhdKTsNCiAgICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybUZpZWxkcywgbmV3SW5kZXgsIHRlbXApOw0KICAgICAgfQ0KICAgIH0sDQogICAgLyoqIOabtOaWsOWtl+auteWQjeensCAqLw0KICAgIHVwZGF0ZUZpZWxkTmFtZShmaWVsZCwgbGFiZWwpIHsNCiAgICAgIGlmICghZmllbGQubmFtZSB8fCBmaWVsZC5uYW1lID09PSAnJykgew0KICAgICAgICBmaWVsZC5uYW1lID0gdGhpcy5nZW5lcmF0ZUZpZWxkTmFtZShsYWJlbCk7DQogICAgICB9DQogICAgfSwNCiAgICAvKiog55Sf5oiQ5a2X5q615ZCN56ewICovDQogICAgZ2VuZXJhdGVGaWVsZE5hbWUobGFiZWwpIHsNCiAgICAgIGNvbnN0IHBpbnlpbiA9IHsNCiAgICAgICAgJ+Wnk+WQjSc6ICduYW1lJywNCiAgICAgICAgJ+iBlOezu+eUteivnSc6ICdwaG9uZScsDQogICAgICAgICfnlLXor50nOiAncGhvbmUnLA0KICAgICAgICAn6YKu566xJzogJ2VtYWlsJywNCiAgICAgICAgJ+mCrueuseWcsOWdgCc6ICdlbWFpbCcsDQogICAgICAgICflhazlj7gnOiAnY29tcGFueScsDQogICAgICAgICfpobnnm67lkI3np7AnOiAncHJvamVjdF9uYW1lJywNCiAgICAgICAgJ+mhueebruaPj+i/sCc6ICdwcm9qZWN0X2Rlc2NyaXB0aW9uJywNCiAgICAgICAgJ+WboumYn+inhOaooSc6ICd0ZWFtX3NpemUnDQogICAgICB9Ow0KICAgICAgcmV0dXJuIHBpbnlpbltsYWJlbF0gfHwgbGFiZWwudG9Mb3dlckNhc2UoKS5yZXBsYWNlKC9ccysvZywgJ18nKTsNCiAgICB9LA0KICAgIC8qKiDojrflj5blrZfmrrXlm77moIcgKi8NCiAgICBnZXRGaWVsZEljb24odHlwZSkgew0KICAgICAgY29uc3QgaWNvbnMgPSB7DQogICAgICAgIGlucHV0OiAnZWwtaWNvbi1lZGl0JywNCiAgICAgICAgdGV4dGFyZWE6ICdlbC1pY29uLWRvY3VtZW50JywNCiAgICAgICAgbnVtYmVyOiAnZWwtaWNvbi1zLWRhdGEnLA0KICAgICAgICBlbWFpbDogJ2VsLWljb24tbWVzc2FnZScsDQogICAgICAgIHRlbDogJ2VsLWljb24tcGhvbmUnLA0KICAgICAgICByYWRpbzogJ2VsLWljb24tc3VjY2VzcycsDQogICAgICAgIGNoZWNrYm94OiAnZWwtaWNvbi1jaGVjaycsDQogICAgICAgIHNlbGVjdDogJ2VsLWljb24tYXJyb3ctZG93bicsDQogICAgICAgIHJhZGlvX290aGVyOiAnZWwtaWNvbi1jaXJjbGUtcGx1cycsDQogICAgICAgIGNoZWNrYm94X290aGVyOiAnZWwtaWNvbi1zcXVhcmUtcGx1cycsDQogICAgICAgIHNlbGVjdF9vdGhlcjogJ2VsLWljb24tcGx1cycsDQogICAgICAgIGRhdGU6ICdlbC1pY29uLWRhdGUnLA0KICAgICAgICBmaWxlOiAnZWwtaWNvbi11cGxvYWQnDQogICAgICB9Ow0KICAgICAgcmV0dXJuIGljb25zW3R5cGVdIHx8ICdlbC1pY29uLWVkaXQnOw0KICAgIH0sDQogICAgLyoqIOWkhOeQhuaooeadv+WRveS7pCAqLw0KICAgIGhhbmRsZVRlbXBsYXRlQ29tbWFuZChjb21tYW5kKSB7DQogICAgICBpZiAoY29tbWFuZCA9PT0gJ2NsZWFyJykgew0KICAgICAgICB0aGlzLiRjb25maXJtKCfnoa7lrpropoHmuIXnqbrmiYDmnInlrZfmrrXlkJfvvJ8nLCAn5o+Q56S6Jywgew0KICAgICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywNCiAgICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywNCiAgICAgICAgICB0eXBlOiAnd2FybmluZycNCiAgICAgICAgfSkudGhlbigoKSA9PiB7DQogICAgICAgICAgdGhpcy5mb3JtRmllbGRzID0gW107DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCflt7LmuIXnqbrmiYDmnInlrZfmrrUnKTsNCiAgICAgICAgfSk7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCg0KICAgICAgY29uc3QgdGVtcGxhdGVzID0gew0KICAgICAgICBiYXNpYzogWw0KICAgICAgICAgIHsgbGFiZWw6ICflp5PlkI0nLCBuYW1lOiAnbmFtZScsIHR5cGU6ICdpbnB1dCcsIHJlcXVpcmVkOiB0cnVlLCBvcHRpb25zOiAnJyB9LA0KICAgICAgICAgIHsgbGFiZWw6ICfogZTns7vnlLXor50nLCBuYW1lOiAncGhvbmUnLCB0eXBlOiAndGVsJywgcmVxdWlyZWQ6IHRydWUsIG9wdGlvbnM6ICcnIH0sDQogICAgICAgICAgeyBsYWJlbDogJ+mCrueuseWcsOWdgCcsIG5hbWU6ICdlbWFpbCcsIHR5cGU6ICdlbWFpbCcsIHJlcXVpcmVkOiBmYWxzZSwgb3B0aW9uczogJycgfQ0KICAgICAgICBdLA0KICAgICAgICByb2Fkc2hvdzogWw0KICAgICAgICAgIHsgbGFiZWw6ICflp5PlkI0nLCBuYW1lOiAnbmFtZScsIHR5cGU6ICdpbnB1dCcsIHJlcXVpcmVkOiB0cnVlLCBvcHRpb25zOiAnJyB9LA0KICAgICAgICAgIHsgbGFiZWw6ICfogZTns7vnlLXor50nLCBuYW1lOiAncGhvbmUnLCB0eXBlOiAndGVsJywgcmVxdWlyZWQ6IHRydWUsIG9wdGlvbnM6ICcnIH0sDQogICAgICAgICAgeyBsYWJlbDogJ+mCrueuseWcsOWdgCcsIG5hbWU6ICdlbWFpbCcsIHR5cGU6ICdlbWFpbCcsIHJlcXVpcmVkOiB0cnVlLCBvcHRpb25zOiAnJyB9LA0KICAgICAgICAgIHsgbGFiZWw6ICflhazlj7gv5Zui6ZifJywgbmFtZTogJ2NvbXBhbnknLCB0eXBlOiAnaW5wdXQnLCByZXF1aXJlZDogdHJ1ZSwgb3B0aW9uczogJycgfSwNCiAgICAgICAgICB7IGxhYmVsOiAn6aG555uu5ZCN56ewJywgbmFtZTogJ3Byb2plY3RfbmFtZScsIHR5cGU6ICdpbnB1dCcsIHJlcXVpcmVkOiB0cnVlLCBvcHRpb25zOiAnJyB9LA0KICAgICAgICAgIHsgbGFiZWw6ICfpobnnm67mnaXmupAnLCBuYW1lOiAncHJvamVjdF9zb3VyY2UnLCB0eXBlOiAncmFkaW9fb3RoZXInLCByZXF1aXJlZDogdHJ1ZSwgb3B0aW9uczogJ+ekvuS8mizpq5jmoKEs56eR56CU6Zmi5omALOS8geS4muWGhemDqCcgfSwNCiAgICAgICAgICB7IGxhYmVsOiAn6aG555uu6Zi25q61JywgbmFtZTogJ3Byb2plY3Rfc3RhZ2UnLCB0eXBlOiAncmFkaW8nLCByZXF1aXJlZDogdHJ1ZSwgb3B0aW9uczogJ+amguW/temYtuautSzlvIDlj5HpmLbmrrUs5rWL6K+V6Zi25q61LOS4iue6v+mYtuautScgfSwNCiAgICAgICAgICB7IGxhYmVsOiAn5Zui6Zif6KeE5qihJywgbmFtZTogJ3RlYW1fc2l6ZScsIHR5cGU6ICdzZWxlY3QnLCByZXF1aXJlZDogdHJ1ZSwgb3B0aW9uczogJzHkurosMi0z5Lq6LDQtNeS6uiw2LTEw5Lq6LDEw5Lq65Lul5LiKJyB9LA0KICAgICAgICAgIHsgbGFiZWw6ICfono3otYTpnIDmsYInLCBuYW1lOiAnZnVuZGluZ19uZWVkcycsIHR5cGU6ICdpbnB1dCcsIHJlcXVpcmVkOiBmYWxzZSwgb3B0aW9uczogJycgfSwNCiAgICAgICAgICB7IGxhYmVsOiAn5ZWG5Lia6K6h5YiS5LmmJywgbmFtZTogJ2J1c2luZXNzX3BsYW4nLCB0eXBlOiAnZmlsZScsIHJlcXVpcmVkOiB0cnVlLCBvcHRpb25zOiAnJyB9LA0KICAgICAgICAgIHsgbGFiZWw6ICfpobnnm67mj4/ov7AnLCBuYW1lOiAncHJvamVjdF9kZXNjcmlwdGlvbicsIHR5cGU6ICd0ZXh0YXJlYScsIHJlcXVpcmVkOiB0cnVlLCBvcHRpb25zOiAnJyB9DQogICAgICAgIF0NCiAgICAgIH07DQoNCiAgICAgIGlmICh0ZW1wbGF0ZXNbY29tbWFuZF0pIHsNCiAgICAgICAgdGhpcy5mb3JtRmllbGRzID0gdGVtcGxhdGVzW2NvbW1hbmRdOw0KICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+aooeadv+W6lOeUqOaIkOWKnycpOw0KICAgICAgfQ0KICAgIH0sDQogICAgLyoqIOmihOiniOihqOWNlSAqLw0KICAgIHByZXZpZXdGb3JtKCkgew0KICAgICAgaWYgKHRoaXMuZm9ybUZpZWxkcy5sZW5ndGggPT09IDApIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor7flhYjmt7vliqDooajljZXlrZfmrrUnKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KICAgICAgdGhpcy5wcmV2aWV3RGlhbG9nVmlzaWJsZSA9IHRydWU7DQogICAgfSwNCiAgICAvKiog5L+d5a2Y6KGo5Y2V6YWN572uICovDQogICAgc2F2ZUZvcm1Db25maWcoKSB7DQogICAgICBpZiAodGhpcy5mb3JtRmllbGRzLmxlbmd0aCA9PT0gMCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ivt+iHs+Wwkea3u+WKoOS4gOS4quihqOWNleWtl+autScpOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIC8vIOmqjOivgeWtl+autemFjee9rg0KICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCB0aGlzLmZvcm1GaWVsZHMubGVuZ3RoOyBpKyspIHsNCiAgICAgICAgY29uc3QgZmllbGQgPSB0aGlzLmZvcm1GaWVsZHNbaV07DQogICAgICAgIGlmICghZmllbGQubGFiZWwpIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKGDnrKwke2kgKyAxfeS4quWtl+auteeahOagh+etvuS4jeiDveS4uuepumApOw0KICAgICAgICAgIHJldHVybjsNCiAgICAgICAgfQ0KICAgICAgICBpZiAoWydyYWRpbycsICdjaGVja2JveCcsICdzZWxlY3QnLCAncmFkaW9fb3RoZXInLCAnY2hlY2tib3hfb3RoZXInLCAnc2VsZWN0X290aGVyJ10uaW5jbHVkZXMoZmllbGQudHlwZSkgJiYgIWZpZWxkLm9wdGlvbnMpIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKGDlrZfmrrUiJHtmaWVsZC5sYWJlbH0i6ZyA6KaB6YWN572u6YCJ6aG5YCk7DQogICAgICAgICAgcmV0dXJuOw0KICAgICAgICB9DQogICAgICB9DQoNCiAgICAgIGNvbnN0IGNvbmZpZ0RhdGEgPSBKU09OLnN0cmluZ2lmeSh0aGlzLmZvcm1GaWVsZHMpOw0KICAgICAgY29uc3QgdXBkYXRlRGF0YSA9IHsNCiAgICAgICAgYWN0aXZpdHlJZDogdGhpcy5hY3Rpdml0eUlkLA0KICAgICAgICBmb3JtQ29uZmlnOiBjb25maWdEYXRhDQogICAgICB9Ow0KDQogICAgICB1cGRhdGVBY3Rpdml0eUNvbmZpZyh1cGRhdGVEYXRhKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi6KGo5Y2V6YWN572u5L+d5a2Y5oiQ5YqfIik7DQogICAgICAgIHRoaXMuZm9ybUNvbmZpZ09wZW4gPSBmYWxzZTsNCiAgICAgICAgdGhpcy5sb2FkRm9ybUNvbmZpZygpOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog6I635Y+W5a2X5q6157G75Z6L5ZCN56ewICovDQogICAgZ2V0RmllbGRUeXBlTmFtZSh0eXBlKSB7DQogICAgICBjb25zdCB0eXBlTmFtZXMgPSB7DQogICAgICAgIGlucHV0OiAn5paH5pys6L6T5YWlJywNCiAgICAgICAgdGV4dGFyZWE6ICflpJrooYzmlofmnKwnLA0KICAgICAgICBudW1iZXI6ICfmlbDlrZfovpPlhaUnLA0KICAgICAgICBlbWFpbDogJ+mCrueusScsDQogICAgICAgIHRlbDogJ+eUteivnScsDQogICAgICAgIHJhZGlvOiAn5Y2V6YCJJywNCiAgICAgICAgY2hlY2tib3g6ICflpJrpgIknLA0KICAgICAgICBzZWxlY3Q6ICfkuIvmi4npgInmi6knLA0KICAgICAgICByYWRpb19vdGhlcjogJ+WNlemAiSvlhbbku5YnLA0KICAgICAgICBjaGVja2JveF9vdGhlcjogJ+WkmumAiSvlhbbku5YnLA0KICAgICAgICBzZWxlY3Rfb3RoZXI6ICfkuIvmi4kr5YW25LuWJywNCiAgICAgICAgZGF0ZTogJ+aXpeacnycsDQogICAgICAgIGZpbGU6ICfmlofku7bkuIrkvKAnDQogICAgICB9Ow0KICAgICAgcmV0dXJuIHR5cGVOYW1lc1t0eXBlXSB8fCAn5pyq55+l57G75Z6LJzsNCiAgICB9DQogIH0NCn07DQo="}, null]}