<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SysUserMapper">

    <resultMap type="SysUser" id="SysUserResult">
        <id     property="userId"        column="user_id"         />
        <result property="deptId"        column="dept_id"         />
        <result property="userName"      column="user_name"       />
        <result property="nickName"      column="nick_name"       />
        <result property="userType"      column="user_type"       />
        <result property="email"         column="email"           />
        <result property="phonenumber"   column="phonenumber"     />
        <result property="sex"           column="sex"             />
        <result property="avatar"        column="avatar"          />
        <result property="password"      column="password"        />
        <result property="status"        column="status"          />
        <result property="delFlag"       column="del_flag"        />
        <result property="loginIp"       column="login_ip"        />
        <result property="loginDate"     column="login_date"      />
        <result property="pwdUpdateDate" column="pwd_update_date" />
        <result property="createBy"      column="create_by"       />
        <result property="createTime"    column="create_time"     />
        <result property="updateBy"      column="update_by"       />
        <result property="updateTime"    column="update_time"     />
        <result property="remark"        column="remark"          />
        <result property="weixinNickname" column="weixin_nickname" />
        <result property="weixinAvatar"   column="weixin_avatar"   />
        <result property="openid"        column="openid"          />
        <result property="unionid"       column="unionid"         />
        <result property="sessionKey"    column="session_key"     />
        <result property="realName"      column="real_name"       />
        <result property="portraitUrl"   column="portrait_url"    />
        <result property="birthDate"     column="birth_date"      />
        <result property="region"        column="region"          />
        <result property="graduateSchool" column="graduate_school" />
        <result property="graduationYear" column="graduation_year" />
        <result property="major"         column="major"           />
        <result property="college"       column="college"         />
        <result property="currentCompany" column="current_company" />
        <result property="industryField" column="industry_field"  />
        <result property="positionTitle" column="position_title"  />
        <result property="personalIntroduction" column="personal_introduction" />
        <result property="profileCompletionRate" column="profile_completion_rate" />
        <result property="totalPoints"   column="total_points"    />
        <result property="lastLoginTime" column="last_login_time" />
        <result property="industryNames" column="industry_names"  />
        <association property="dept"     javaType="SysDept"         resultMap="deptResult" />
        <collection  property="roles"    javaType="java.util.List"  resultMap="RoleResult" />
    </resultMap>
	
    <resultMap id="deptResult" type="SysDept">
        <id     property="deptId"    column="dept_id"     />
        <result property="parentId"  column="parent_id"   />
        <result property="deptName"  column="dept_name"   />
        <result property="ancestors" column="ancestors"   />
        <result property="orderNum"  column="order_num"   />
        <result property="leader"    column="leader"      />
        <result property="status"    column="dept_status" />
    </resultMap>
	
    <resultMap id="RoleResult" type="SysRole">
        <id     property="roleId"       column="role_id"        />
        <result property="roleName"     column="role_name"      />
        <result property="roleKey"      column="role_key"       />
        <result property="roleSort"     column="role_sort"      />
        <result property="dataScope"    column="data_scope"     />
        <result property="status"       column="role_status"    />
    </resultMap>

    <!-- 用户列表查询专用的ResultMap，不包含角色和部门信息 -->
    <resultMap type="SysUser" id="SysUserListResult">
        <id     property="userId"        column="user_id"         />
        <result property="deptId"        column="dept_id"         />
        <result property="userName"      column="user_name"       />
        <result property="nickName"      column="nick_name"       />
        <result property="userType"      column="user_type"       />
        <result property="email"         column="email"           />
        <result property="phonenumber"   column="phonenumber"     />
        <result property="sex"           column="sex"             />
        <result property="avatar"        column="avatar"          />
        <result property="password"      column="password"        />
        <result property="status"        column="status"          />
        <result property="delFlag"       column="del_flag"        />
        <result property="loginIp"       column="login_ip"        />
        <result property="loginDate"     column="login_date"      />
        <result property="pwdUpdateDate" column="pwd_update_date" />
        <result property="createBy"      column="create_by"       />
        <result property="createTime"    column="create_time"     />
        <result property="updateBy"      column="update_by"       />
        <result property="updateTime"    column="update_time"     />
        <result property="remark"        column="remark"          />
        <result property="weixinNickname" column="weixin_nickname" />
        <result property="weixinAvatar"   column="weixin_avatar"   />
        <result property="openid"        column="openid"          />
        <result property="unionid"       column="unionid"         />
        <result property="sessionKey"    column="session_key"     />
        <result property="realName"      column="real_name"       />
        <result property="portraitUrl"   column="portrait_url"    />
        <result property="birthDate"     column="birth_date"      />
        <result property="region"        column="region"          />
        <result property="graduateSchool" column="graduate_school" />
        <result property="graduationYear" column="graduation_year" />
        <result property="major"         column="major"           />
        <result property="college"       column="college"         />
        <result property="currentCompany" column="current_company" />
        <result property="industryField" column="industry_field"  />
        <result property="positionTitle" column="position_title"  />
        <result property="personalIntroduction" column="personal_introduction" />
        <result property="profileCompletionRate" column="profile_completion_rate" />
        <result property="totalPoints"   column="total_points"    />
        <result property="lastLoginTime" column="last_login_time" />
        <result property="industryNames" column="industry_names"  />
        <!-- 注意：这里不包含dept的association映射和roles的collection映射 -->
    </resultMap>
	
	<sql id="selectUserVo">
        select u.user_id, u.dept_id, u.user_name, u.nick_name, u.user_type, u.email, u.avatar, u.phonenumber, u.password, u.sex, u.status, u.del_flag, u.login_ip, u.login_date, u.pwd_update_date, u.create_by, u.create_time, u.update_by, u.update_time, u.remark, u.weixin_nickname, u.weixin_avatar, u.openid, u.unionid, u.session_key, u.real_name, u.portrait_url, u.birth_date, u.region, u.graduate_school, u.graduation_year, u.major, u.college, u.current_company, u.industry_field, u.position_title, u.personal_introduction, u.total_points, u.last_login_time,u.profile_completion_rate,
        d.dept_id, d.parent_id, d.ancestors, d.dept_name, d.order_num, d.leader, d.status as dept_status,
        r.role_id, r.role_name, r.role_key, r.role_sort, r.data_scope, r.status as role_status
        from sys_user u
		    left join sys_dept d on u.dept_id = d.dept_id
		    left join sys_user_role ur on u.user_id = ur.user_id
		    left join sys_role r on r.role_id = ur.role_id
    </sql>
    
    <select id="selectUserList" parameterType="SysUser" resultMap="SysUserResult">
		select u.user_id, u.dept_id, u.nick_name, u.user_name, u.user_type, u.email, u.avatar, u.phonenumber, u.sex, u.status, u.del_flag, u.login_ip, u.login_date, u.pwd_update_date, u.create_by, u.create_time, u.update_by, u.update_time, u.remark, u.weixin_nickname, u.weixin_avatar, u.openid, u.unionid, u.session_key, u.real_name, u.portrait_url, u.birth_date, u.region, u.graduate_school, u.graduation_year, u.major, u.college, u.current_company, u.industry_field, u.position_title, u.personal_introduction, u.total_points, u.last_login_time, d.dept_name, d.leader from sys_user u
		left join sys_dept d on u.dept_id = d.dept_id
		where u.del_flag = '0'
		<if test="userId != null and userId != 0">
			AND u.user_id = #{userId}
		</if>
		<if test="userName != null and userName != ''">
			AND u.user_name like concat('%', #{userName}, '%')
		</if>
		<if test="status != null and status != ''">
			AND u.status = #{status}
		</if>
		<if test="phonenumber != null and phonenumber != ''">
			AND u.phonenumber like concat('%', #{phonenumber}, '%')
		</if>
		<if test="userType != null and userType != ''">
			AND u.user_type = #{userType}
		</if>
		<if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
			AND date_format(u.create_time,'%Y%m%d') &gt;= date_format(#{params.beginTime},'%Y%m%d')
		</if>
		<if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
			AND date_format(u.create_time,'%Y%m%d') &lt;= date_format(#{params.endTime},'%Y%m%d')
		</if>
		<if test="deptId != null and deptId != 0">
			AND (u.dept_id = #{deptId} OR u.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE find_in_set(#{deptId}, ancestors) ))
		</if>
		<if test="graduationYear != null and graduationYear != ''">
			AND u.graduation_year = #{graduationYear}
		</if>
		<if test="region != null and region != ''">
			AND u.region like concat('%', #{region}, '%')
		</if>
		<if test="industryField != null and industryField != ''">
			AND EXISTS (
				SELECT 1 FROM mini_industry_tree t3
				LEFT JOIN mini_industry_tree t2 ON t3.parent_id = t2.id
				LEFT JOIN mini_industry_tree t1 ON t2.parent_id = t1.id
				WHERE FIND_IN_SET(t3.id, u.industry_field) > 0
				AND t1.id = #{industryField}
				AND t1.node_level = 1
			)
		</if>
		<if test="realName != null and realName != ''">
			AND u.real_name like concat('%', #{realName}, '%')
		</if>
		<if test="weixinNickname != null and weixinNickname != ''">
			AND u.weixin_nickname like concat('%', #{weixinNickname}, '%')
		</if>
		<if test="params.searchValue != null and params.searchValue != ''">
			AND (
				u.user_name like concat('%', #{params.searchValue}, '%')
				OR u.real_name like concat('%', #{params.searchValue}, '%')
				OR u.weixin_nickname like concat('%', #{params.searchValue}, '%')
				OR u.phonenumber like concat('%', #{params.searchValue}, '%')
				OR u.region like concat('%', #{params.searchValue}, '%')
				OR u.graduate_school like concat('%', #{params.searchValue}, '%')
				OR u.current_company like concat('%', #{params.searchValue}, '%')
				OR u.position_title like concat('%', #{params.searchValue}, '%')
			)
		</if>
		<!-- 数据范围过滤 -->
		${params.dataScope}
	</select>

	<select id="selectUserListWithIndustry" parameterType="SysUser" resultMap="SysUserListResult">
		select u.user_id, u.dept_id, u.nick_name, u.user_name, u.user_type, u.email, u.avatar, u.phonenumber, u.sex, u.status, u.del_flag, u.login_ip, u.login_date, u.pwd_update_date, u.create_by, u.create_time, u.update_by, u.update_time, u.remark, u.weixin_nickname, u.weixin_avatar, u.openid, u.unionid, u.session_key, u.real_name, u.portrait_url, u.birth_date, u.region, u.graduate_school, u.graduation_year, u.major, u.college, u.current_company, u.industry_field, u.position_title, u.personal_introduction, u.total_points, u.last_login_time,
		(
			SELECT GROUP_CONCAT(DISTINCT t1.node_name SEPARATOR ', ')
			FROM mini_industry_tree t3
			LEFT JOIN mini_industry_tree t2 ON t3.parent_id = t2.id
			LEFT JOIN mini_industry_tree t1 ON t2.parent_id = t1.id
			WHERE FIND_IN_SET(t3.id, u.industry_field) > 0
			AND t1.node_level = 1
			AND t1.status = '0'
		) as industry_names
		from sys_user u
		where u.del_flag = '0'
		<if test="userId != null and userId != 0">
			AND u.user_id = #{userId}
		</if>
		<if test="userName != null and userName != ''">
			AND u.user_name like concat('%', #{userName}, '%')
		</if>
		<if test="status != null and status != ''">
			AND u.status = #{status}
		</if>
		<if test="phonenumber != null and phonenumber != ''">
			AND u.phonenumber like concat('%', #{phonenumber}, '%')
		</if>
		<if test="userType != null and userType != ''">
			AND u.user_type = #{userType}
		</if>
		<if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
			AND date_format(u.create_time,'%Y%m%d') &gt;= date_format(#{params.beginTime},'%Y%m%d')
		</if>
		<if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
			AND date_format(u.create_time,'%Y%m%d') &lt;= date_format(#{params.endTime},'%Y%m%d')
		</if>
		<if test="graduationYear != null and graduationYear != ''">
			AND u.graduation_year = #{graduationYear}
		</if>
		<if test="region != null and region != ''">
			AND u.region like concat('%', #{region}, '%')
		</if>
		<if test="industryField != null and industryField != ''">
			AND EXISTS (
				SELECT 1 FROM mini_industry_tree t3
				LEFT JOIN mini_industry_tree t2 ON t3.parent_id = t2.id
				LEFT JOIN mini_industry_tree t1 ON t2.parent_id = t1.id
				WHERE FIND_IN_SET(t3.id, u.industry_field) > 0
				AND t1.id = #{industryField}
				AND t1.node_level = 1
			)
		</if>
		<if test="realName != null and realName != ''">
			AND u.real_name like concat('%', #{realName}, '%')
		</if>
		<if test="weixinNickname != null and weixinNickname != ''">
			AND u.weixin_nickname like concat('%', #{weixinNickname}, '%')
		</if>
		<if test="searchValue != null and searchValue != ''">
			AND (
				u.user_name like concat('%', #{searchValue}, '%')
				OR u.real_name like concat('%', #{searchValue}, '%')
				OR u.weixin_nickname like concat('%', #{searchValue}, '%')
				OR u.phonenumber like concat('%', #{searchValue}, '%')
				OR u.region like concat('%', #{searchValue}, '%')
				OR u.graduate_school like concat('%', #{searchValue}, '%')
				OR u.current_company like concat('%', #{searchValue}, '%')
				OR u.position_title like concat('%', #{searchValue}, '%')
			)
		</if>
		<!-- 数据范围过滤 -->
		${params.dataScope}
	</select>
	
	<select id="selectAllocatedList" parameterType="SysUser" resultMap="SysUserResult">
	    select distinct u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.phonenumber, u.status, u.create_time
	    from sys_user u
			 left join sys_dept d on u.dept_id = d.dept_id
			 left join sys_user_role ur on u.user_id = ur.user_id
			 left join sys_role r on r.role_id = ur.role_id
	    where u.del_flag = '0' and r.role_id = #{roleId}
	    <if test="userName != null and userName != ''">
			AND u.user_name like concat('%', #{userName}, '%')
		</if>
		<if test="phonenumber != null and phonenumber != ''">
			AND u.phonenumber like concat('%', #{phonenumber}, '%')
		</if>
		<!-- 数据范围过滤 -->
		${params.dataScope}
	</select>
	
	<select id="selectUnallocatedList" parameterType="SysUser" resultMap="SysUserResult">
	    select distinct u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.phonenumber, u.status, u.create_time
	    from sys_user u
			 left join sys_dept d on u.dept_id = d.dept_id
			 left join sys_user_role ur on u.user_id = ur.user_id
			 left join sys_role r on r.role_id = ur.role_id
	    where u.del_flag = '0' and (r.role_id != #{roleId} or r.role_id IS NULL)
	    and u.user_id not in (select u.user_id from sys_user u inner join sys_user_role ur on u.user_id = ur.user_id and ur.role_id = #{roleId})
	    <if test="userName != null and userName != ''">
			AND u.user_name like concat('%', #{userName}, '%')
		</if>
		<if test="phonenumber != null and phonenumber != ''">
			AND u.phonenumber like concat('%', #{phonenumber}, '%')
		</if>
		<!-- 数据范围过滤 -->
		${params.dataScope}
	</select>
	
	<select id="selectUserByUserName" parameterType="String" resultMap="SysUserResult">
	    <include refid="selectUserVo"/>
		where u.user_name = #{userName} and u.del_flag = '0'
	</select>
	
	<select id="selectUserById" parameterType="Long" resultMap="SysUserResult">
		<include refid="selectUserVo"/>
		where u.user_id = #{userId}
	</select>

	<select id="selectUserByOpenid" parameterType="String" resultMap="SysUserResult">
	    <include refid="selectUserVo"/>
		where u.openid = #{openid} and u.del_flag = '0' and u.status = '0'
	</select>
	
	<select id="checkUserNameUnique" parameterType="String" resultMap="SysUserResult">
		select user_id, user_name from sys_user where user_name = #{userName} and del_flag = '0' limit 1
	</select>
	
	<select id="checkPhoneUnique" parameterType="String" resultMap="SysUserResult">
		select user_id, phonenumber from sys_user where phonenumber = #{phonenumber} and del_flag = '0' limit 1
	</select>
	
	<select id="checkEmailUnique" parameterType="String" resultMap="SysUserResult">
		select user_id, email from sys_user where email = #{email} and del_flag = '0' limit 1
	</select>
	
	<insert id="insertUser" parameterType="SysUser" useGeneratedKeys="true" keyProperty="userId">
 		insert into sys_user(
 			<if test="userId != null and userId != 0">user_id,</if>
 			<if test="deptId != null and deptId != 0">dept_id,</if>
 			<if test="userName != null and userName != ''">user_name,</if>
 			<if test="nickName != null and nickName != ''">nick_name,</if>
 			<if test="userType != null and userType != ''">user_type,</if>
 			<if test="email != null and email != ''">email,</if>
 			<if test="avatar != null and avatar != ''">avatar,</if>
 			<if test="phonenumber != null and phonenumber != ''">phonenumber,</if>
 			<if test="sex != null and sex != ''">sex,</if>
 			<if test="password != null and password != ''">password,</if>
 			<if test="status != null and status != ''">status,</if>
 			<if test="pwdUpdateDate != null">pwd_update_date,</if>
 			<if test="createBy != null and createBy != ''">create_by,</if>
 			<if test="remark != null and remark != ''">remark,</if>
 			<if test="realName != null and realName != ''">real_name,</if>
 			<if test="weixinNickname != null and weixinNickname != ''">weixin_nickname,</if>
 			<if test="weixinAvatar != null and weixinAvatar != ''">weixin_avatar,</if>
 			<if test="openid != null and openid != ''">openid,</if>
 			<if test="unionid != null and unionid != ''">unionid,</if>
 			<if test="sessionKey != null and sessionKey != ''">session_key,</if>
 			<if test="portraitUrl != null and portraitUrl != ''">portrait_url,</if>
 			<if test="birthDate != null">birth_date,</if>
 			<if test="region != null and region != ''">region,</if>
 			<if test="graduateSchool != null and graduateSchool != ''">graduate_school,</if>
 			<if test="graduationYear != null and graduationYear != ''">graduation_year,</if>
 			<if test="major != null and major != ''">major,</if>
 			<if test="college != null and college != ''">college,</if>
 			<if test="currentCompany != null and currentCompany != ''">current_company,</if>
 			<if test="industryField != null and industryField != ''">industry_field,</if>
 			<if test="positionTitle != null and positionTitle != ''">position_title,</if>
 			<if test="personalIntroduction != null and personalIntroduction != ''">personal_introduction,</if>
 			<if test="totalPoints != null">total_points,</if>
 			<if test="lastLoginTime != null">last_login_time,</if>
 			create_time
 		)values(
 			<if test="userId != null and userId != ''">#{userId},</if>
 			<if test="deptId != null and deptId != ''">#{deptId},</if>
 			<if test="userName != null and userName != ''">#{userName},</if>
 			<if test="nickName != null and nickName != ''">#{nickName},</if>
 			<if test="userType != null and userType != ''">#{userType},</if>
 			<if test="email != null and email != ''">#{email},</if>
 			<if test="avatar != null and avatar != ''">#{avatar},</if>
 			<if test="phonenumber != null and phonenumber != ''">#{phonenumber},</if>
 			<if test="sex != null and sex != ''">#{sex},</if>
 			<if test="password != null and password != ''">#{password},</if>
 			<if test="status != null and status != ''">#{status},</if>
 			<if test="pwdUpdateDate != null">#{pwdUpdateDate},</if>
 			<if test="createBy != null and createBy != ''">#{createBy},</if>
 			<if test="remark != null and remark != ''">#{remark},</if>
 			<if test="realName != null and realName != ''">#{realName},</if>
 			<if test="weixinNickname != null and weixinNickname != ''">#{weixinNickname},</if>
 			<if test="weixinAvatar != null and weixinAvatar != ''">#{weixinAvatar},</if>
 			<if test="openid != null and openid != ''">#{openid},</if>
 			<if test="unionid != null and unionid != ''">#{unionid},</if>
 			<if test="sessionKey != null and sessionKey != ''">#{sessionKey},</if>
 			<if test="portraitUrl != null and portraitUrl != ''">#{portraitUrl},</if>
 			<if test="birthDate != null">#{birthDate},</if>
 			<if test="region != null and region != ''">#{region},</if>
 			<if test="graduateSchool != null and graduateSchool != ''">#{graduateSchool},</if>
 			<if test="graduationYear != null and graduationYear != ''">#{graduationYear},</if>
 			<if test="major != null and major != ''">#{major},</if>
 			<if test="college != null and college != ''">#{college},</if>
 			<if test="currentCompany != null and currentCompany != ''">#{currentCompany},</if>
 			<if test="industryField != null and industryField != ''">#{industryField},</if>
 			<if test="positionTitle != null and positionTitle != ''">#{positionTitle},</if>
 			<if test="personalIntroduction != null and personalIntroduction != ''">#{personalIntroduction},</if>
 			<if test="totalPoints != null">#{totalPoints},</if>
 			<if test="lastLoginTime != null">#{lastLoginTime},</if>
 			sysdate()
 		)
	</insert>
	
	<update id="updateUser" parameterType="SysUser">
 		update sys_user
 		<set>
 			<if test="deptId != null and deptId != 0">dept_id = #{deptId},</if>
 			<if test="nickName != null and nickName != ''">nick_name = #{nickName},</if>
 			<if test="userType != null and userType != ''">user_type = #{userType},</if>
 			<if test="email != null ">email = #{email},</if>
 			<if test="phonenumber != null ">phonenumber = #{phonenumber},</if>
 			<if test="sex != null and sex != ''">sex = #{sex},</if>
 			<if test="avatar != null and avatar != ''">avatar = #{avatar},</if>
 			<if test="password != null and password != ''">password = #{password},</if>
 			<if test="status != null and status != ''">status = #{status},</if>
 			<if test="loginIp != null and loginIp != ''">login_ip = #{loginIp},</if>
 			<if test="loginDate != null">login_date = #{loginDate},</if>
 			<if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
 			<if test="remark != null">remark = #{remark},</if>
 			<if test="realName != null">real_name = #{realName},</if>
 			<if test="weixinNickname != null">weixin_nickname = #{weixinNickname},</if>
 			<if test="weixinAvatar != null">weixin_avatar = #{weixinAvatar},</if>
 			<if test="openid != null">openid = #{openid},</if>
 			<if test="unionid != null">unionid = #{unionid},</if>
 			<if test="sessionKey != null">session_key = #{sessionKey},</if>
 			<if test="portraitUrl != null">portrait_url = #{portraitUrl},</if>
 			<if test="birthDate != null">birth_date = #{birthDate},</if>
 			<if test="region != null">region = #{region},</if>
 			<if test="graduateSchool != null">graduate_school = #{graduateSchool},</if>
 			<if test="graduationYear != null">graduation_year = #{graduationYear},</if>
 			<if test="major != null">major = #{major},</if>
 			<if test="college != null">college = #{college},</if>
 			<if test="currentCompany != null">current_company = #{currentCompany},</if>
 			<if test="industryField != null">industry_field = #{industryField},</if>
 			<if test="positionTitle != null">position_title = #{positionTitle},</if>
 			<if test="personalIntroduction != null">personal_introduction = #{personalIntroduction},</if>
 			<if test="totalPoints != null">total_points = #{totalPoints},</if>
 			<if test="lastLoginTime != null">last_login_time = #{lastLoginTime},</if>
 			<if test="profileCompletionRate != null">profile_completion_rate = #{profileCompletionRate},</if>
 			update_time = sysdate()
 		</set>
 		where user_id = #{userId}
	</update>
	
	<update id="updateUserStatus" parameterType="SysUser">
 		update sys_user set status = #{status} where user_id = #{userId}
	</update>
	
	<update id="updateUserAvatar" parameterType="SysUser">
 		update sys_user set avatar = #{avatar} where user_id = #{userId}
	</update>
	
	<update id="resetUserPwd" parameterType="SysUser">
 		update sys_user set pwd_update_date = sysdate(), password = #{password} where user_id = #{userId}
	</update>

	<update id="updateUserFollowCounts">
		update sys_user
		set following_count = #{followingCount},
		    follower_count = #{followersCount},
		    update_time = sysdate()
		where user_id = #{userId}
	</update>
	
	<delete id="deleteUserById" parameterType="Long">
 		update sys_user set del_flag = '2' where user_id = #{userId}
 	</delete>
 	
 	<delete id="deleteUserByIds" parameterType="Long">
 		update sys_user set del_flag = '2' where user_id in
 		<foreach collection="array" item="userId" open="(" separator="," close=")">
 			#{userId}
        </foreach> 
 	</delete>
	
</mapper> 