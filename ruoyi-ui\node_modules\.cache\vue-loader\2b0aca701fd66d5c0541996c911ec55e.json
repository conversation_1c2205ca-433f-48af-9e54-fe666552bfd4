{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\xiqing\\activity-config\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\xiqing\\activity-config\\index.vue", "mtime": 1753758422831}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBnZXRBY3Rpdml0eUNvbmZpZywgdXBkYXRlQWN0aXZpdHlDb25maWcgfSBmcm9tICJAL2FwaS9taW5pYXBwL3hpcWluZy9hY3Rpdml0eS1jb25maWciOw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJYaXFpbmdBY3Rpdml0eUNvbmZpZyIsDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIC8vIOmBrue9qeWxgg0KICAgICAgbG9hZGluZzogdHJ1ZSwNCiAgICAgIC8vIOaYr+WQpuaYvuekuuihqOWNlemFjee9ruW8ueWHuuWxgg0KICAgICAgZm9ybUNvbmZpZ09wZW46IGZhbHNlLA0KICAgICAgLy8g5piv5ZCm5pi+56S66KGo5Y2V6aKE6KeI5by55Ye65bGCDQogICAgICBwcmV2aWV3RGlhbG9nVmlzaWJsZTogZmFsc2UsDQogICAgICAvLyDooajljZXlrZfmrrXphY3nva4NCiAgICAgIGZvcm1GaWVsZHM6IFtdLA0KICAgICAgLy8g5rS75YqoSUTvvIjlm7rlrprkuLox77yM5Zug5Li65Y+q5pyJ5LiA5Liq6Lev5ryU5rS75Yqo6YWN572u77yJDQogICAgICBhY3Rpdml0eUlkOiAxDQogICAgfTsNCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICB0aGlzLmxvYWRGb3JtQ29uZmlnKCk7DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICAvKiog5Yqg6L296KGo5Y2V6YWN572uICovDQogICAgbG9hZEZvcm1Db25maWcoKSB7DQogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOw0KICAgICAgZ2V0QWN0aXZpdHlDb25maWcodGhpcy5hY3Rpdml0eUlkKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEgJiYgcmVzcG9uc2UuZGF0YS5mb3JtQ29uZmlnKSB7DQogICAgICAgICAgdHJ5IHsNCiAgICAgICAgICAgIHRoaXMuZm9ybUZpZWxkcyA9IEpTT04ucGFyc2UocmVzcG9uc2UuZGF0YS5mb3JtQ29uZmlnKTsNCiAgICAgICAgICB9IGNhdGNoIChlKSB7DQogICAgICAgICAgICB0aGlzLmZvcm1GaWVsZHMgPSBbXTsNCiAgICAgICAgICB9DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy5mb3JtRmllbGRzID0gW107DQogICAgICAgIH0NCiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICB9KS5jYXRjaCgoKSA9PiB7DQogICAgICAgIHRoaXMuZm9ybUZpZWxkcyA9IFtdOw0KICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOihqOWNlemFjee9ruaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZUZvcm1Db25maWcoKSB7DQogICAgICB0aGlzLmZvcm1Db25maWdPcGVuID0gdHJ1ZTsNCiAgICB9LA0KICAgIC8qKiDmt7vliqDooajljZXlrZfmrrUgKi8NCiAgICBhZGRGb3JtRmllbGQoKSB7DQogICAgICB0aGlzLmZvcm1GaWVsZHMucHVzaCh7DQogICAgICAgIG5hbWU6ICcnLA0KICAgICAgICBsYWJlbDogJycsDQogICAgICAgIHR5cGU6ICdpbnB1dCcsDQogICAgICAgIHJlcXVpcmVkOiBmYWxzZSwNCiAgICAgICAgb3B0aW9uczogJycNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOWIoOmZpOihqOWNleWtl+autSAqLw0KICAgIHJlbW92ZUZvcm1GaWVsZChpbmRleCkgew0KICAgICAgdGhpcy5mb3JtRmllbGRzLnNwbGljZShpbmRleCwgMSk7DQogICAgfSwNCiAgICAvKiog56e75Yqo5a2X5q615L2N572uICovDQogICAgbW92ZUZpZWxkKGluZGV4LCBkaXJlY3Rpb24pIHsNCiAgICAgIGNvbnN0IG5ld0luZGV4ID0gaW5kZXggKyBkaXJlY3Rpb247DQogICAgICBpZiAobmV3SW5kZXggPj0gMCAmJiBuZXdJbmRleCA8IHRoaXMuZm9ybUZpZWxkcy5sZW5ndGgpIHsNCiAgICAgICAgY29uc3QgdGVtcCA9IHRoaXMuZm9ybUZpZWxkc1tpbmRleF07DQogICAgICAgIHRoaXMuJHNldCh0aGlzLmZvcm1GaWVsZHMsIGluZGV4LCB0aGlzLmZvcm1GaWVsZHNbbmV3SW5kZXhdKTsNCiAgICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybUZpZWxkcywgbmV3SW5kZXgsIHRlbXApOw0KICAgICAgfQ0KICAgIH0sDQogICAgLyoqIOabtOaWsOWtl+auteWQjeensCAqLw0KICAgIHVwZGF0ZUZpZWxkTmFtZShmaWVsZCwgbGFiZWwpIHsNCiAgICAgIGlmICghZmllbGQubmFtZSB8fCBmaWVsZC5uYW1lID09PSAnJykgew0KICAgICAgICBmaWVsZC5uYW1lID0gdGhpcy5nZW5lcmF0ZUZpZWxkTmFtZShsYWJlbCk7DQogICAgICB9DQogICAgfSwNCiAgICAvKiog55Sf5oiQ5a2X5q615ZCN56ewICovDQogICAgZ2VuZXJhdGVGaWVsZE5hbWUobGFiZWwpIHsNCiAgICAgIGNvbnN0IHBpbnlpbiA9IHsNCiAgICAgICAgJ+Wnk+WQjSc6ICduYW1lJywNCiAgICAgICAgJ+iBlOezu+eUteivnSc6ICdwaG9uZScsDQogICAgICAgICfnlLXor50nOiAncGhvbmUnLA0KICAgICAgICAn6YKu566xJzogJ2VtYWlsJywNCiAgICAgICAgJ+mCrueuseWcsOWdgCc6ICdlbWFpbCcsDQogICAgICAgICflhazlj7gnOiAnY29tcGFueScsDQogICAgICAgICfpobnnm67lkI3np7AnOiAncHJvamVjdF9uYW1lJywNCiAgICAgICAgJ+mhueebruaPj+i/sCc6ICdwcm9qZWN0X2Rlc2NyaXB0aW9uJywNCiAgICAgICAgJ+WboumYn+inhOaooSc6ICd0ZWFtX3NpemUnDQogICAgICB9Ow0KICAgICAgcmV0dXJuIHBpbnlpbltsYWJlbF0gfHwgbGFiZWwudG9Mb3dlckNhc2UoKS5yZXBsYWNlKC9ccysvZywgJ18nKTsNCiAgICB9LA0KICAgIC8qKiDojrflj5blrZfmrrXlm77moIcgKi8NCiAgICBnZXRGaWVsZEljb24odHlwZSkgew0KICAgICAgY29uc3QgaWNvbnMgPSB7DQogICAgICAgIGlucHV0OiAnZWwtaWNvbi1lZGl0JywNCiAgICAgICAgdGV4dGFyZWE6ICdlbC1pY29uLWRvY3VtZW50JywNCiAgICAgICAgbnVtYmVyOiAnZWwtaWNvbi1zLWRhdGEnLA0KICAgICAgICBlbWFpbDogJ2VsLWljb24tbWVzc2FnZScsDQogICAgICAgIHRlbDogJ2VsLWljb24tcGhvbmUnLA0KICAgICAgICByYWRpbzogJ2VsLWljb24tc3VjY2VzcycsDQogICAgICAgIGNoZWNrYm94OiAnZWwtaWNvbi1jaGVjaycsDQogICAgICAgIHNlbGVjdDogJ2VsLWljb24tYXJyb3ctZG93bicsDQogICAgICAgIHJhZGlvX290aGVyOiAnZWwtaWNvbi1jaXJjbGUtcGx1cycsDQogICAgICAgIGNoZWNrYm94X290aGVyOiAnZWwtaWNvbi1zcXVhcmUtcGx1cycsDQogICAgICAgIHNlbGVjdF9vdGhlcjogJ2VsLWljb24tcGx1cycsDQogICAgICAgIGRhdGU6ICdlbC1pY29uLWRhdGUnLA0KICAgICAgICBmaWxlOiAnZWwtaWNvbi11cGxvYWQnDQogICAgICB9Ow0KICAgICAgcmV0dXJuIGljb25zW3R5cGVdIHx8ICdlbC1pY29uLWVkaXQnOw0KICAgIH0sDQogICAgLyoqIOWkhOeQhuaooeadv+WRveS7pCAqLw0KICAgIGhhbmRsZVRlbXBsYXRlQ29tbWFuZChjb21tYW5kKSB7DQogICAgICBpZiAoY29tbWFuZCA9PT0gJ2NsZWFyJykgew0KICAgICAgICB0aGlzLiRjb25maXJtKCfnoa7lrpropoHmuIXnqbrmiYDmnInlrZfmrrXlkJfvvJ8nLCAn5o+Q56S6Jywgew0KICAgICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywNCiAgICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywNCiAgICAgICAgICB0eXBlOiAnd2FybmluZycNCiAgICAgICAgfSkudGhlbigoKSA9PiB7DQogICAgICAgICAgdGhpcy5mb3JtRmllbGRzID0gW107DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCflt7LmuIXnqbrmiYDmnInlrZfmrrUnKTsNCiAgICAgICAgfSk7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCg0KICAgICAgY29uc3QgdGVtcGxhdGVzID0gew0KICAgICAgICBiYXNpYzogWw0KICAgICAgICAgIHsgbGFiZWw6ICflp5PlkI0nLCBuYW1lOiAnbmFtZScsIHR5cGU6ICdpbnB1dCcsIHJlcXVpcmVkOiB0cnVlLCBvcHRpb25zOiAnJyB9LA0KICAgICAgICAgIHsgbGFiZWw6ICfogZTns7vnlLXor50nLCBuYW1lOiAncGhvbmUnLCB0eXBlOiAndGVsJywgcmVxdWlyZWQ6IHRydWUsIG9wdGlvbnM6ICcnIH0sDQogICAgICAgICAgeyBsYWJlbDogJ+mCrueuseWcsOWdgCcsIG5hbWU6ICdlbWFpbCcsIHR5cGU6ICdlbWFpbCcsIHJlcXVpcmVkOiBmYWxzZSwgb3B0aW9uczogJycgfQ0KICAgICAgICBdLA0KICAgICAgICByb2Fkc2hvdzogWw0KICAgICAgICAgIHsgbGFiZWw6ICflp5PlkI0nLCBuYW1lOiAnbmFtZScsIHR5cGU6ICdpbnB1dCcsIHJlcXVpcmVkOiB0cnVlLCBvcHRpb25zOiAnJyB9LA0KICAgICAgICAgIHsgbGFiZWw6ICfogZTns7vnlLXor50nLCBuYW1lOiAncGhvbmUnLCB0eXBlOiAndGVsJywgcmVxdWlyZWQ6IHRydWUsIG9wdGlvbnM6ICcnIH0sDQogICAgICAgICAgeyBsYWJlbDogJ+mCrueuseWcsOWdgCcsIG5hbWU6ICdlbWFpbCcsIHR5cGU6ICdlbWFpbCcsIHJlcXVpcmVkOiB0cnVlLCBvcHRpb25zOiAnJyB9LA0KICAgICAgICAgIHsgbGFiZWw6ICflhazlj7gv5Zui6ZifJywgbmFtZTogJ2NvbXBhbnknLCB0eXBlOiAnaW5wdXQnLCByZXF1aXJlZDogdHJ1ZSwgb3B0aW9uczogJycgfSwNCiAgICAgICAgICB7IGxhYmVsOiAn6aG555uu5ZCN56ewJywgbmFtZTogJ3Byb2plY3RfbmFtZScsIHR5cGU6ICdpbnB1dCcsIHJlcXVpcmVkOiB0cnVlLCBvcHRpb25zOiAnJyB9LA0KICAgICAgICAgIHsgbGFiZWw6ICfpobnnm67mnaXmupAnLCBuYW1lOiAncHJvamVjdF9zb3VyY2UnLCB0eXBlOiAncmFkaW9fb3RoZXInLCByZXF1aXJlZDogdHJ1ZSwgb3B0aW9uczogJ+ekvuS8mizpq5jmoKEs56eR56CU6Zmi5omALOS8geS4muWGhemDqCcgfSwNCiAgICAgICAgICB7IGxhYmVsOiAn6aG555uu6Zi25q61JywgbmFtZTogJ3Byb2plY3Rfc3RhZ2UnLCB0eXBlOiAncmFkaW8nLCByZXF1aXJlZDogdHJ1ZSwgb3B0aW9uczogJ+amguW/temYtuautSzlvIDlj5HpmLbmrrUs5rWL6K+V6Zi25q61LOS4iue6v+mYtuautScgfSwNCiAgICAgICAgICB7IGxhYmVsOiAn5Zui6Zif6KeE5qihJywgbmFtZTogJ3RlYW1fc2l6ZScsIHR5cGU6ICdzZWxlY3QnLCByZXF1aXJlZDogdHJ1ZSwgb3B0aW9uczogJzHkurosMi0z5Lq6LDQtNeS6uiw2LTEw5Lq6LDEw5Lq65Lul5LiKJyB9LA0KICAgICAgICAgIHsgbGFiZWw6ICfono3otYTpnIDmsYInLCBuYW1lOiAnZnVuZGluZ19uZWVkcycsIHR5cGU6ICdpbnB1dCcsIHJlcXVpcmVkOiBmYWxzZSwgb3B0aW9uczogJycgfSwNCiAgICAgICAgICB7IGxhYmVsOiAn5ZWG5Lia6K6h5YiS5LmmJywgbmFtZTogJ2J1c2luZXNzX3BsYW4nLCB0eXBlOiAnZmlsZScsIHJlcXVpcmVkOiB0cnVlLCBvcHRpb25zOiAnJyB9LA0KICAgICAgICAgIHsgbGFiZWw6ICfpobnnm67mj4/ov7AnLCBuYW1lOiAncHJvamVjdF9kZXNjcmlwdGlvbicsIHR5cGU6ICd0ZXh0YXJlYScsIHJlcXVpcmVkOiB0cnVlLCBvcHRpb25zOiAnJyB9DQogICAgICAgIF0NCiAgICAgIH07DQoNCiAgICAgIGlmICh0ZW1wbGF0ZXNbY29tbWFuZF0pIHsNCiAgICAgICAgdGhpcy5mb3JtRmllbGRzID0gdGVtcGxhdGVzW2NvbW1hbmRdOw0KICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+aooeadv+W6lOeUqOaIkOWKnycpOw0KICAgICAgfQ0KICAgIH0sDQogICAgLyoqIOmihOiniOihqOWNlSAqLw0KICAgIHByZXZpZXdGb3JtKCkgew0KICAgICAgaWYgKHRoaXMuZm9ybUZpZWxkcy5sZW5ndGggPT09IDApIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor7flhYjmt7vliqDooajljZXlrZfmrrUnKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KICAgICAgdGhpcy5wcmV2aWV3RGlhbG9nVmlzaWJsZSA9IHRydWU7DQogICAgfSwNCiAgICAvKiog5L+d5a2Y6KGo5Y2V6YWN572uICovDQogICAgc2F2ZUZvcm1Db25maWcoKSB7DQogICAgICBpZiAodGhpcy5mb3JtRmllbGRzLmxlbmd0aCA9PT0gMCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ivt+iHs+Wwkea3u+WKoOS4gOS4quihqOWNleWtl+autScpOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIC8vIOmqjOivgeWtl+autemFjee9rg0KICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCB0aGlzLmZvcm1GaWVsZHMubGVuZ3RoOyBpKyspIHsNCiAgICAgICAgY29uc3QgZmllbGQgPSB0aGlzLmZvcm1GaWVsZHNbaV07DQogICAgICAgIGlmICghZmllbGQubGFiZWwpIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKGDnrKwke2kgKyAxfeS4quWtl+auteeahOagh+etvuS4jeiDveS4uuepumApOw0KICAgICAgICAgIHJldHVybjsNCiAgICAgICAgfQ0KICAgICAgICBpZiAoWydyYWRpbycsICdjaGVja2JveCcsICdzZWxlY3QnLCAncmFkaW9fb3RoZXInLCAnY2hlY2tib3hfb3RoZXInLCAnc2VsZWN0X290aGVyJ10uaW5jbHVkZXMoZmllbGQudHlwZSkgJiYgIWZpZWxkLm9wdGlvbnMpIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKGDlrZfmrrUiJHtmaWVsZC5sYWJlbH0i6ZyA6KaB6YWN572u6YCJ6aG5YCk7DQogICAgICAgICAgcmV0dXJuOw0KICAgICAgICB9DQogICAgICB9DQoNCiAgICAgIGNvbnN0IGNvbmZpZ0RhdGEgPSBKU09OLnN0cmluZ2lmeSh0aGlzLmZvcm1GaWVsZHMpOw0KICAgICAgY29uc3QgdXBkYXRlRGF0YSA9IHsNCiAgICAgICAgYWN0aXZpdHlJZDogdGhpcy5hY3Rpdml0eUlkLA0KICAgICAgICBmb3JtQ29uZmlnOiBjb25maWdEYXRhDQogICAgICB9Ow0KDQogICAgICB1cGRhdGVBY3Rpdml0eUNvbmZpZyh1cGRhdGVEYXRhKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi6KGo5Y2V6YWN572u5L+d5a2Y5oiQ5YqfIik7DQogICAgICAgIHRoaXMuZm9ybUNvbmZpZ09wZW4gPSBmYWxzZTsNCiAgICAgICAgdGhpcy5sb2FkRm9ybUNvbmZpZygpOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog6I635Y+W5a2X5q6157G75Z6L5ZCN56ewICovDQogICAgZ2V0RmllbGRUeXBlTmFtZSh0eXBlKSB7DQogICAgICBjb25zdCB0eXBlTmFtZXMgPSB7DQogICAgICAgIGlucHV0OiAn5paH5pys6L6T5YWlJywNCiAgICAgICAgdGV4dGFyZWE6ICflpJrooYzmlofmnKwnLA0KICAgICAgICBudW1iZXI6ICfmlbDlrZfovpPlhaUnLA0KICAgICAgICBlbWFpbDogJ+mCrueusScsDQogICAgICAgIHRlbDogJ+eUteivnScsDQogICAgICAgIHJhZGlvOiAn5Y2V6YCJJywNCiAgICAgICAgY2hlY2tib3g6ICflpJrpgIknLA0KICAgICAgICBzZWxlY3Q6ICfkuIvmi4npgInmi6knLA0KICAgICAgICByYWRpb19vdGhlcjogJ+WNlemAiSvlhbbku5YnLA0KICAgICAgICBjaGVja2JveF9vdGhlcjogJ+WkmumAiSvlhbbku5YnLA0KICAgICAgICBzZWxlY3Rfb3RoZXI6ICfkuIvmi4kr5YW25LuWJywNCiAgICAgICAgZGF0ZTogJ+aXpeacnycsDQogICAgICAgIGZpbGU6ICfmlofku7bkuIrkvKAnDQogICAgICB9Ow0KICAgICAgcmV0dXJuIHR5cGVOYW1lc1t0eXBlXSB8fCAn5pyq55+l57G75Z6LJzsNCiAgICB9DQogIH0NCn07DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyUA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/miniapp/xiqing/activity-config", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <div class=\"form-config-container\">\r\n      <div class=\"config-header\">\r\n        <h3>路演活动报名表单配置</h3>\r\n        <el-button\r\n          type=\"primary\"\r\n          icon=\"el-icon-setting\"\r\n          @click=\"handleFormConfig\"\r\n          v-hasPermi=\"['miniapp:xiqing:activity:edit']\"\r\n        >配置表单</el-button>\r\n      </div>\r\n\r\n      <div class=\"config-content\" v-loading=\"loading\">\r\n        <div v-if=\"formFields.length > 0\" class=\"form-preview\">\r\n          <h4>当前表单字段预览：</h4>\r\n          <div class=\"field-list\">\r\n            <div v-for=\"(field, index) in formFields\" :key=\"index\" class=\"field-item\">\r\n              <div class=\"field-info\">\r\n                <i :class=\"getFieldIcon(field.type)\" class=\"field-icon\"></i>\r\n                <span class=\"field-label\">{{ field.label }}</span>\r\n                <el-tag v-if=\"field.required\" size=\"mini\" type=\"danger\">必填</el-tag>\r\n                <el-tag v-else size=\"mini\" type=\"info\">选填</el-tag>\r\n                <span class=\"field-type\">{{ getFieldTypeName(field.type) }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div v-else class=\"empty-form\">\r\n          <i class=\"el-icon-document-add\"></i>\r\n          <p>暂未配置表单字段，点击\"配置表单\"开始设置</p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n\r\n\r\n    <!-- 表单配置对话框 -->\r\n    <el-dialog title=\"报名表单配置\" :visible.sync=\"formConfigOpen\" width=\"1000px\" append-to-body>\r\n      <div class=\"form-fields-config\">\r\n        <!-- 工具栏 -->\r\n        <div class=\"form-fields-toolbar\">\r\n          <div class=\"toolbar-left\">\r\n            <el-button type=\"primary\" size=\"small\" @click=\"addFormField\" icon=\"el-icon-plus\">\r\n              添加字段\r\n            </el-button>\r\n            <el-dropdown @command=\"handleTemplateCommand\" size=\"small\">\r\n              <el-button size=\"small\">\r\n                预设模板<i class=\"el-icon-arrow-down el-icon--right\"></i>\r\n              </el-button>\r\n              <el-dropdown-menu slot=\"dropdown\">\r\n                <el-dropdown-item command=\"basic\">基础信息模板</el-dropdown-item>\r\n                <el-dropdown-item command=\"roadshow\">路演报名模板</el-dropdown-item>\r\n                <el-dropdown-item command=\"clear\">清空所有字段</el-dropdown-item>\r\n              </el-dropdown-menu>\r\n            </el-dropdown>\r\n          </div>\r\n          <div class=\"toolbar-right\">\r\n            <el-button size=\"small\" @click=\"previewForm\" icon=\"el-icon-view\">\r\n              预览表单\r\n            </el-button>\r\n            <el-button type=\"success\" size=\"small\" @click=\"saveFormConfig\" icon=\"el-icon-check\">\r\n              保存配置\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 字段配置区域 -->\r\n        <div class=\"form-fields-list\" v-if=\"formFields.length > 0\">\r\n          <div\r\n            v-for=\"(field, index) in formFields\"\r\n            :key=\"index\"\r\n            class=\"form-field-item\"\r\n          >\r\n            <div class=\"field-header\">\r\n              <div class=\"field-info\">\r\n                <i :class=\"getFieldIcon(field.type)\" class=\"field-icon\"></i>\r\n                <span class=\"field-label\">{{ field.label || '未命名字段' }}</span>\r\n                <el-tag v-if=\"field.required\" size=\"mini\" type=\"danger\">必填</el-tag>\r\n                <el-tag v-else size=\"mini\" type=\"info\">选填</el-tag>\r\n              </div>\r\n              <div class=\"field-actions\">\r\n                <el-button\r\n                  type=\"text\"\r\n                  size=\"mini\"\r\n                  @click=\"moveField(index, -1)\"\r\n                  :disabled=\"index === 0\"\r\n                  icon=\"el-icon-arrow-up\"\r\n                ></el-button>\r\n                <el-button\r\n                  type=\"text\"\r\n                  size=\"mini\"\r\n                  @click=\"moveField(index, 1)\"\r\n                  :disabled=\"index === formFields.length - 1\"\r\n                  icon=\"el-icon-arrow-down\"\r\n                ></el-button>\r\n                <el-button\r\n                  type=\"text\"\r\n                  size=\"mini\"\r\n                  @click=\"removeFormField(index)\"\r\n                  icon=\"el-icon-delete\"\r\n                  class=\"danger-btn\"\r\n                ></el-button>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"field-content\">\r\n              <el-row :gutter=\"10\">\r\n                <el-col :span=\"12\">\r\n                  <div class=\"field-item\">\r\n                    <label>字段标签</label>\r\n                    <el-input\r\n                      v-model=\"field.label\"\r\n                      placeholder=\"显示给用户的标签，如：姓名、电话等\"\r\n                      size=\"small\"\r\n                      @input=\"updateFieldName(field, $event)\"\r\n                    />\r\n                  </div>\r\n                </el-col>\r\n                <el-col :span=\"12\">\r\n                  <div class=\"field-item\">\r\n                    <label>字段类型</label>\r\n                    <el-select v-model=\"field.type\" placeholder=\"选择类型\" size=\"small\" style=\"width: 100%\">\r\n                      <el-option label=\"📝 文本输入\" value=\"input\" />\r\n                      <el-option label=\"📄 多行文本\" value=\"textarea\" />\r\n                      <el-option label=\"🔢 数字输入\" value=\"number\" />\r\n                      <el-option label=\"📧 邮箱\" value=\"email\" />\r\n                      <el-option label=\"📞 电话\" value=\"tel\" />\r\n                      <el-option label=\"🔘 单选\" value=\"radio\" />\r\n                      <el-option label=\"☑️ 多选\" value=\"checkbox\" />\r\n                      <el-option label=\"📋 下拉选择\" value=\"select\" />\r\n                      <el-option label=\"🔘➕ 单选+其他\" value=\"radio_other\" />\r\n                      <el-option label=\"☑️➕ 多选+其他\" value=\"checkbox_other\" />\r\n                      <el-option label=\"📋➕ 下拉+其他\" value=\"select_other\" />\r\n                      <el-option label=\"📅 日期\" value=\"date\" />\r\n                      <el-option label=\"📎 文件上传\" value=\"file\" />\r\n                    </el-select>\r\n                  </div>\r\n                </el-col>\r\n              </el-row>\r\n\r\n              <el-row :gutter=\"10\" style=\"margin-top: 10px;\">\r\n                <el-col :span=\"4\">\r\n                  <div class=\"field-item\">\r\n                    <label>是否必填</label>\r\n                    <el-switch v-model=\"field.required\" />\r\n                  </div>\r\n                </el-col>\r\n                <el-col :span=\"20\" v-if=\"['radio', 'checkbox', 'select', 'radio_other', 'checkbox_other', 'select_other'].includes(field.type)\">\r\n                  <div class=\"field-item\">\r\n                    <label>选项配置</label>\r\n                    <el-input\r\n                      v-model=\"field.options\"\r\n                      placeholder=\"用逗号分隔选项，如：选项1,选项2,选项3\"\r\n                      size=\"small\"\r\n                    />\r\n                    <div class=\"options-preview\" v-if=\"field.options\">\r\n                      <el-tag\r\n                        v-for=\"(option, optIndex) in field.options.split(',')\"\r\n                        :key=\"optIndex\"\r\n                        size=\"mini\"\r\n                        style=\"margin-right: 5px; margin-top: 5px;\"\r\n                      >\r\n                        {{ option.trim() }}\r\n                      </el-tag>\r\n                    </div>\r\n                  </div>\r\n                </el-col>\r\n              </el-row>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 空状态 -->\r\n        <div class=\"empty-state\" v-else>\r\n          <i class=\"el-icon-document-add\"></i>\r\n          <p>暂无表单字段，点击\"添加字段\"开始配置</p>\r\n        </div>\r\n      </div>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"formConfigOpen = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 表单预览对话框 -->\r\n    <el-dialog title=\"表单预览\" :visible.sync=\"previewDialogVisible\" width=\"600px\" append-to-body>\r\n      <div class=\"form-preview\">\r\n        <div class=\"preview-header\">\r\n          <h3>{{ currentActivity ? currentActivity.activityTitle : '路演报名表' }}</h3>\r\n          <p>请填写以下信息完成报名</p>\r\n        </div>\r\n        <div class=\"preview-form\">\r\n          <div v-for=\"(field, index) in formFields\" :key=\"index\" class=\"preview-field\">\r\n            <label class=\"preview-label\">\r\n              {{ field.label }}\r\n              <span v-if=\"field.required\" class=\"required\">*</span>\r\n            </label>\r\n            <div class=\"preview-input\">\r\n              <el-input\r\n                v-if=\"field.type === 'input' || field.type === 'email' || field.type === 'tel'\"\r\n                :placeholder=\"'请输入' + field.label\"\r\n                size=\"small\"\r\n                disabled\r\n              />\r\n              <el-input\r\n                v-else-if=\"field.type === 'textarea'\"\r\n                type=\"textarea\"\r\n                :placeholder=\"'请输入' + field.label\"\r\n                size=\"small\"\r\n                disabled\r\n              />\r\n              <el-input-number\r\n                v-else-if=\"field.type === 'number'\"\r\n                :placeholder=\"'请输入' + field.label\"\r\n                size=\"small\"\r\n                disabled\r\n                style=\"width: 100%\"\r\n              />\r\n              <el-radio-group v-else-if=\"field.type === 'radio'\" disabled>\r\n                <el-radio\r\n                  v-for=\"(option, optIndex) in (field.options || '').split(',')\"\r\n                  :key=\"optIndex\"\r\n                  :label=\"option.trim()\"\r\n                >\r\n                  {{ option.trim() }}\r\n                </el-radio>\r\n              </el-radio-group>\r\n              <el-checkbox-group v-else-if=\"field.type === 'checkbox'\" disabled>\r\n                <el-checkbox\r\n                  v-for=\"(option, optIndex) in (field.options || '').split(',')\"\r\n                  :key=\"optIndex\"\r\n                  :label=\"option.trim()\"\r\n                >\r\n                  {{ option.trim() }}\r\n                </el-checkbox>\r\n              </el-checkbox-group>\r\n              <el-select v-else-if=\"field.type === 'select'\" :placeholder=\"'请选择' + field.label\" size=\"small\" disabled style=\"width: 100%\">\r\n                <el-option\r\n                  v-for=\"(option, optIndex) in (field.options || '').split(',')\"\r\n                  :key=\"optIndex\"\r\n                  :label=\"option.trim()\"\r\n                  :value=\"option.trim()\"\r\n                />\r\n              </el-select>\r\n              <el-date-picker\r\n                v-else-if=\"field.type === 'date'\"\r\n                type=\"date\"\r\n                :placeholder=\"'请选择' + field.label\"\r\n                size=\"small\"\r\n                disabled\r\n                style=\"width: 100%\"\r\n              />\r\n              <el-upload\r\n                v-else-if=\"field.type === 'file'\"\r\n                class=\"upload-demo\"\r\n                action=\"#\"\r\n                :disabled=\"true\"\r\n                :show-file-list=\"false\"\r\n              >\r\n                <el-button size=\"small\" type=\"primary\" disabled>\r\n                  <i class=\"el-icon-upload\"></i> 选择文件\r\n                </el-button>\r\n                <div slot=\"tip\" class=\"el-upload__tip\">支持上传PDF、Word、Excel等格式文件</div>\r\n              </el-upload>\r\n              <!-- 单选+其他 -->\r\n              <div v-else-if=\"field.type === 'radio_other'\">\r\n                <el-radio-group disabled>\r\n                  <div v-for=\"(option, optIndex) in field.options.split(',')\" :key=\"optIndex\" style=\"display: block; margin-bottom: 8px;\">\r\n                    <el-radio :label=\"option.trim()\">{{ option.trim() }}</el-radio>\r\n                  </div>\r\n                  <div style=\"display: block; margin-bottom: 8px;\">\r\n                    <el-radio label=\"其他\">\r\n                      其他\r\n                      <el-input\r\n                        placeholder=\"请输入其他内容\"\r\n                        size=\"small\"\r\n                        disabled\r\n                        style=\"width: 200px; margin-left: 10px;\"\r\n                      />\r\n                    </el-radio>\r\n                  </div>\r\n                </el-radio-group>\r\n              </div>\r\n              <!-- 多选+其他 -->\r\n              <div v-else-if=\"field.type === 'checkbox_other'\">\r\n                <el-checkbox-group disabled>\r\n                  <div v-for=\"(option, optIndex) in field.options.split(',')\" :key=\"optIndex\" style=\"display: block; margin-bottom: 8px;\">\r\n                    <el-checkbox :label=\"option.trim()\">{{ option.trim() }}</el-checkbox>\r\n                  </div>\r\n                  <div style=\"display: block; margin-bottom: 8px;\">\r\n                    <el-checkbox label=\"其他\">\r\n                      其他\r\n                      <el-input\r\n                        placeholder=\"请输入其他内容\"\r\n                        size=\"small\"\r\n                        disabled\r\n                        style=\"width: 200px; margin-left: 10px;\"\r\n                      />\r\n                    </el-checkbox>\r\n                  </div>\r\n                </el-checkbox-group>\r\n              </div>\r\n              <!-- 下拉+其他 -->\r\n              <div v-else-if=\"field.type === 'select_other'\">\r\n                <el-select disabled placeholder=\"请选择\" size=\"small\" style=\"width: 100%; margin-bottom: 8px;\">\r\n                  <el-option v-for=\"(option, optIndex) in field.options.split(',')\" :key=\"optIndex\" :label=\"option.trim()\" :value=\"option.trim()\" />\r\n                  <el-option label=\"其他\" value=\"其他\" />\r\n                </el-select>\r\n                <el-input\r\n                  placeholder=\"选择'其他'时请在此输入具体内容\"\r\n                  size=\"small\"\r\n                  disabled\r\n                  style=\"width: 100%;\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"previewDialogVisible = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getActivityConfig, updateActivityConfig } from \"@/api/miniapp/xiqing/activity-config\";\r\n\r\nexport default {\r\n  name: \"XiqingActivityConfig\",\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 是否显示表单配置弹出层\r\n      formConfigOpen: false,\r\n      // 是否显示表单预览弹出层\r\n      previewDialogVisible: false,\r\n      // 表单字段配置\r\n      formFields: [],\r\n      // 活动ID（固定为1，因为只有一个路演活动配置）\r\n      activityId: 1\r\n    };\r\n  },\r\n  created() {\r\n    this.loadFormConfig();\r\n  },\r\n  methods: {\r\n    /** 加载表单配置 */\r\n    loadFormConfig() {\r\n      this.loading = true;\r\n      getActivityConfig(this.activityId).then(response => {\r\n        if (response.data && response.data.formConfig) {\r\n          try {\r\n            this.formFields = JSON.parse(response.data.formConfig);\r\n          } catch (e) {\r\n            this.formFields = [];\r\n          }\r\n        } else {\r\n          this.formFields = [];\r\n        }\r\n        this.loading = false;\r\n      }).catch(() => {\r\n        this.formFields = [];\r\n        this.loading = false;\r\n      });\r\n    },\r\n    /** 表单配置按钮操作 */\r\n    handleFormConfig() {\r\n      this.formConfigOpen = true;\r\n    },\r\n    /** 添加表单字段 */\r\n    addFormField() {\r\n      this.formFields.push({\r\n        name: '',\r\n        label: '',\r\n        type: 'input',\r\n        required: false,\r\n        options: ''\r\n      });\r\n    },\r\n    /** 删除表单字段 */\r\n    removeFormField(index) {\r\n      this.formFields.splice(index, 1);\r\n    },\r\n    /** 移动字段位置 */\r\n    moveField(index, direction) {\r\n      const newIndex = index + direction;\r\n      if (newIndex >= 0 && newIndex < this.formFields.length) {\r\n        const temp = this.formFields[index];\r\n        this.$set(this.formFields, index, this.formFields[newIndex]);\r\n        this.$set(this.formFields, newIndex, temp);\r\n      }\r\n    },\r\n    /** 更新字段名称 */\r\n    updateFieldName(field, label) {\r\n      if (!field.name || field.name === '') {\r\n        field.name = this.generateFieldName(label);\r\n      }\r\n    },\r\n    /** 生成字段名称 */\r\n    generateFieldName(label) {\r\n      const pinyin = {\r\n        '姓名': 'name',\r\n        '联系电话': 'phone',\r\n        '电话': 'phone',\r\n        '邮箱': 'email',\r\n        '邮箱地址': 'email',\r\n        '公司': 'company',\r\n        '项目名称': 'project_name',\r\n        '项目描述': 'project_description',\r\n        '团队规模': 'team_size'\r\n      };\r\n      return pinyin[label] || label.toLowerCase().replace(/\\s+/g, '_');\r\n    },\r\n    /** 获取字段图标 */\r\n    getFieldIcon(type) {\r\n      const icons = {\r\n        input: 'el-icon-edit',\r\n        textarea: 'el-icon-document',\r\n        number: 'el-icon-s-data',\r\n        email: 'el-icon-message',\r\n        tel: 'el-icon-phone',\r\n        radio: 'el-icon-success',\r\n        checkbox: 'el-icon-check',\r\n        select: 'el-icon-arrow-down',\r\n        radio_other: 'el-icon-circle-plus',\r\n        checkbox_other: 'el-icon-square-plus',\r\n        select_other: 'el-icon-plus',\r\n        date: 'el-icon-date',\r\n        file: 'el-icon-upload'\r\n      };\r\n      return icons[type] || 'el-icon-edit';\r\n    },\r\n    /** 处理模板命令 */\r\n    handleTemplateCommand(command) {\r\n      if (command === 'clear') {\r\n        this.$confirm('确定要清空所有字段吗？', '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }).then(() => {\r\n          this.formFields = [];\r\n          this.$message.success('已清空所有字段');\r\n        });\r\n        return;\r\n      }\r\n\r\n      const templates = {\r\n        basic: [\r\n          { label: '姓名', name: 'name', type: 'input', required: true, options: '' },\r\n          { label: '联系电话', name: 'phone', type: 'tel', required: true, options: '' },\r\n          { label: '邮箱地址', name: 'email', type: 'email', required: false, options: '' }\r\n        ],\r\n        roadshow: [\r\n          { label: '姓名', name: 'name', type: 'input', required: true, options: '' },\r\n          { label: '联系电话', name: 'phone', type: 'tel', required: true, options: '' },\r\n          { label: '邮箱地址', name: 'email', type: 'email', required: true, options: '' },\r\n          { label: '公司/团队', name: 'company', type: 'input', required: true, options: '' },\r\n          { label: '项目名称', name: 'project_name', type: 'input', required: true, options: '' },\r\n          { label: '项目来源', name: 'project_source', type: 'radio_other', required: true, options: '社会,高校,科研院所,企业内部' },\r\n          { label: '项目阶段', name: 'project_stage', type: 'radio', required: true, options: '概念阶段,开发阶段,测试阶段,上线阶段' },\r\n          { label: '团队规模', name: 'team_size', type: 'select', required: true, options: '1人,2-3人,4-5人,6-10人,10人以上' },\r\n          { label: '融资需求', name: 'funding_needs', type: 'input', required: false, options: '' },\r\n          { label: '商业计划书', name: 'business_plan', type: 'file', required: true, options: '' },\r\n          { label: '项目描述', name: 'project_description', type: 'textarea', required: true, options: '' }\r\n        ]\r\n      };\r\n\r\n      if (templates[command]) {\r\n        this.formFields = templates[command];\r\n        this.$message.success('模板应用成功');\r\n      }\r\n    },\r\n    /** 预览表单 */\r\n    previewForm() {\r\n      if (this.formFields.length === 0) {\r\n        this.$message.warning('请先添加表单字段');\r\n        return;\r\n      }\r\n      this.previewDialogVisible = true;\r\n    },\r\n    /** 保存表单配置 */\r\n    saveFormConfig() {\r\n      if (this.formFields.length === 0) {\r\n        this.$message.warning('请至少添加一个表单字段');\r\n        return;\r\n      }\r\n\r\n      // 验证字段配置\r\n      for (let i = 0; i < this.formFields.length; i++) {\r\n        const field = this.formFields[i];\r\n        if (!field.label) {\r\n          this.$message.error(`第${i + 1}个字段的标签不能为空`);\r\n          return;\r\n        }\r\n        if (['radio', 'checkbox', 'select', 'radio_other', 'checkbox_other', 'select_other'].includes(field.type) && !field.options) {\r\n          this.$message.error(`字段\"${field.label}\"需要配置选项`);\r\n          return;\r\n        }\r\n      }\r\n\r\n      const configData = JSON.stringify(this.formFields);\r\n      const updateData = {\r\n        activityId: this.activityId,\r\n        formConfig: configData\r\n      };\r\n\r\n      updateActivityConfig(updateData).then(response => {\r\n        this.$modal.msgSuccess(\"表单配置保存成功\");\r\n        this.formConfigOpen = false;\r\n        this.loadFormConfig();\r\n      });\r\n    },\r\n    /** 获取字段类型名称 */\r\n    getFieldTypeName(type) {\r\n      const typeNames = {\r\n        input: '文本输入',\r\n        textarea: '多行文本',\r\n        number: '数字输入',\r\n        email: '邮箱',\r\n        tel: '电话',\r\n        radio: '单选',\r\n        checkbox: '多选',\r\n        select: '下拉选择',\r\n        radio_other: '单选+其他',\r\n        checkbox_other: '多选+其他',\r\n        select_other: '下拉+其他',\r\n        date: '日期',\r\n        file: '文件上传'\r\n      };\r\n      return typeNames[type] || '未知类型';\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.form-config-container {\r\n  max-width: 800px;\r\n  margin: 20px auto;\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  overflow: hidden;\r\n}\r\n\r\n.config-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 20px 24px;\r\n  background: #f8f9fa;\r\n  border-bottom: 1px solid #e9ecef;\r\n}\r\n\r\n.config-header h3 {\r\n  margin: 0;\r\n  color: #303133;\r\n  font-size: 18px;\r\n  font-weight: 500;\r\n}\r\n\r\n.config-content {\r\n  padding: 24px;\r\n  min-height: 300px;\r\n}\r\n\r\n.form-preview h4 {\r\n  margin: 0 0 20px 0;\r\n  color: #303133;\r\n  font-size: 16px;\r\n}\r\n\r\n.field-list {\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 6px;\r\n  overflow: hidden;\r\n}\r\n\r\n.field-item {\r\n  padding: 12px 16px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n  background: #fafbfc;\r\n}\r\n\r\n.field-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.field-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.field-icon {\r\n  color: #409eff;\r\n  font-size: 16px;\r\n}\r\n\r\n.field-label {\r\n  font-weight: 500;\r\n  color: #303133;\r\n  min-width: 100px;\r\n}\r\n\r\n.field-type {\r\n  color: #909399;\r\n  font-size: 12px;\r\n  margin-left: auto;\r\n}\r\n\r\n.empty-form {\r\n  text-align: center;\r\n  padding: 60px 20px;\r\n  color: #909399;\r\n}\r\n\r\n.empty-form i {\r\n  font-size: 48px;\r\n  margin-bottom: 16px;\r\n  display: block;\r\n}\r\n\r\n.form-fields-config {\r\n  min-height: 400px;\r\n}\r\n\r\n.form-fields-toolbar {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n  padding: 10px;\r\n  background: #f5f7fa;\r\n  border-radius: 4px;\r\n}\r\n\r\n.toolbar-left, .toolbar-right {\r\n  display: flex;\r\n  gap: 10px;\r\n}\r\n\r\n.form-fields-list {\r\n  max-height: 500px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.form-field-item {\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 6px;\r\n  margin-bottom: 15px;\r\n  background: #fff;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.form-field-item:hover {\r\n  border-color: #409eff;\r\n  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);\r\n}\r\n\r\n.field-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 12px 16px;\r\n  background: #fafbfc;\r\n  border-bottom: 1px solid #e4e7ed;\r\n  border-radius: 6px 6px 0 0;\r\n}\r\n\r\n.field-actions {\r\n  display: flex;\r\n  gap: 5px;\r\n}\r\n\r\n.danger-btn {\r\n  color: #f56c6c;\r\n}\r\n\r\n.field-content {\r\n  padding: 16px;\r\n}\r\n\r\n.field-item {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.field-item label {\r\n  display: block;\r\n  margin-bottom: 5px;\r\n  font-size: 12px;\r\n  color: #606266;\r\n  font-weight: 500;\r\n}\r\n\r\n.options-preview {\r\n  margin-top: 5px;\r\n}\r\n\r\n.empty-state {\r\n  text-align: center;\r\n  padding: 60px 20px;\r\n  color: #909399;\r\n}\r\n\r\n.empty-state i {\r\n  font-size: 48px;\r\n  margin-bottom: 16px;\r\n  display: block;\r\n}\r\n\r\n.form-preview {\r\n  padding: 20px;\r\n}\r\n\r\n.preview-header {\r\n  text-align: center;\r\n  margin-bottom: 30px;\r\n  padding-bottom: 20px;\r\n  border-bottom: 1px solid #e4e7ed;\r\n}\r\n\r\n.preview-header h3 {\r\n  margin: 0 0 10px 0;\r\n  color: #303133;\r\n}\r\n\r\n.preview-header p {\r\n  margin: 0;\r\n  color: #909399;\r\n  font-size: 14px;\r\n}\r\n\r\n.preview-field {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.preview-label {\r\n  display: block;\r\n  margin-bottom: 8px;\r\n  font-weight: 500;\r\n  color: #606266;\r\n}\r\n\r\n.required {\r\n  color: #f56c6c;\r\n  margin-left: 2px;\r\n}\r\n\r\n.preview-input {\r\n  width: 100%;\r\n}\r\n</style>\r\n"]}]}