{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\xiqing\\activity-config\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\xiqing\\activity-config\\index.vue", "mtime": 1753758422831}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\babel.config.js", "mtime": 1751427652172}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}