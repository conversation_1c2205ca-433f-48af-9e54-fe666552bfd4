{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\xiqing\\registration-manage\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\xiqing\\registration-manage\\index.vue", "mtime": 1753771710724}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\babel.config.js", "mtime": 1751427652172}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}