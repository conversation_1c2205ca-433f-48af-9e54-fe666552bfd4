package com.ruoyi.miniapp.controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.annotation.Anonymous;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.service.ISysDeptService;
import com.ruoyi.system.service.ISysRoleService;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.system.mapper.SysUserRoleMapper;
import com.ruoyi.framework.web.service.TokenService;
import com.ruoyi.framework.web.service.SysPermissionService;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.miniapp.domain.dto.MiniappLoginRequest;
import com.ruoyi.miniapp.domain.dto.MiniappLoginResponse;
import com.ruoyi.miniapp.domain.dto.UserProfileUpdateRequest;
import com.ruoyi.miniapp.domain.dto.WechatJscode2sessionResponse;
import com.ruoyi.miniapp.service.WechatMiniappService;
import com.ruoyi.miniapp.utils.WechatDecryptUtils;
import com.ruoyi.miniapp.annotation.SensitiveWordFilter;
import java.util.Set;
import java.util.HashSet;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 小程序用户管理Controller
 * 
 * <AUTHOR>
 * @date 2025-07-10
 */
@Api(tags = "小程序用户管理")
@RestController
@RequestMapping("/miniapp/user")
public class MiniUserController extends BaseController
{
    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysRoleService roleService;

    @Autowired
    private ISysDeptService deptService;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private SysPermissionService permissionService;

    @Autowired
    private SysUserRoleMapper userRoleMapper;

    @Autowired
    private WechatMiniappService wechatMiniappService;

    @Autowired
    private com.ruoyi.miniapp.service.IMiniIndustryTreeService miniIndustryTreeService;

    @Autowired
    private com.ruoyi.miniapp.service.IMiniJobTypeService miniJobTypeService;

    @Autowired
    private com.ruoyi.miniapp.service.IMiniEnterpriseService miniEnterpriseService;

    @Autowired
    private com.ruoyi.miniapp.service.IMiniUserFollowService miniUserFollowService;

    /**
     * 查询小程序用户列表
     */
    @ApiOperation("查询小程序用户列表")
    @PreAuthorize("@ss.hasPermi('miniapp:user:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysUser user)
    {
        startPage();
        // 只查询小程序用户
        user.setUserType("01");
        List<SysUser> list = userService.selectUserListWithIndustry(user);

//        // 过滤掉admin角色的用户
//        List<SysUser> filteredList = list.stream()
//            .filter(u -> !hasAdminRole(u))
//            .collect(Collectors.toList());

        return getDataTable(list);
    }

    /**
     * 导出小程序用户列表
     */
    @ApiOperation("导出小程序用户列表")
    @PreAuthorize("@ss.hasPermi('miniapp:user:export')")
    @Log(title = "小程序用户", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysUser user)
    {
        // 只查询小程序用户
        user.setUserType("01");
        List<SysUser> list = userService.selectUserList(user);

        // 过滤掉admin角色的用户
        List<SysUser> filteredList = list.stream()
            .filter(u -> !hasAdminRole(u))
            .collect(Collectors.toList());

        ExcelUtil<SysUser> util = new ExcelUtil<SysUser>(SysUser.class);
        util.exportExcel(response, filteredList, "小程序用户数据");
    }

    /**
     * 根据用户编号获取详细信息
     */
    @ApiOperation("获取小程序用户详细信息")
    @PreAuthorize("@ss.hasPermi('miniapp:user:query')")
    @GetMapping("/{userId}")
    public AjaxResult getInfo(@PathVariable("userId") Long userId)
    {
        userService.checkUserDataScope(userId);
        SysUser sysUser = userService.selectUserById(userId);

        // 检查是否为admin用户
        if (hasAdminRole(sysUser))
        {
            return error("无权限访问admin用户信息");
        }

        // 检查是否为小程序用户
        if (!"01".equals(sysUser.getUserType()))
        {
            return error("该用户不是小程序用户");
        }

        AjaxResult ajax = AjaxResult.success();
        ajax.put(AjaxResult.DATA_TAG, sysUser);
        return ajax;
    }



    /**
     * 修改小程序用户
     */
    @ApiOperation("修改小程序用户")
    @PreAuthorize("@ss.hasPermi('miniapp:user:edit')")
    @Log(title = "小程序用户", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody SysUser user)
    {
        // 检查是否为admin用户
        SysUser existingUser = userService.selectUserById(user.getUserId());
        if (hasAdminRole(existingUser))
        {
            return error("不允许修改admin用户");
        }
        
        // 检查角色分配限制
        if (hasRestrictedRoles(user.getRoleIds()))
        {
            return error("不允许分配admin或mini角色");
        }
        
        userService.checkUserAllowed(user);
        userService.checkUserDataScope(user.getUserId());
        if (user.getDeptId() != null) {
            deptService.checkDeptDataScope(user.getDeptId());
        }
        roleService.checkRoleDataScope(user.getRoleIds());
        
        if (!userService.checkUserNameUnique(user))
        {
            return error("修改用户'" + user.getUserName() + "'失败，登录账号已存在");
        }
        else if (StringUtils.isNotEmpty(user.getPhonenumber()) && !userService.checkPhoneUnique(user))
        {
            return error("修改用户'" + user.getUserName() + "'失败，手机号码已存在");
        }
        else if (StringUtils.isNotEmpty(user.getEmail()) && !userService.checkEmailUnique(user))
        {
            return error("修改用户'" + user.getUserName() + "'失败，邮箱账号已存在");
        }
        
        user.setUpdateBy(getUsername());
        return toAjax(userService.updateUser(user));
    }

    /**
     * 批量停用小程序用户
     */
    @ApiOperation("批量停用小程序用户")
    @PreAuthorize("@ss.hasPermi('miniapp:user:edit')")
    @Log(title = "小程序用户", businessType = BusinessType.UPDATE)
    @PutMapping("/batchDisable")
    public AjaxResult batchDisable(@RequestBody Long[] userIds)
    {
        // 检查是否包含admin用户
        for (Long userId : userIds)
        {
            SysUser user = userService.selectUserById(userId);
            if (hasAdminRole(user))
            {
                return error("不允许停用admin用户");
            }
        }

        // 批量停用用户
        int result = 0;
        for (Long userId : userIds)
        {
            SysUser user = new SysUser();
            user.setUserId(userId);
            user.setStatus("1"); // 设置为停用状态
            user.setUpdateBy(getUsername());
            result += userService.updateUserStatus(user);
        }

        return toAjax(result);
    }



    /**
     * 状态修改
     */
    @ApiOperation("修改小程序用户状态")
    @PreAuthorize("@ss.hasPermi('miniapp:user:edit')")
    @Log(title = "小程序用户", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody SysUser user)
    {
        // 检查是否为admin用户
        SysUser existingUser = userService.selectUserById(user.getUserId());
        if (hasAdminRole(existingUser))
        {
            return error("不允许修改admin用户状态");
        }
        
        userService.checkUserAllowed(user);
        userService.checkUserDataScope(user.getUserId());
        user.setUpdateBy(getUsername());
        return toAjax(userService.updateUserStatus(user));
    }



    /**
     * 检查用户是否有admin角色
     */
    private boolean hasAdminRole(SysUser user)
    {
        if (user == null || user.getRoles() == null)
        {
            return false;
        }
        return user.getRoles().stream()
            .anyMatch(role -> "admin".equals(role.getRoleKey()));
    }

    /**
     * 获取当前用户信息（需要token验证）
     * 前端每次进入小程序时调用此接口
     * - token有效：返回用户信息，直接进入小程序
     * - token无效：返回401，前端跳转注册页
     */
    @ApiOperation("获取当前用户信息")
    @GetMapping("/getCurrentUser")
    public AjaxResult getCurrentUser()
    {
        try
        {
            // 获取当前登录用户（通过token验证）
            LoginUser loginUser = SecurityUtils.getLoginUser();
            SysUser currentUser = loginUser.getUser();

            // 构建响应数据 - 与登录接口保持一致的格式
            MiniappLoginResponse response = buildLoginResponse(currentUser, loginUser.getToken(), false);

            // 添加额外的信息
            Map<String, Object> result = new HashMap<>();
            result.put("token", response.getToken());
            result.put("userId", response.getUserId());
            result.put("userName", response.getUserName());
            result.put("nickName", response.getNickName());
            result.put("weixinNickname", response.getWeixinNickname());
            result.put("avatar", response.getAvatar());
            result.put("weixinAvatar", response.getWeixinAvatar());
            result.put("realName", response.getRealName());
            result.put("phonenumber", response.getPhonenumber());
            result.put("sex", response.getSex());
            result.put("totalPoints", response.getTotalPoints());
            result.put("isNewUser", response.getIsNewUser());
            result.put("isInfoComplete", StringUtils.isNotEmpty(currentUser.getRealName()));
            result.put("permissions", loginUser.getPermissions());

            return AjaxResult.success("获取用户信息成功", result);
        }
        catch (Exception e)
        {
            logger.error("获取当前用户信息失败", e);
            return error("获取用户信息失败：" + e.getMessage());
        }
    }

    /**
     * 小程序微信登录注册
     */
    @Anonymous
    @ApiOperation("小程序微信登录注册")
    @PostMapping("/weixinLogin")
    public AjaxResult weixinLogin(@ApiParam("登录信息") @RequestBody MiniappLoginRequest loginRequest)
    {
        try
        {
            // 验证必要参数
            if (StringUtils.isEmpty(loginRequest.getCode()))
            {
                return error("微信登录凭证code不能为空");
            }

            // 验证微信配置
            if (!wechatMiniappService.isConfigValid())
            {
                return error("微信小程序配置不完整，请联系管理员");
            }

            // 通过code换取openid和session_key
            WechatJscode2sessionResponse wechatResponse = wechatMiniappService.jscode2session(loginRequest.getCode());

            // 设置从微信获取的信息到登录请求中
            loginRequest.setOpenid(wechatResponse.getOpenid());
            loginRequest.setSessionKey(wechatResponse.getSessionKey());
            if (StringUtils.isNotEmpty(wechatResponse.getUnionid()))
            {
                loginRequest.setUnionid(wechatResponse.getUnionid());
            }

            // 如果有加密数据，解密手机号
            if (StringUtils.isNotEmpty(loginRequest.getEncryptedData()) && StringUtils.isNotEmpty(loginRequest.getIv()))
            {
                try
                {
                    WechatDecryptUtils.WechatPhoneInfo phoneInfo = wechatMiniappService.decryptPhoneNumber(
                        loginRequest.getEncryptedData(),
                        wechatResponse.getSessionKey(),
                        loginRequest.getIv()
                    );
                    loginRequest.setPhonenumber(phoneInfo.getPurePhoneNumber());
                    logger.info("成功解密手机号: {}", phoneInfo.getPurePhoneNumber());
                }
                catch (Exception e)
                {
                    logger.warn("解密手机号失败，继续登录流程: {}", e.getMessage());
                }
            }

            // 查询是否已存在该用户
            SysUser existingUser =userService.selectUserByOpenid(loginRequest.getOpenid());
            boolean isNewUser = false;
            SysUser user;

            if (existingUser == null)
            {
                // 新用户注册
                user = createNewMiniappUser(loginRequest);
                isNewUser = true;
            }
            else
            {
                // 已存在用户，更新登录信息
                user = updateExistingUser(existingUser, loginRequest);

//                 清除该用户之前的所有token
                tokenService.clearUserTokens(user.getUserId());
                logger.info("已清除用户{}之前的所有token", user.getUserId());
            }

            // 生成登录token
            String token = generateLoginToken(user);

            // 构建响应数据
            MiniappLoginResponse response = buildLoginResponse(user, token, isNewUser);

            return AjaxResult.success("登录成功", response);
        }
        catch (Exception e)
        {
            logger.error("小程序登录失败", e);
            return error("登录失败：" + e.getMessage());
        }
    }

    /**
     * 检查角色数组是否包含受限角色
     */
    private boolean hasRestrictedRoles(Long[] roleIds)
    {
        if (roleIds == null || roleIds.length == 0)
        {
            return false;
        }

        for (Long roleId : roleIds)
        {
            SysRole role = roleService.selectRoleById(roleId);
            if (role != null && ("admin".equals(role.getRoleKey()) || "mini".equals(role.getRoleKey())))
            {
                return true;
            }
        }
        return false;
    }

    // ================================ 小程序端接口 ================================


    /**
     * 完善用户资料
     */
    @ApiOperation("完善用户资料")
    @SensitiveWordFilter(moduleName = "用户资料", strategy = SensitiveWordFilter.FilterStrategy.REPLACE)
    @PostMapping("/updateProfile")
    public AjaxResult updateProfile(@ApiParam("用户资料信息") @RequestBody UserProfileUpdateRequest profileRequest)
    {
        try
        {
            // 从token中获取当前用户
            LoginUser loginUser = SecurityUtils.getLoginUser();
            if (loginUser == null)
            {
                return error("用户未登录");
            }

            // 获取用户信息
            SysUser user = userService.selectUserById(loginUser.getUserId());
            if (user == null || !"01".equals(user.getUserType()))
            {
                return error("用户信息不存在或非小程序用户");
            }

            // 记录更新前的状态，用于计算积分奖励
            boolean hadBasicInfo = hasBasicInfo(user);
            boolean hadEducationInfo = hasEducationInfo(user);
            boolean hadCareerInfo = hasCareerInfo(user);

            // 处理企业信息
            Long enterpriseId = handleEnterpriseInfo(profileRequest, user);

            // 更新用户信息
            updateUserProfile(user, profileRequest, enterpriseId);

            // 计算并奖励积分（在保存前计算，这样积分也会一起保存）
            int pointsAwarded = calculateAndAwardPoints(user, hadBasicInfo, hadEducationInfo, hadCareerInfo);

            // 保存更新（包括积分）
            user.setUpdateBy(getUsername());
            user.setUpdateTime(DateUtils.getNowDate());
            Long[] roleIds = {101L};
            user.setRoleIds(roleIds);
            int result = userService.updateUser(user);

            if (result > 0)
            {
                // 返回更新后的用户信息
                MiniappLoginResponse userInfo = buildLoginResponse(user, null, false);

                Map<String, Object> responseData = new HashMap<>();
                responseData.put("userInfo", userInfo);
                responseData.put("pointsAwarded", pointsAwarded);

                return AjaxResult.success("资料更新成功", responseData);
            }
            else
            {
                return error("资料更新失败");
            }
        }
        catch (Exception e)
        {
            logger.error("完善用户资料失败", e);
            return error("完善用户资料失败：" + e.getMessage());
        }
    }

    /**
     * 获取用户资料详情
     */
    @ApiOperation("获取用户资料详情")
    @GetMapping("/getProfileDetail")
    public AjaxResult getProfileDetail()
    {
        try
        {
            // 从token中获取当前用户
            LoginUser loginUser = SecurityUtils.getLoginUser();
            if (loginUser == null)
            {
                return error("用户未登录");
            }

            // 获取用户信息
            SysUser user = userService.selectUserById(loginUser.getUserId());
            if (user == null || !"01".equals(user.getUserType()))
            {
                return error("用户信息不存在或非小程序用户");
            }

            // 构建详细的用户资料信息
            Map<String, Object> profileDetail = buildProfileDetail(user);

            return AjaxResult.success("获取成功", profileDetail);
        }
        catch (Exception e)
        {
            logger.error("获取用户资料详情失败", e);
            return error("获取用户资料详情失败：" + e.getMessage());
        }
    }

    /**
     * 创建新的小程序用户
     */
    private SysUser createNewMiniappUser(MiniappLoginRequest loginRequest)
    {
        SysUser user = new SysUser();

        // 生成用户名（使用openid后8位 + 时间戳后4位）
        String userName = "mini_" + loginRequest.getOpenid().substring(Math.max(0, loginRequest.getOpenid().length() - 8))
                         + String.valueOf(System.currentTimeMillis()).substring(9);


        // 设置基本信息 - 优先使用传入的昵称，其次使用微信昵称
        String nickName = "小程序用户"; // 默认昵称
        if (StringUtils.isNotEmpty(loginRequest.getNickName()))
        {
            userName = loginRequest.getNickName();
            nickName = loginRequest.getNickName();
        }
        else if (StringUtils.isNotEmpty(loginRequest.getWeixinNickname()))
        {
            userName = loginRequest.getNickName();
            nickName = loginRequest.getWeixinNickname();
        }
        user.setUserName(userName);
        user.setNickName(nickName);
        user.setUserType("01"); // 小程序用户
        user.setStatus("0"); // 正常状态
        user.setDelFlag("0"); // 未删除
        user.setCreateBy("miniapp");
        user.setCreateTime(DateUtils.getNowDate());
        user.setUpdateTime(DateUtils.getNowDate());

        // 设置微信相关信息
        user.setOpenid(loginRequest.getOpenid());
        user.setWeixinNickname(loginRequest.getWeixinNickname());
        user.setWeixinAvatar(loginRequest.getWeixinAvatar());
        user.setUnionid(loginRequest.getUnionid());
        user.setSessionKey(loginRequest.getSessionKey());

        // 设置其他信息
        if (StringUtils.isNotEmpty(loginRequest.getPhonenumber()))
        {
            user.setPhonenumber(loginRequest.getPhonenumber());
        }
        if (StringUtils.isNotEmpty(loginRequest.getRealName()))
        {
            user.setRealName(loginRequest.getRealName());
        }
        if (StringUtils.isNotEmpty(loginRequest.getSex()))
        {
            user.setSex(loginRequest.getSex());
        }

        // 设置头像 - 优先使用传入的头像，其次使用微信头像
        String avatar = "";
        if (StringUtils.isNotEmpty(loginRequest.getAvatar()))
        {
            avatar = loginRequest.getAvatar();
        }
        else if (StringUtils.isNotEmpty(loginRequest.getWeixinAvatar()))
        {
            avatar = loginRequest.getWeixinAvatar();
        }
        user.setAvatar(avatar);

        // 初始化积分
        user.setTotalPoints(0);
        user.setLastLoginTime(DateUtils.getNowDate());

        // 保存用户
        userService.insertUser(user);

        // 分配小程序用户角色（role_id = 101）
        Long[] roleIds = {101L}; // 小程序用户角色ID
        userService.insertUserAuth(user.getUserId(), roleIds);

        return user;
    }

    /**
     * 更新已存在用户的登录信息
     */
    private SysUser updateExistingUser(SysUser existingUser, MiniappLoginRequest loginRequest)
    {
        // 检查用户状态
        if ("1".equals(existingUser.getStatus()))
        {
            throw new ServiceException("用户账号已被停用，无法登录");
        }
        // 更新昵称 - 优先使用传入的昵称，其次使用微信昵称
        if (StringUtils.isNotEmpty(loginRequest.getNickName()))
        {
            existingUser.setNickName(loginRequest.getNickName());
        }
        else if (StringUtils.isNotEmpty(loginRequest.getWeixinNickname()))
        {
            existingUser.setNickName(loginRequest.getWeixinNickname());
        }

        // 更新头像 - 优先使用传入的头像，其次使用微信头像
        if (StringUtils.isNotEmpty(loginRequest.getAvatar()))
        {
            existingUser.setAvatar(loginRequest.getAvatar());
        }
        else if (StringUtils.isNotEmpty(loginRequest.getWeixinAvatar()))
        {
            existingUser.setAvatar(loginRequest.getWeixinAvatar());
        }

        // 更新微信相关信息
        if (StringUtils.isNotEmpty(loginRequest.getWeixinNickname()))
        {
            existingUser.setWeixinNickname(loginRequest.getWeixinNickname());
        }
        if (StringUtils.isNotEmpty(loginRequest.getWeixinAvatar()))
        {
            existingUser.setWeixinAvatar(loginRequest.getWeixinAvatar());
        }
        if (StringUtils.isNotEmpty(loginRequest.getUnionid()))
        {
            existingUser.setUnionid(loginRequest.getUnionid());
        }
        if (StringUtils.isNotEmpty(loginRequest.getSessionKey()))
        {
            existingUser.setSessionKey(loginRequest.getSessionKey());
        }

        // 更新其他信息（如果提供）
        if (StringUtils.isNotEmpty(loginRequest.getPhonenumber()))
        {
            existingUser.setPhonenumber(loginRequest.getPhonenumber());
        }
        if (StringUtils.isNotEmpty(loginRequest.getRealName()))
        {
            existingUser.setRealName(loginRequest.getRealName());
        }
        if (StringUtils.isNotEmpty(loginRequest.getSex()))
        {
            existingUser.setSex(loginRequest.getSex());
        }

        // 更新登录时间
        existingUser.setLastLoginTime(DateUtils.getNowDate());
        existingUser.setUpdateTime(DateUtils.getNowDate());
        existingUser.setUpdateBy("miniapp");

        // 更新用户信息
        Long[] roleIds = {101L};
        existingUser.setRoleIds(roleIds);
        userService.updateUser(existingUser);

        return existingUser;
    }

    /**
     * 生成登录token
     */
    private String generateLoginToken(SysUser user)
    {
        // 获取用户角色权限
        Set<String> permissions = new HashSet<>();

//        // 添加基本权限
//        permissions.add("miniapp:user:view");

        // 获取用户的角色权限
        Set<String> rolePermissions = permissionService.getRolePermission(user);
        if (rolePermissions != null && !rolePermissions.isEmpty()) {
            permissions.addAll(rolePermissions);
        }

        // 获取用户的菜单权限
        Set<String> menuPermissions = permissionService.getMenuPermission(user);
        if (menuPermissions != null && !menuPermissions.isEmpty()) {
            permissions.addAll(menuPermissions);
        }

        // 创建LoginUser对象
        LoginUser loginUser = new LoginUser(user.getUserId(), user.getDeptId(), user, permissions);

        // 生成token
        return tokenService.createToken(loginUser);
    }

    /**
     * 构建登录响应数据
     */
    private MiniappLoginResponse buildLoginResponse(SysUser user, String token, Boolean isNewUser)
    {
        MiniappLoginResponse response = new MiniappLoginResponse();

        response.setToken(token);
        response.setUserId(user.getUserId());
        response.setUserName(user.getUserName());
        response.setNickName(user.getNickName());
        response.setWeixinNickname(user.getWeixinNickname());
        response.setAvatar(user.getAvatar());
        response.setWeixinAvatar(user.getWeixinAvatar());
        response.setRealName(user.getRealName());
        response.setPhonenumber(user.getPhonenumber());
        response.setSex(user.getSex());
        response.setTotalPoints(user.getTotalPoints() != null ? user.getTotalPoints() : 0);
        response.setIsNewUser(isNewUser);

        return response;
    }

    /**
     * 构建基本用户信息（不包含敏感信息）
     */
    private Map<String, Object> buildBasicUserInfo(SysUser user)
    {
        Map<String, Object> userInfo = new HashMap<>();
        userInfo.put("userId", user.getUserId());
        userInfo.put("nickName", user.getNickName());
        userInfo.put("avatar", user.getAvatar());
        userInfo.put("hasPhone", StringUtils.isNotEmpty(user.getPhonenumber()));
        userInfo.put("hasRealName", StringUtils.isNotEmpty(user.getRealName()));
        userInfo.put("totalPoints", user.getTotalPoints() != null ? user.getTotalPoints() : 0);
        return userInfo;
    }


    /**
     * 处理企业信息
     */
    private Long handleEnterpriseInfo(UserProfileUpdateRequest profileRequest, SysUser user)
    {
        if (StringUtils.isEmpty(profileRequest.getCurrentCompany()))
        {
            return null;
        }

        try
        {
            // 查询企业是否已存在
            com.ruoyi.miniapp.domain.MiniEnterprise queryParam = new com.ruoyi.miniapp.domain.MiniEnterprise();
            queryParam.setEnterpriseName(profileRequest.getCurrentCompany());
            List<com.ruoyi.miniapp.domain.MiniEnterprise> existingEnterprises = miniEnterpriseService.selectMiniEnterpriseList(queryParam);

            com.ruoyi.miniapp.domain.MiniEnterprise enterprise;
            if (existingEnterprises != null && !existingEnterprises.isEmpty())
            {
                // 企业已存在，使用现有企业
                enterprise = existingEnterprises.get(0);
            }
            else
            {
                // 企业不存在，创建新企业
                enterprise = new com.ruoyi.miniapp.domain.MiniEnterprise();
                enterprise.setEnterpriseName(profileRequest.getCurrentCompany());
                enterprise.setStatus("0"); // 正常状态
                enterprise.setCreateBy(user.getUserName());
                enterprise.setCreateTime(DateUtils.getNowDate());
                enterprise.setUpdateTime(DateUtils.getNowDate());

                miniEnterpriseService.insertMiniEnterprise(enterprise);
            }

            // 处理企业与行业的关联关系
            if (StringUtils.isNotEmpty(profileRequest.getIndustryField()))
            {
                handleEnterpriseIndustryRelation(enterprise.getEnterpriseId(), profileRequest.getIndustryField());
            }

            return enterprise.getEnterpriseId();
        }
        catch (Exception e)
        {
            logger.error("处理企业信息失败", e);
            return null;
        }
    }

    /**
     * 处理企业与行业的关联关系
     */
    private void handleEnterpriseIndustryRelation(Long enterpriseId, String industryField)
    {
        try
        {
            // 解析行业ID字符串
            String[] industryIds = industryField.split(",");
            List<com.ruoyi.miniapp.domain.MiniEnterprise.MiniEnterpriseIndustry> industryList = new ArrayList<>();

            for (String industryIdStr : industryIds)
            {
                if (StringUtils.isNotEmpty(industryIdStr.trim()))
                {
                    Long industryId = Long.parseLong(industryIdStr.trim());
                    com.ruoyi.miniapp.domain.MiniEnterprise.MiniEnterpriseIndustry industry =
                        new com.ruoyi.miniapp.domain.MiniEnterprise.MiniEnterpriseIndustry();
                    industry.setEnterpriseId(enterpriseId);
                    industry.setIndustryTreeId(industryId);
                    industry.setCreateTime(DateUtils.getNowDate());
                    industry.setUpdateTime(DateUtils.getNowDate());
                    industryList.add(industry);
                }
            }

            if (!industryList.isEmpty())
            {
                // 更新企业行业关联
                com.ruoyi.miniapp.domain.MiniEnterprise enterprise = new com.ruoyi.miniapp.domain.MiniEnterprise();
                enterprise.setEnterpriseId(enterpriseId);
                enterprise.setIndustryList(industryList);
                miniEnterpriseService.updateEnterpriseIndustry(enterprise);
            }
        }
        catch (Exception e)
        {
            logger.error("处理企业行业关联失败", e);
        }
    }

    /**
     * 更新用户资料信息（包含企业ID）
     */
    private void updateUserProfile(SysUser user, UserProfileUpdateRequest profileRequest, Long enterpriseId)
    {
        // 更新基本信息
        if (StringUtils.isNotEmpty(profileRequest.getRealName()))
        {
            user.setRealName(profileRequest.getRealName());
        }
        if (StringUtils.isNotEmpty(profileRequest.getSex()))
        {
            user.setSex(profileRequest.getSex());
        }
        if (StringUtils.isNotEmpty(profileRequest.getNickName()))
        {
            user.setNickName(profileRequest.getNickName());
        }
        if (StringUtils.isNotEmpty(profileRequest.getAvatar()))
        {
            user.setAvatar(profileRequest.getAvatar());
        }
        if (StringUtils.isNotEmpty(profileRequest.getPhonenumber()))
        {
            user.setPhonenumber(profileRequest.getPhonenumber());
        }
        if (StringUtils.isNotEmpty(profileRequest.getPortraitUrl()))
        {
            user.setPortraitUrl(profileRequest.getPortraitUrl());
        }
         if (StringUtils.isNotEmpty(profileRequest.getPersonalIntroduction()))
         {
             user.setPersonalIntroduction(profileRequest.getPersonalIntroduction());
         }

        // 更新个人信息
        if (StringUtils.isNotEmpty(profileRequest.getBirthDate()))
        {
            try
            {
                user.setBirthDate(DateUtils.parseDate(profileRequest.getBirthDate()));
            }
            catch (Exception e)
            {
                logger.warn("解析出生日期失败: {}", profileRequest.getBirthDate());
            }
        }
        if (StringUtils.isNotEmpty(profileRequest.getRegion()))
        {
            user.setRegion(profileRequest.getRegion());
        }

        // 更新教育背景
        if (StringUtils.isNotEmpty(profileRequest.getGraduateSchool()))
        {
            user.setGraduateSchool(profileRequest.getGraduateSchool());
        }
        if (StringUtils.isNotEmpty(profileRequest.getGraduationYear()))
        {
            user.setGraduationYear(profileRequest.getGraduationYear());
        }
        if (StringUtils.isNotEmpty(profileRequest.getMajor()))
        {
            user.setMajor(profileRequest.getMajor());
        }
        if (StringUtils.isNotEmpty(profileRequest.getCollege()))
        {
            user.setCollege(profileRequest.getCollege());
        }

        // 更新职业信息
        if (StringUtils.isNotEmpty(profileRequest.getCurrentCompany()))
        {
            user.setCurrentCompany(profileRequest.getCurrentCompany());
        }
        if (StringUtils.isNotEmpty(profileRequest.getIndustryField()))
        {
            user.setIndustryField(profileRequest.getIndustryField());
        }
        if (StringUtils.isNotEmpty(profileRequest.getPositionTitle()))
        {
            user.setPositionTitle(profileRequest.getPositionTitle());
        }


//         更新资料完成度
         if (profileRequest.getProfileCompletionRate() != null)
         {
             user.setProfileCompletionRate(profileRequest.getProfileCompletionRate());
         }
    }

    /**
     * 判断用户是否已完善基础信息
     */
    private boolean hasBasicInfo(SysUser user)
    {
        return StringUtils.isNotEmpty(user.getRealName()) &&
               StringUtils.isNotEmpty(user.getSex()) &&
               user.getBirthDate() != null &&
               StringUtils.isNotEmpty(user.getRegion()) &&
               StringUtils.isNotEmpty(user.getPhonenumber()) &&
               StringUtils.isNotEmpty(user.getPortraitUrl());
    }

    /**
     * 判断用户是否已完善教育背景
     */
    private boolean hasEducationInfo(SysUser user)
    {
        return StringUtils.isNotEmpty(user.getGraduateSchool()) &&
               StringUtils.isNotEmpty(user.getGraduationYear()) &&
               StringUtils.isNotEmpty(user.getMajor()) &&
               StringUtils.isNotEmpty(user.getCollege());
    }

    /**
     * 判断用户是否已完善职业信息
     */
    private boolean hasCareerInfo(SysUser user)
    {
        return StringUtils.isNotEmpty(user.getCurrentCompany()) &&
               StringUtils.isNotEmpty(user.getIndustryField()) &&
               StringUtils.isNotEmpty(user.getPositionTitle());
    }

    /**
     * 计算并奖励积分
     */
    private int calculateAndAwardPoints(SysUser user, boolean hadBasicInfo, boolean hadEducationInfo, boolean hadCareerInfo)
    {
        int pointsAwarded = 0;
        int currentPoints = user.getTotalPoints() != null ? user.getTotalPoints() : 0;

        // 基础信息完善奖励50积分
        if (!hadBasicInfo && hasBasicInfo(user))
        {
            pointsAwarded += 50;
            logger.info("用户 {} 完善基础信息，奖励50积分", user.getUserName());
        }

        // 教育背景完善奖励50积分
        if (!hadEducationInfo && hasEducationInfo(user))
        {
            pointsAwarded += 50;
            logger.info("用户 {} 完善教育背景，奖励50积分", user.getUserName());
        }

        // 职业信息完善奖励100积分
        if (!hadCareerInfo && hasCareerInfo(user))
        {
            pointsAwarded += 100;
            logger.info("用户 {} 完善职业信息，奖励100积分", user.getUserName());
        }

        // 更新用户总积分
        if (pointsAwarded > 0)
        {
            user.setTotalPoints(currentPoints + pointsAwarded);
            logger.info("用户 {} 本次获得 {} 积分，总积分: {}", user.getUserName(), pointsAwarded, user.getTotalPoints());
        }

        return pointsAwarded;
    }

    /**
     * 构建用户资料详情
     */
    private Map<String, Object> buildProfileDetail(SysUser user)
    {
        Map<String, Object> profileDetail = new HashMap<>();

        // 基础信息
        Map<String, Object> basicInfo = new HashMap<>();
        basicInfo.put("realName", user.getRealName());
        basicInfo.put("sex", user.getSex());
        basicInfo.put("birthDate", user.getBirthDate() != null ? DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, user.getBirthDate()) : null);
        basicInfo.put("region", user.getRegion());
        basicInfo.put("phonenumber", user.getPhonenumber());
        basicInfo.put("portraitUrl", user.getPortraitUrl());
        basicInfo.put("isComplete", hasBasicInfo(user));
        profileDetail.put("basicInfo", basicInfo);

        // 教育背景
        Map<String, Object> educationInfo = new HashMap<>();
        educationInfo.put("graduateSchool", user.getGraduateSchool());
        educationInfo.put("graduationYear", user.getGraduationYear());
        educationInfo.put("major", user.getMajor());
        educationInfo.put("college", user.getCollege());
        educationInfo.put("isComplete", hasEducationInfo(user));
        profileDetail.put("educationInfo", educationInfo);

        // 职业信息
        Map<String, Object> careerInfo = new HashMap<>();
        careerInfo.put("currentCompany", user.getCurrentCompany());
        careerInfo.put("industryField", user.getIndustryField());
        careerInfo.put("positionTitle", user.getPositionTitle());
        careerInfo.put("isComplete", hasCareerInfo(user));

        // 解析行业标签
        if (StringUtils.isNotEmpty(user.getIndustryField()))
        {
            List<Map<String, Object>> industryTags = parseIndustryTags(user.getIndustryField());
            careerInfo.put("industryTags", industryTags);
        }
        else
        {
            careerInfo.put("industryTags", new ArrayList<>());
        }

        profileDetail.put("careerInfo", careerInfo);

        // 用户基本信息
        profileDetail.put("userId", user.getUserId());
        profileDetail.put("userName", user.getUserName());
        profileDetail.put("createTime", user.getCreateTime());
        profileDetail.put("updateTime", user.getUpdateTime());
        profileDetail.put("personalIntroduction",user.getPersonalIntroduction());

        profileDetail.put("totalPoints", user.getTotalPoints());
        profileDetail.put("profileCompletionRate", user.getProfileCompletionRate());

        return profileDetail;
    }

    /**
     * 解析行业标签
     */
    private List<Map<String, Object>> parseIndustryTags(String industryField)
    {
        List<Map<String, Object>> industryTags = new ArrayList<>();

        if (StringUtils.isEmpty(industryField))
        {
            return industryTags;
        }

        try
        {
            String[] industryIds = industryField.split(",");
            for (String industryIdStr : industryIds)
            {
                if (StringUtils.isNotEmpty(industryIdStr.trim()))
                {
                    Long industryId = Long.parseLong(industryIdStr.trim());

                    // 查询行业信息
                    com.ruoyi.miniapp.domain.MiniIndustryTree industry = miniIndustryTreeService.selectMiniIndustryTreeById(industryId);
                    if (industry != null)
                    {
                        Map<String, Object> industryTag = new HashMap<>();
                        industryTag.put("id", industry.getId());
                        industryTag.put("nodeName", industry.getNodeName());
                        industryTag.put("nodeType", industry.getNodeType());
                        industryTag.put("nodeLevel", industry.getNodeLevel());
                        industryTag.put("streamType", industry.getStreamType());
                        
                        // 获取一级节点信息
                        com.ruoyi.miniapp.domain.MiniIndustryTree rootNode = findRootNode(industry);
                        if (rootNode != null) {
                            Map<String, Object> rootInfo = new HashMap<>();
                            rootInfo.put("id", rootNode.getId());
                            rootInfo.put("nodeName", rootNode.getNodeName());
                            rootInfo.put("nodeType", rootNode.getNodeType());
                            industryTag.put("rootNode", rootInfo);
                        }
                        
                        industryTags.add(industryTag);
                    }
                }
            }
        }
        catch (Exception e)
        {
            logger.error("解析行业标签失败", e);
        }

        return industryTags;
    }

    /**
     * 查找根节点（一级节点）
     */
    private com.ruoyi.miniapp.domain.MiniIndustryTree findRootNode(com.ruoyi.miniapp.domain.MiniIndustryTree node) {
        if (node == null) {
            return null;
        }
        
        // 如果已经是一级节点，直接返回
        if (node.getNodeLevel() == 1) {
            return node;
        }
        
        // 递归向上查找
        if (node.getParentId() != null && node.getParentId() > 0) {
            com.ruoyi.miniapp.domain.MiniIndustryTree parentNode = miniIndustryTreeService.selectMiniIndustryTreeById(node.getParentId());
            return findRootNode(parentNode);
        }
        
        return null;
    }

    /**
     * 构建当前用户完整信息（用于已登录用户）
     */
    private Map<String, Object> buildCurrentUserInfo(SysUser user)
    {
        Map<String, Object> userInfo = new HashMap<>();
        userInfo.put("userId", user.getUserId());
        userInfo.put("userName", user.getUserName());
        userInfo.put("nickName", user.getNickName());
        userInfo.put("weixinNickname", user.getWeixinNickname());
        userInfo.put("avatar", user.getAvatar());
        userInfo.put("weixinAvatar", user.getWeixinAvatar());
        userInfo.put("realName", user.getRealName());
        userInfo.put("phonenumber", user.getPhonenumber());
        userInfo.put("sex", user.getSex());
        userInfo.put("totalPoints", user.getTotalPoints() != null ? user.getTotalPoints() : 0);
        userInfo.put("email", user.getEmail());
        userInfo.put("birthDate", user.getBirthDate());
        userInfo.put("region", user.getRegion());
        userInfo.put("graduateSchool", user.getGraduateSchool());
        userInfo.put("graduationYear", user.getGraduationYear());
        userInfo.put("major", user.getMajor());
        userInfo.put("college", user.getCollege());
        userInfo.put("currentCompany", user.getCurrentCompany());
        userInfo.put("industryField", user.getIndustryField());
        userInfo.put("positionTitle", user.getPositionTitle());
        userInfo.put("profileCompletionRate", user.getProfileCompletionRate());
        return userInfo;
    }

    // ================================ 用户关注相关接口 ================================

    /**
     * 关注用户
     */
    @ApiOperation("关注用户")
    @PostMapping("/follow/{followedId}")
    public AjaxResult followUser(@ApiParam("被关注用户ID") @PathVariable("followedId") Long followedId)
    {
        try
        {
            // 获取当前登录用户
            LoginUser loginUser = SecurityUtils.getLoginUser();
            Long followerId = loginUser.getUserId();

            // 执行关注操作
            boolean result = miniUserFollowService.followUser(followerId, followedId);

            if (result)
            {
                return AjaxResult.success("关注成功");
            }
            else
            {
                return error("关注失败");
            }
        }
        catch (Exception e)
        {
            logger.error("关注用户失败", e);
            return error("关注失败：" + e.getMessage());
        }
    }

    /**
     * 取消关注用户
     */
    @ApiOperation("取消关注用户")
    @DeleteMapping("/unfollow/{followedId}")
    public AjaxResult unfollowUser(@ApiParam("被关注用户ID") @PathVariable("followedId") Long followedId)
    {
        try
        {
            // 获取当前登录用户
            LoginUser loginUser = SecurityUtils.getLoginUser();
            Long followerId = loginUser.getUserId();

            // 执行取消关注操作
            boolean result = miniUserFollowService.unfollowUser(followerId, followedId);

            if (result)
            {
                return AjaxResult.success("取消关注成功");
            }
            else
            {
                return error("取消关注失败");
            }
        }
        catch (Exception e)
        {
            logger.error("取消关注用户失败", e);
            return error("取消关注失败：" + e.getMessage());
        }
    }

    /**
     * 获取我的关注列表
     */
    @ApiOperation("获取我的关注列表")
    @GetMapping("/getMyFollowingList")
    public AjaxResult getMyFollowingList()
    {
        try
        {
            // 获取当前登录用户
            LoginUser loginUser = SecurityUtils.getLoginUser();
            Long followerId = loginUser.getUserId();

            // 获取关注列表
            List<com.ruoyi.miniapp.domain.MiniUserFollow> followingList = miniUserFollowService.getMyFollowingList(followerId);

            // 构建响应数据
            Map<String, Object> result = new HashMap<>();
            result.put("followingList", followingList);
            result.put("followingCount", followingList.size());

            return AjaxResult.success("获取成功", result);
        }
        catch (Exception e)
        {
            logger.error("获取关注列表失败", e);
            return error("获取关注列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取我的粉丝列表
     */
    @ApiOperation("获取我的粉丝列表")
    @GetMapping("/getMyFollowersList")
    public AjaxResult getMyFollowersList()
    {
        try
        {
            // 获取当前登录用户
            LoginUser loginUser = SecurityUtils.getLoginUser();
            Long followedId = loginUser.getUserId();

            // 获取粉丝列表
            List<com.ruoyi.miniapp.domain.MiniUserFollow> followersList = miniUserFollowService.getMyFollowersList(followedId);

            // 构建响应数据
            Map<String, Object> result = new HashMap<>();
            result.put("followersList", followersList);
            result.put("followersCount", followersList.size());

            return AjaxResult.success("获取成功", result);
        }
        catch (Exception e)
        {
            logger.error("获取粉丝列表失败", e);
            return error("获取粉丝列表失败：" + e.getMessage());
        }
    }

    /**
     * 检查是否已关注某用户
     */
    @ApiOperation("检查是否已关注某用户")
    @GetMapping("/checkFollowStatus/{followedId}")
    public AjaxResult checkFollowStatus(@ApiParam("被关注用户ID") @PathVariable("followedId") Long followedId)
    {
        try
        {
            // 获取当前登录用户
            LoginUser loginUser = SecurityUtils.getLoginUser();
            Long followerId = loginUser.getUserId();

            // 检查关注状态
            boolean isFollowing = miniUserFollowService.isFollowing(followerId, followedId);
            boolean isMutualFollow = miniUserFollowService.isMutualFollow(followerId, followedId);

            // 构建响应数据
            Map<String, Object> result = new HashMap<>();
            result.put("isFollowing", isFollowing);
            result.put("isMutualFollow", isMutualFollow);

            return AjaxResult.success("获取成功", result);
        }
        catch (Exception e)
        {
            logger.error("检查关注状态失败", e);
            return error("检查关注状态失败：" + e.getMessage());
        }
    }

    /**
     * 获取用户关注统计信息
     */
    @ApiOperation("获取用户关注统计信息")
    @GetMapping("/getFollowStats/{userId}")
    public AjaxResult getFollowStats(@ApiParam("用户ID") @PathVariable("userId") Long userId)
    {
        try
        {
            // 获取统计信息
            int followingCount = miniUserFollowService.getFollowingCount(userId);
            int followersCount = miniUserFollowService.getFollowersCount(userId);

            // 构建响应数据
            Map<String, Object> result = new HashMap<>();
            result.put("followingCount", followingCount);
            result.put("followersCount", followersCount);

            return AjaxResult.success("获取成功", result);
        }
        catch (Exception e)
        {
            logger.error("获取关注统计失败", e);
            return error("获取关注统计失败：" + e.getMessage());
        }
    }

    // ================================ 登录登出相关接口 ================================

    /**
     * 退出登录
     * 清除当前用户的token，但保留用户账号
     */
    @ApiOperation("退出登录")
    @PostMapping("/logout")
    public AjaxResult logout()
    {
        try
        {
            // 获取当前登录用户
            LoginUser loginUser = SecurityUtils.getLoginUser();
            if (loginUser == null)
            {
                return AjaxResult.success("退出成功");
            }

            // 清除当前token
            tokenService.delLoginUser(loginUser.getToken());

            logger.info("用户 {} 退出登录成功", loginUser.getUsername());
            return AjaxResult.success("退出登录成功");
        }
        catch (Exception e)
        {
            logger.error("退出登录失败", e);
            return error("退出登录失败：" + e.getMessage());
        }
    }

    /**
     * 注销账号
     * 清除用户的所有token并停用账号
     */
    @ApiOperation("注销账号")
    @PostMapping("/deleteAccount")
    public AjaxResult deleteAccount(@ApiParam("注销确认") @RequestBody Map<String, String> request)
    {
        try
        {
            // 获取当前登录用户
            LoginUser loginUser = SecurityUtils.getLoginUser();
            if (loginUser == null)
            {
                return error("用户未登录");
            }

            SysUser currentUser = loginUser.getUser();

            // 检查是否为admin用户
            if (hasAdminRole(currentUser))
            {
                return error("admin用户不允许注销账号");
            }

            // 验证确认参数（可选的安全验证）
            String confirmText = request.get("confirmText");
            if (!"确认注销".equals(confirmText))
            {
                return error("请输入正确的确认文字：确认注销");
            }

            // 清除该用户的所有token
            tokenService.clearUserTokens(currentUser.getUserId());

            // 停用用户账号（设置为停用状态，而不是物理删除）
            SysUser updateUser = new SysUser();
            updateUser.setUserId(currentUser.getUserId());
            updateUser.setStatus("1"); // 停用状态
            updateUser.setUpdateBy("system");
            updateUser.setUpdateTime(DateUtils.getNowDate());
            updateUser.setRemark("用户主动注销账号");

            int result = userService.updateUserStatus(updateUser);

            if (result > 0)
            {
                logger.info("用户 {} (ID: {}) 注销账号成功", currentUser.getUserName(), currentUser.getUserId());
                return AjaxResult.success("账号注销成功");
            }
            else
            {
                return error("账号注销失败");
            }
        }
        catch (Exception e)
        {
            logger.error("注销账号失败", e);
            return error("注销账号失败：" + e.getMessage());
        }
    }

    /**
     * 强制退出所有设备
     * 清除用户在所有设备上的登录状态
     */
    @ApiOperation("强制退出所有设备")
    @PostMapping("/logoutAllDevices")
    public AjaxResult logoutAllDevices()
    {
        try
        {
            // 获取当前登录用户
            LoginUser loginUser = SecurityUtils.getLoginUser();
            if (loginUser == null)
            {
                return error("用户未登录");
            }

            // 清除该用户的所有token
            tokenService.clearUserTokens(loginUser.getUserId());

            logger.info("用户 {} 强制退出所有设备成功", loginUser.getUsername());
            return AjaxResult.success("已强制退出所有设备");
        }
        catch (Exception e)
        {
            logger.error("强制退出所有设备失败", e);
            return error("强制退出所有设备失败：" + e.getMessage());
        }
    }

//    /**
//     * 构建基本用户信息（不包含敏感信息，用于未登录状态的预览）
//     */
//    private Map<String, Object> buildBasicUserInfo(SysUser user)
//    {
//        Map<String, Object> userInfo = new HashMap<>();
//        userInfo.put("userId", user.getUserId());
//        userInfo.put("nickName", user.getNickName());
//        userInfo.put("avatar", user.getAvatar());
//        userInfo.put("weixinNickname", user.getWeixinNickname());
//        userInfo.put("weixinAvatar", user.getWeixinAvatar());
//        userInfo.put("sex", user.getSex());
//        userInfo.put("totalPoints", user.getTotalPoints() != null ? user.getTotalPoints() : 0);
//        return userInfo;
//    }
}

