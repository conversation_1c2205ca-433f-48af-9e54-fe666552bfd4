<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="活动ID" prop="activityId">
        <el-input
          v-model="queryParams.activityId"
          placeholder="请输入活动ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="用户ID" prop="userId">
        <el-input
          v-model="queryParams.userId"
          placeholder="请输入用户ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="审核状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择审核状态" clearable>
          <el-option label="待审核" value="0" />
          <el-option label="通过" value="1" />
          <el-option label="拒绝" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['miniapp:xiqing:registration:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['miniapp:xiqing:registration:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="registrationManageList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="报名ID" align="center" prop="registrationId" width="80" />
      <el-table-column label="活动标题" align="center" prop="activityTitle" :show-overflow-tooltip="true" />
      <el-table-column label="用户ID" align="center" prop="userId" width="80" />
      <el-table-column label="报名时间" align="center" prop="registrationTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.registrationTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="审核状态" align="center" prop="status" width="100">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status === '0'" type="warning">待审核</el-tag>
          <el-tag v-else-if="scope.row.status === '1'" type="success">通过</el-tag>
          <el-tag v-else-if="scope.row.status === '2'" type="danger">拒绝</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="审核人" align="center" prop="auditBy" width="100" />
      <el-table-column label="审核时间" align="center" prop="auditTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.auditTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['miniapp:xiqing:registration:query']"
          >查看</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-check"
            @click="handleAudit(scope.row)"
            v-hasPermi="['miniapp:xiqing:registration:audit']"
            v-if="scope.row.status === '0'"
          >审核</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['miniapp:xiqing:registration:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 查看报名详情对话框 -->
    <el-dialog title="报名详情" :visible.sync="viewOpen" width="800px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="报名ID">{{ form.registrationId }}</el-descriptions-item>
        <el-descriptions-item label="活动标题">{{ form.activityTitle }}</el-descriptions-item>
        <el-descriptions-item label="用户ID">{{ form.userId }}</el-descriptions-item>
        <el-descriptions-item label="报名时间">{{ parseTime(form.registrationTime) }}</el-descriptions-item>
        <el-descriptions-item label="审核状态">
          <el-tag v-if="form.status === '0'" type="warning">待审核</el-tag>
          <el-tag v-else-if="form.status === '1'" type="success">通过</el-tag>
          <el-tag v-else-if="form.status === '2'" type="danger">拒绝</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="审核人">{{ form.auditBy }}</el-descriptions-item>
        <el-descriptions-item label="审核时间">{{ parseTime(form.auditTime) }}</el-descriptions-item>
        <el-descriptions-item label="审核备注" :span="2">{{ form.auditRemark }}</el-descriptions-item>
      </el-descriptions>
      
      <div style="margin-top: 20px;">
        <h4>报名表单数据：</h4>
        <el-table :data="formDataList" border style="margin-top: 10px;">
          <el-table-column prop="key" label="字段名" width="200" />
          <el-table-column label="字段值">
            <template slot-scope="scope">
              <div v-if="scope.row.type === 'textarea'" class="textarea-content">
                {{ scope.row.value }}
              </div>
              <el-tag v-else-if="scope.row.type === 'radio' || scope.row.type === 'picker' || scope.row.type === 'select'"
                      type="primary" size="small">
                {{ scope.row.value }}
              </el-tag>
              <span v-else-if="scope.row.type === 'tel' || scope.row.type === 'phone'"
                    class="phone-number">
                {{ scope.row.value }}
              </span>
              <el-tag v-else-if="scope.row.type === 'date'" type="info" size="small">
                {{ scope.row.value }}
              </el-tag>
              <div v-else-if="scope.row.type === 'checkbox'" class="checkbox-content">
                {{ scope.row.value }}
              </div>
              <span v-else>{{ scope.row.value }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="viewOpen = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 审核对话框 -->
    <el-dialog title="审核报名" :visible.sync="auditOpen" width="500px" append-to-body>
      <el-form ref="auditForm" :model="auditForm" label-width="100px">
        <el-form-item label="审核结果" prop="status">
          <el-radio-group v-model="auditForm.status">
            <el-radio label="1">通过</el-radio>
            <el-radio label="2">拒绝</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审核备注" prop="auditRemark">
          <el-input v-model="auditForm.auditRemark" type="textarea" placeholder="请输入审核备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitAudit">确 定</el-button>
        <el-button @click="auditOpen = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listRegistrationManage, getRegistrationManage, delRegistrationManage, exportRegistrationManage, auditRegistrationManage } from "@/api/miniapp/xiqing/registration-manage";
import { getActivityConfig } from "@/api/miniapp/xiqing/activity-config";

export default {
  name: "XiqingRegistrationManage",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 西青金种子路演报名管理表格数据
      registrationManageList: [],
      // 是否显示查看弹出层
      viewOpen: false,
      // 是否显示审核弹出层
      auditOpen: false,
      // 表单数据列表
      formDataList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        activityId: null,
        userId: null,
        status: null
      },
      // 表单参数
      form: {},
      // 审核表单参数
      auditForm: {}
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询西青金种子路演报名管理列表 */
    getList() {
      this.loading = true;
      listRegistrationManage(this.queryParams).then(response => {
        this.registrationManageList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.registrationId)
      this.multiple = !selection.length
    },
    /** 查看按钮操作 */
    handleView(row) {
      const registrationId = row.registrationId;
      getRegistrationManage(registrationId).then(async response => {
        this.form = response.data;
        await this.parseFormData();
        this.viewOpen = true;
      });
    },
    /** 解析表单数据 */
    async parseFormData() {
      this.formDataList = [];
      if (this.form.formData) {
        try {
          const data = JSON.parse(this.form.formData);

          // 检查数据格式
          if (Array.isArray(data)) {
            // 新格式：数组格式，每个元素包含name、type、label、value等属性
            data.forEach(field => {
              if (field.name && field.value !== undefined && field.value !== null && field.value !== '') {
                this.formDataList.push({
                  key: field.label || field.name, // 优先使用label，没有则使用name
                  value: this.formatFieldValue(field.value, field.type),
                  type: field.type
                });
              }
            });
          } else if (typeof data === 'object') {
            // 旧格式：对象格式，key-value形式
            // 获取活动的表单配置来显示正确的字段标签
            const formConfig = await this.getActivityFormConfig();
            const fieldLabelMap = {};
            if (formConfig) {
              formConfig.forEach(field => {
                fieldLabelMap[field.name] = field.label;
              });
            }

            for (const key in data) {
              if (data[key] !== undefined && data[key] !== null && data[key] !== '') {
                this.formDataList.push({
                  key: fieldLabelMap[key] || key, // 优先使用中文标签，没有则使用原字段名
                  value: data[key],
                  type: 'text'
                });
              }
            }
          }
        } catch (e) {
          console.error('解析表单数据失败:', e);
        }
      }
    },
    /** 格式化字段值 */
    formatFieldValue(value, type) {
      if (value === undefined || value === null || value === '') {
        return '未填写';
      }

      switch (type) {
        case 'checkbox':
          // 复选框类型，value可能是数组
          if (Array.isArray(value)) {
            return value.length > 0 ? value.join(', ') : '未选择';
          }
          return value;
        case 'radio':
        case 'picker':
        case 'select':
          // 单选类型
          return value || '未选择';
        case 'textarea':
          // 文本域类型，保持换行
          return value;
        case 'date':
          // 日期类型
          return value || '未选择';
        case 'tel':
        case 'phone':
          // 电话类型
          return value;
        default:
          // 默认文本类型
          return value;
      }
    },
    /** 获取活动表单配置 */
    async getActivityFormConfig() {
      if (!this.form.activityId) {
        return null;
      }
      try {
        const response = await getActivityConfig(this.form.activityId);
        if (response.data && response.data.formConfig) {
          return JSON.parse(response.data.formConfig);
        }
      } catch (e) {
        console.error('获取活动表单配置失败:', e);
      }
      return null;
    },
    /** 审核按钮操作 */
    handleAudit(row) {
      this.auditForm = {
        registrationId: row.registrationId,
        status: '1',
        auditRemark: ''
      };
      this.auditOpen = true;
    },
    /** 提交审核 */
    submitAudit() {
      auditRegistrationManage(this.auditForm).then(response => {
        this.$modal.msgSuccess("审核成功");
        this.auditOpen = false;
        this.getList();
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const registrationIds = row.registrationId || this.ids;
      this.$modal.confirm('是否确认删除报名编号为"' + registrationIds + '"的数据项？').then(function() {
        return delRegistrationManage(registrationIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('miniapp/xiqing/registration-manage/export', {
        ...this.queryParams
      }, `registration_manage_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>

<style scoped>
.textarea-content {
  white-space: pre-wrap;
  word-break: break-word;
  line-height: 1.5;
  max-height: 120px;
  overflow-y: auto;
  padding: 8px;
  background-color: #f5f7fa;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.phone-number {
  color: #409eff;
  font-weight: 500;
}

.checkbox-content {
  color: #606266;
  line-height: 1.5;
}
</style>
