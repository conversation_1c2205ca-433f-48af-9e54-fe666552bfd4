(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-3adad45c"],{"26dc":function(e,t,i){"use strict";i.d(t,"a",(function(){return a})),i.d(t,"b",(function(){return s}));var l=i("b775");function a(e){return Object(l["a"])({url:"/miniapp/xiqing/activity-config/"+e,method:"get"})}function s(e){return Object(l["a"])({url:"/miniapp/xiqing/activity-config",method:"put",data:e})}},"35d0":function(e,t,i){"use strict";i("87bd")},"87bd":function(e,t,i){},ceb6:function(e,t,i){"use strict";i.r(t);var l=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"app-container"},[i("div",{staticClass:"form-config-container"},[i("div",{staticClass:"config-header"},[i("h3",[e._v("路演活动报名表单配置")]),i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:xiqing:activity:edit"],expression:"['miniapp:xiqing:activity:edit']"}],attrs:{type:"primary",icon:"el-icon-setting"},on:{click:e.handleFormConfig}},[e._v("配置表单")])],1),i("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"config-content"},[e.formFields.length>0?i("div",{staticClass:"form-preview"},[i("h4",[e._v("当前表单字段预览：")]),i("div",{staticClass:"field-list"},e._l(e.formFields,(function(t,l){return i("div",{key:l,staticClass:"field-item"},[i("div",{staticClass:"field-info"},[i("i",{staticClass:"field-icon",class:e.getFieldIcon(t.type)}),i("span",{staticClass:"field-label"},[e._v(e._s(t.label))]),t.required?i("el-tag",{attrs:{size:"mini",type:"danger"}},[e._v("必填")]):i("el-tag",{attrs:{size:"mini",type:"info"}},[e._v("选填")]),i("span",{staticClass:"field-type"},[e._v(e._s(e.getFieldTypeName(t.type)))])],1)])})),0)]):i("div",{staticClass:"empty-form"},[i("i",{staticClass:"el-icon-document-add"}),i("p",[e._v('暂未配置表单字段，点击"配置表单"开始设置')])])])]),i("el-dialog",{attrs:{title:"报名表单配置",visible:e.formConfigOpen,width:"1000px","append-to-body":""},on:{"update:visible":function(t){e.formConfigOpen=t}}},[i("div",{staticClass:"form-fields-config"},[i("div",{staticClass:"form-fields-toolbar"},[i("div",{staticClass:"toolbar-left"},[i("el-button",{attrs:{type:"primary",size:"small",icon:"el-icon-plus"},on:{click:e.addFormField}},[e._v(" 添加字段 ")]),i("el-dropdown",{attrs:{size:"small"},on:{command:e.handleTemplateCommand}},[i("el-button",{attrs:{size:"small"}},[e._v(" 预设模板"),i("i",{staticClass:"el-icon-arrow-down el-icon--right"})]),i("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[i("el-dropdown-item",{attrs:{command:"basic"}},[e._v("基础信息模板")]),i("el-dropdown-item",{attrs:{command:"roadshow"}},[e._v("路演报名模板")]),i("el-dropdown-item",{attrs:{command:"clear"}},[e._v("清空所有字段")])],1)],1)],1),i("div",{staticClass:"toolbar-right"},[i("el-button",{attrs:{size:"small",icon:"el-icon-view"},on:{click:e.previewForm}},[e._v(" 预览表单 ")]),i("el-button",{attrs:{type:"success",size:"small",icon:"el-icon-check"},on:{click:e.saveFormConfig}},[e._v(" 保存配置 ")])],1)]),e.formFields.length>0?i("div",{staticClass:"form-fields-list"},e._l(e.formFields,(function(t,l){return i("div",{key:l,staticClass:"form-field-item"},[i("div",{staticClass:"field-header"},[i("div",{staticClass:"field-info"},[i("i",{staticClass:"field-icon",class:e.getFieldIcon(t.type)}),i("span",{staticClass:"field-label"},[e._v(e._s(t.label||"未命名字段"))]),t.required?i("el-tag",{attrs:{size:"mini",type:"danger"}},[e._v("必填")]):i("el-tag",{attrs:{size:"mini",type:"info"}},[e._v("选填")])],1),i("div",{staticClass:"field-actions"},[i("el-button",{attrs:{type:"text",size:"mini",disabled:0===l,icon:"el-icon-arrow-up"},on:{click:function(t){return e.moveField(l,-1)}}}),i("el-button",{attrs:{type:"text",size:"mini",disabled:l===e.formFields.length-1,icon:"el-icon-arrow-down"},on:{click:function(t){return e.moveField(l,1)}}}),i("el-button",{staticClass:"danger-btn",attrs:{type:"text",size:"mini",icon:"el-icon-delete"},on:{click:function(t){return e.removeFormField(l)}}})],1)]),i("div",{staticClass:"field-content"},[i("el-row",{attrs:{gutter:10}},[i("el-col",{attrs:{span:12}},[i("div",{staticClass:"field-item"},[i("label",[e._v("字段标签")]),i("el-input",{attrs:{placeholder:"显示给用户的标签，如：姓名、电话等",size:"small"},on:{input:function(i){return e.updateFieldName(t,i)}},model:{value:t.label,callback:function(i){e.$set(t,"label",i)},expression:"field.label"}})],1)]),i("el-col",{attrs:{span:12}},[i("div",{staticClass:"field-item"},[i("label",[e._v("字段类型")]),i("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"选择类型",size:"small"},model:{value:t.type,callback:function(i){e.$set(t,"type",i)},expression:"field.type"}},[i("el-option",{attrs:{label:"📝 文本输入",value:"input"}}),i("el-option",{attrs:{label:"📄 多行文本",value:"textarea"}}),i("el-option",{attrs:{label:"🔢 数字输入",value:"number"}}),i("el-option",{attrs:{label:"📧 邮箱",value:"email"}}),i("el-option",{attrs:{label:"📞 电话",value:"tel"}}),i("el-option",{attrs:{label:"🔘 单选",value:"radio"}}),i("el-option",{attrs:{label:"☑️ 多选",value:"checkbox"}}),i("el-option",{attrs:{label:"📋 下拉选择",value:"select"}}),i("el-option",{attrs:{label:"🔘➕ 单选+其他",value:"radio_other"}}),i("el-option",{attrs:{label:"☑️➕ 多选+其他",value:"checkbox_other"}}),i("el-option",{attrs:{label:"📋➕ 下拉+其他",value:"select_other"}}),i("el-option",{attrs:{label:"📅 日期",value:"date"}}),i("el-option",{attrs:{label:"📎 文件上传",value:"file"}})],1)],1)])],1),i("el-row",{staticStyle:{"margin-top":"10px"},attrs:{gutter:10}},[i("el-col",{attrs:{span:4}},[i("div",{staticClass:"field-item"},[i("label",[e._v("是否必填")]),i("el-switch",{model:{value:t.required,callback:function(i){e.$set(t,"required",i)},expression:"field.required"}})],1)]),["radio","checkbox","select","radio_other","checkbox_other","select_other"].includes(t.type)?i("el-col",{attrs:{span:20}},[i("div",{staticClass:"field-item"},[i("label",[e._v("选项配置")]),i("el-input",{attrs:{placeholder:"用逗号分隔选项，如：选项1,选项2,选项3",size:"small"},model:{value:t.options,callback:function(i){e.$set(t,"options",i)},expression:"field.options"}}),t.options?i("div",{staticClass:"options-preview"},e._l(t.options.split(","),(function(t,l){return i("el-tag",{key:l,staticStyle:{"margin-right":"5px","margin-top":"5px"},attrs:{size:"mini"}},[e._v(" "+e._s(t.trim())+" ")])})),1):e._e()],1)]):e._e()],1)],1)])})),0):i("div",{staticClass:"empty-state"},[i("i",{staticClass:"el-icon-document-add"}),i("p",[e._v('暂无表单字段，点击"添加字段"开始配置')])])]),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{on:{click:function(t){e.formConfigOpen=!1}}},[e._v("关 闭")])],1)]),i("el-dialog",{attrs:{title:"表单预览",visible:e.previewDialogVisible,width:"600px","append-to-body":""},on:{"update:visible":function(t){e.previewDialogVisible=t}}},[i("div",{staticClass:"form-preview"},[i("div",{staticClass:"preview-header"},[i("h3",[e._v(e._s(e.currentActivity?e.currentActivity.activityTitle:"路演报名表"))]),i("p",[e._v("请填写以下信息完成报名")])]),i("div",{staticClass:"preview-form"},e._l(e.formFields,(function(t,l){return i("div",{key:l,staticClass:"preview-field"},[i("label",{staticClass:"preview-label"},[e._v(" "+e._s(t.label)+" "),t.required?i("span",{staticClass:"required"},[e._v("*")]):e._e()]),i("div",{staticClass:"preview-input"},["input"===t.type||"email"===t.type||"tel"===t.type?i("el-input",{attrs:{placeholder:"请输入"+t.label,size:"small",disabled:""}}):"textarea"===t.type?i("el-input",{attrs:{type:"textarea",placeholder:"请输入"+t.label,size:"small",disabled:""}}):"number"===t.type?i("el-input-number",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入"+t.label,size:"small",disabled:""}}):"radio"===t.type?i("el-radio-group",{attrs:{disabled:""}},e._l((t.options||"").split(","),(function(t,l){return i("el-radio",{key:l,attrs:{label:t.trim()}},[e._v(" "+e._s(t.trim())+" ")])})),1):"checkbox"===t.type?i("el-checkbox-group",{attrs:{disabled:""}},e._l((t.options||"").split(","),(function(t,l){return i("el-checkbox",{key:l,attrs:{label:t.trim()}},[e._v(" "+e._s(t.trim())+" ")])})),1):"select"===t.type?i("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择"+t.label,size:"small",disabled:""}},e._l((t.options||"").split(","),(function(e,t){return i("el-option",{key:t,attrs:{label:e.trim(),value:e.trim()}})})),1):"date"===t.type?i("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",placeholder:"请选择"+t.label,size:"small",disabled:""}}):"file"===t.type?i("el-upload",{staticClass:"upload-demo",attrs:{action:"#",disabled:!0,"show-file-list":!1}},[i("el-button",{attrs:{size:"small",type:"primary",disabled:""}},[i("i",{staticClass:"el-icon-upload"}),e._v(" 选择文件 ")]),i("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[e._v("支持上传PDF、Word、Excel等格式文件")])],1):"radio_other"===t.type?i("div",[i("el-radio-group",{attrs:{disabled:""}},[e._l(t.options.split(","),(function(t,l){return i("div",{key:l,staticStyle:{display:"block","margin-bottom":"8px"}},[i("el-radio",{attrs:{label:t.trim()}},[e._v(e._s(t.trim()))])],1)})),i("div",{staticStyle:{display:"block","margin-bottom":"8px"}},[i("el-radio",{attrs:{label:"其他"}},[e._v(" 其他 "),i("el-input",{staticStyle:{width:"200px","margin-left":"10px"},attrs:{placeholder:"请输入其他内容",size:"small",disabled:""}})],1)],1)],2)],1):"checkbox_other"===t.type?i("div",[i("el-checkbox-group",{attrs:{disabled:""}},[e._l(t.options.split(","),(function(t,l){return i("div",{key:l,staticStyle:{display:"block","margin-bottom":"8px"}},[i("el-checkbox",{attrs:{label:t.trim()}},[e._v(e._s(t.trim()))])],1)})),i("div",{staticStyle:{display:"block","margin-bottom":"8px"}},[i("el-checkbox",{attrs:{label:"其他"}},[e._v(" 其他 "),i("el-input",{staticStyle:{width:"200px","margin-left":"10px"},attrs:{placeholder:"请输入其他内容",size:"small",disabled:""}})],1)],1)],2)],1):"select_other"===t.type?i("div",[i("el-select",{staticStyle:{width:"100%","margin-bottom":"8px"},attrs:{disabled:"",placeholder:"请选择",size:"small"}},[e._l(t.options.split(","),(function(e,t){return i("el-option",{key:t,attrs:{label:e.trim(),value:e.trim()}})})),i("el-option",{attrs:{label:"其他",value:"其他"}})],2),i("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"选择'其他'时请在此输入具体内容",size:"small",disabled:""}})],1):e._e()],1)])})),0)]),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{on:{click:function(t){e.previewDialogVisible=!1}}},[e._v("关 闭")])],1)])],1)},a=[],s=(i("caad"),i("14d9"),i("a434"),i("b0c0"),i("e9c4"),i("b64b"),i("ac1f"),i("5319"),i("26dc")),o={name:"XiqingActivityConfig",data:function(){return{loading:!0,formConfigOpen:!1,previewDialogVisible:!1,formFields:[],activityId:1}},created:function(){this.loadFormConfig()},methods:{loadFormConfig:function(){var e=this;this.loading=!0,Object(s["a"])(this.activityId).then((function(t){if(t.data&&t.data.formConfig)try{e.formFields=JSON.parse(t.data.formConfig)}catch(i){e.formFields=[]}else e.formFields=[];e.loading=!1})).catch((function(){e.formFields=[],e.loading=!1}))},handleFormConfig:function(){this.formConfigOpen=!0},addFormField:function(){this.formFields.push({name:"",label:"",type:"input",required:!1,options:""})},removeFormField:function(e){this.formFields.splice(e,1)},moveField:function(e,t){var i=e+t;if(i>=0&&i<this.formFields.length){var l=this.formFields[e];this.$set(this.formFields,e,this.formFields[i]),this.$set(this.formFields,i,l)}},updateFieldName:function(e,t){e.name&&""!==e.name||(e.name=this.generateFieldName(t))},generateFieldName:function(e){var t={"姓名":"name","联系电话":"phone","电话":"phone","邮箱":"email","邮箱地址":"email","公司":"company","项目名称":"project_name","项目描述":"project_description","团队规模":"team_size"};return t[e]||e.toLowerCase().replace(/\s+/g,"_")},getFieldIcon:function(e){var t={input:"el-icon-edit",textarea:"el-icon-document",number:"el-icon-s-data",email:"el-icon-message",tel:"el-icon-phone",radio:"el-icon-success",checkbox:"el-icon-check",select:"el-icon-arrow-down",radio_other:"el-icon-circle-plus",checkbox_other:"el-icon-square-plus",select_other:"el-icon-plus",date:"el-icon-date",file:"el-icon-upload"};return t[e]||"el-icon-edit"},handleTemplateCommand:function(e){var t=this;if("clear"!==e){var i={basic:[{label:"姓名",name:"name",type:"input",required:!0,options:""},{label:"联系电话",name:"phone",type:"tel",required:!0,options:""},{label:"邮箱地址",name:"email",type:"email",required:!1,options:""}],roadshow:[{label:"姓名",name:"name",type:"input",required:!0,options:""},{label:"联系电话",name:"phone",type:"tel",required:!0,options:""},{label:"邮箱地址",name:"email",type:"email",required:!0,options:""},{label:"公司/团队",name:"company",type:"input",required:!0,options:""},{label:"项目名称",name:"project_name",type:"input",required:!0,options:""},{label:"项目来源",name:"project_source",type:"radio_other",required:!0,options:"社会,高校,科研院所,企业内部"},{label:"项目阶段",name:"project_stage",type:"radio",required:!0,options:"概念阶段,开发阶段,测试阶段,上线阶段"},{label:"团队规模",name:"team_size",type:"select",required:!0,options:"1人,2-3人,4-5人,6-10人,10人以上"},{label:"融资需求",name:"funding_needs",type:"input",required:!1,options:""},{label:"商业计划书",name:"business_plan",type:"file",required:!0,options:""},{label:"项目描述",name:"project_description",type:"textarea",required:!0,options:""}]};i[e]&&(this.formFields=i[e],this.$message.success("模板应用成功"))}else this.$confirm("确定要清空所有字段吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.formFields=[],t.$message.success("已清空所有字段")}))},previewForm:function(){0!==this.formFields.length?this.previewDialogVisible=!0:this.$message.warning("请先添加表单字段")},saveFormConfig:function(){var e=this;if(0!==this.formFields.length){for(var t=0;t<this.formFields.length;t++){var i=this.formFields[t];if(!i.label)return void this.$message.error("第".concat(t+1,"个字段的标签不能为空"));if(["radio","checkbox","select","radio_other","checkbox_other","select_other"].includes(i.type)&&!i.options)return void this.$message.error('字段"'.concat(i.label,'"需要配置选项'))}var l=JSON.stringify(this.formFields),a={activityId:this.activityId,formConfig:l};Object(s["b"])(a).then((function(t){e.$modal.msgSuccess("表单配置保存成功"),e.formConfigOpen=!1,e.loadFormConfig()}))}else this.$message.warning("请至少添加一个表单字段")},getFieldTypeName:function(e){var t={input:"文本输入",textarea:"多行文本",number:"数字输入",email:"邮箱",tel:"电话",radio:"单选",checkbox:"多选",select:"下拉选择",radio_other:"单选+其他",checkbox_other:"多选+其他",select_other:"下拉+其他",date:"日期",file:"文件上传"};return t[e]||"未知类型"}}},n=o,r=(i("35d0"),i("2877")),c=Object(r["a"])(n,l,a,!1,null,"11656995",null);t["default"]=c.exports}}]);