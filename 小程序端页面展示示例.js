// 小程序端页面展示示例

// 1. 在小程序中创建 join-us 页面

// join-us.js
Page({
  data: {
    pageContent: null,
    loading: true
  },
  onLoad: function() {
    this.getJoinUsContent();
  },
  getJoinUsContent: function() {
    wx.showLoading({
      title: '加载中',
    });
    wx.request({
      url: 'https://api.example.com/miniapp/content/page/app/getJoinUs',
      method: 'GET',
      success: (res) => {
        if (res.data.code === 200) {
          this.setData({
            pageContent: res.data.data,
            loading: false
          });
        } else {
          wx.showToast({
            title: '获取内容失败',
            icon: 'none'
          });
        }
      },
      fail: () => {
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  }
});

// 2. 在小程序中创建 join-us 页面的WXML

// join-us.wxml
/*
<view class="container">
  <view class="page-header">
    <text class="page-title">{{pageContent.pageTitle}}</text>
  </view>
  
  <view class="loading" wx:if="{{loading}}">
    <text>内容加载中...</text>
  </view>
  
  <view class="page-content" wx:else>
    <rich-text nodes="{{pageContent.pageContent}}"></rich-text>
  </view>
  
  <view class="contact-section">
    <text class="contact-title">联系我们</text>
    <button class="contact-btn" open-type="contact">在线咨询</button>
  </view>
</view>
*/

// 3. 在小程序中创建 join-us 页面的WXSS

// join-us.wxss
/*
.container {
  padding: 30rpx;
}

.page-header {
  margin-bottom: 30rpx;
  border-bottom: 1px solid #eee;
  padding-bottom: 20rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.loading {
  text-align: center;
  padding: 100rpx 0;
  color: #999;
}

.page-content {
  line-height: 1.6;
  color: #333;
}

.contact-section {
  margin-top: 60rpx;
  padding-top: 30rpx;
  border-top: 1px solid #eee;
  text-align: center;
}

.contact-title {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.contact-btn {
  width: 80%;
  background-color: #1aad19;
  color: #fff;
  font-size: 30rpx;
  padding: 15rpx 0;
  border-radius: 10rpx;
}
*/

// 4. 在小程序的app.json中添加页面配置

// app.json 片段
/*
{
  "pages": [
    "pages/index/index",
    "pages/join-us/join-us",
    "pages/about-us/about-us",
    "pages/user-agreement/user-agreement",
    "pages/privacy-policy/privacy-policy"
  ],
  "window": {
    "backgroundTextStyle": "light",
    "navigationBarBackgroundColor": "#fff",
    "navigationBarTitleText": "海棠杯创新创业创赛",
    "navigationBarTextStyle": "black"
  },
  "tabBar": {
    "color": "#999",
    "selectedColor": "#1aad19",
    "backgroundColor": "#fff",
    "borderStyle": "black",
    "list": [
      {
        "pagePath": "pages/index/index",
        "text": "首页",
        "iconPath": "images/home.png",
        "selectedIconPath": "images/home-active.png"
      },
      {
        "pagePath": "pages/join-us/join-us",
        "text": "加入我们",
        "iconPath": "images/join.png",
        "selectedIconPath": "images/join-active.png"
      }
    ]
  }
}
*/
