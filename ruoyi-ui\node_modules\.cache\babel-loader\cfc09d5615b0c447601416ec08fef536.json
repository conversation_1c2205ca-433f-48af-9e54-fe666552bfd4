{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\xiqing\\consultation\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\xiqing\\consultation\\index.vue", "mtime": 1753694616749}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\babel.config.js", "mtime": 1751427652172}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_consultation", "require", "name", "data", "loading", "open", "contactInfo", "form", "rules", "contactName", "required", "message", "trigger", "contactMethod", "created", "getContactInfo", "methods", "_this", "then", "response", "catch", "cancel", "reset", "consultationId", "status", "remark", "resetForm", "handleEdit", "submitForm", "_this2", "$refs", "validate", "valid", "updateConsultation", "$modal", "msgSuccess"], "sources": ["src/views/miniapp/xiqing/consultation/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <div class=\"contact-info-container\">\r\n      <div class=\"contact-header\">\r\n        <h3>报名咨询联系方式</h3>\r\n        <el-button\r\n          type=\"primary\"\r\n          icon=\"el-icon-edit\"\r\n          @click=\"handleEdit\"\r\n          v-hasPermi=\"['miniapp:xiqing:consultation:edit']\"\r\n        >编辑</el-button>\r\n      </div>\r\n\r\n      <div class=\"contact-content\" v-loading=\"loading\">\r\n        <div class=\"contact-item\">\r\n          <label>联系人：</label>\r\n          <span>{{ contactInfo.contactName || '暂未设置' }}</span>\r\n        </div>\r\n        <div class=\"contact-item\">\r\n          <label>联系方式：</label>\r\n          <span>{{ contactInfo.contactMethod || '暂未设置' }}</span>\r\n        </div>\r\n        <div class=\"contact-item\" v-if=\"contactInfo.remark\">\r\n          <label>备注：</label>\r\n          <span>{{ contactInfo.remark }}</span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 编辑联系信息对话框 -->\r\n    <el-dialog title=\"编辑联系信息\" :visible.sync=\"open\" width=\"500px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n        <el-form-item label=\"联系人\" prop=\"contactName\">\r\n          <el-input v-model=\"form.contactName\" placeholder=\"请输入联系人\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"联系方式\" prop=\"contactMethod\">\r\n          <el-input v-model=\"form.contactMethod\" placeholder=\"请输入联系方式\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\" prop=\"remark\">\r\n          <el-input v-model=\"form.remark\" type=\"textarea\" placeholder=\"请输入备注\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getContactInfo, updateConsultation } from \"@/api/miniapp/xiqing/consultation\";\r\n\r\nexport default {\r\n  name: \"XiqingConsultation\",\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 联系信息\r\n      contactInfo: {},\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        contactName: [\r\n          { required: true, message: \"联系人不能为空\", trigger: \"blur\" }\r\n        ],\r\n        contactMethod: [\r\n          { required: true, message: \"联系方式不能为空\", trigger: \"blur\" }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getContactInfo();\r\n  },\r\n  methods: {\r\n    /** 获取联系信息 */\r\n    getContactInfo() {\r\n      this.loading = true;\r\n      getContactInfo().then(response => {\r\n        this.contactInfo = response.data || {};\r\n        this.loading = false;\r\n      }).catch(() => {\r\n        this.contactInfo = {};\r\n        this.loading = false;\r\n      });\r\n    },\r\n    /** 取消按钮 */\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    /** 表单重置 */\r\n    reset() {\r\n      this.form = {\r\n        consultationId: null,\r\n        contactName: null,\r\n        contactMethod: null,\r\n        status: \"0\",\r\n        remark: null\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 编辑按钮操作 */\r\n    handleEdit() {\r\n      this.reset();\r\n      this.form = {\r\n        consultationId: this.contactInfo.consultationId || null,\r\n        contactName: this.contactInfo.contactName || '',\r\n        contactMethod: this.contactInfo.contactMethod || '',\r\n        status: this.contactInfo.status || \"0\",\r\n        remark: this.contactInfo.remark || ''\r\n      };\r\n      this.open = true;\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs.form.validate(valid => {\r\n        if (valid) {\r\n          updateConsultation(this.form).then(response => {\r\n            this.$modal.msgSuccess(\"保存成功\");\r\n            this.open = false;\r\n            this.getContactInfo();\r\n          });\r\n        }\r\n      });\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.contact-info-container {\r\n  max-width: 600px;\r\n  margin: 20px auto;\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  overflow: hidden;\r\n}\r\n\r\n.contact-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 20px 24px;\r\n  background: #f8f9fa;\r\n  border-bottom: 1px solid #e9ecef;\r\n}\r\n\r\n.contact-header h3 {\r\n  margin: 0;\r\n  color: #303133;\r\n  font-size: 18px;\r\n  font-weight: 500;\r\n}\r\n\r\n.contact-content {\r\n  padding: 24px;\r\n  min-height: 120px;\r\n}\r\n\r\n.contact-item {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  margin-bottom: 16px;\r\n  font-size: 14px;\r\n}\r\n\r\n.contact-item:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.contact-item label {\r\n  min-width: 80px;\r\n  color: #606266;\r\n  font-weight: 500;\r\n  margin-right: 12px;\r\n}\r\n\r\n.contact-item span {\r\n  color: #303133;\r\n  flex: 1;\r\n  word-break: break-all;\r\n}\r\n</style>"], "mappings": ";;;;;;AAmDA,IAAAA,aAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;MACA;MACAC,IAAA;MACA;MACAC,KAAA;QACAC,WAAA,GACA;UAAAC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAC,aAAA,GACA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAE,OAAA,WAAAA,QAAA;IACA,KAAAC,cAAA;EACA;EACAC,OAAA;IACA,aACAD,cAAA,WAAAA,eAAA;MAAA,IAAAE,KAAA;MACA,KAAAb,OAAA;MACA,IAAAW,4BAAA,IAAAG,IAAA,WAAAC,QAAA;QACAF,KAAA,CAAAX,WAAA,GAAAa,QAAA,CAAAhB,IAAA;QACAc,KAAA,CAAAb,OAAA;MACA,GAAAgB,KAAA;QACAH,KAAA,CAAAX,WAAA;QACAW,KAAA,CAAAb,OAAA;MACA;IACA;IACA,WACAiB,MAAA,WAAAA,OAAA;MACA,KAAAhB,IAAA;MACA,KAAAiB,KAAA;IACA;IACA,WACAA,KAAA,WAAAA,MAAA;MACA,KAAAf,IAAA;QACAgB,cAAA;QACAd,WAAA;QACAI,aAAA;QACAW,MAAA;QACAC,MAAA;MACA;MACA,KAAAC,SAAA;IACA;IACA,aACAC,UAAA,WAAAA,WAAA;MACA,KAAAL,KAAA;MACA,KAAAf,IAAA;QACAgB,cAAA,OAAAjB,WAAA,CAAAiB,cAAA;QACAd,WAAA,OAAAH,WAAA,CAAAG,WAAA;QACAI,aAAA,OAAAP,WAAA,CAAAO,aAAA;QACAW,MAAA,OAAAlB,WAAA,CAAAkB,MAAA;QACAC,MAAA,OAAAnB,WAAA,CAAAmB,MAAA;MACA;MACA,KAAApB,IAAA;IACA;IACA,WACAuB,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,CAAAvB,IAAA,CAAAwB,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAC,gCAAA,EAAAJ,MAAA,CAAAtB,IAAA,EAAAW,IAAA,WAAAC,QAAA;YACAU,MAAA,CAAAK,MAAA,CAAAC,UAAA;YACAN,MAAA,CAAAxB,IAAA;YACAwB,MAAA,CAAAd,cAAA;UACA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}