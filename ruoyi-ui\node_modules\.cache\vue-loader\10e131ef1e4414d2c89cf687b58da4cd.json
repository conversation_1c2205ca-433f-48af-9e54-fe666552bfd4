{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\xiqing\\registration-manage\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\xiqing\\registration-manage\\index.vue", "mtime": 1753771710724}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0UmVnaXN0cmF0aW9uTWFuYWdlLCBnZXRSZWdpc3RyYXRpb25NYW5hZ2UsIGRlbFJlZ2lzdHJhdGlvbk1hbmFnZSwgZXhwb3J0UmVnaXN0cmF0aW9uTWFuYWdlLCBhdWRpdFJlZ2lzdHJhdGlvbk1hbmFnZSB9IGZyb20gIkAvYXBpL21pbmlhcHAveGlxaW5nL3JlZ2lzdHJhdGlvbi1tYW5hZ2UiOw0KaW1wb3J0IHsgZ2V0QWN0aXZpdHlDb25maWcgfSBmcm9tICJAL2FwaS9taW5pYXBwL3hpcWluZy9hY3Rpdml0eS1jb25maWciOw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJYaXFpbmdSZWdpc3RyYXRpb25NYW5hZ2UiLA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICAvLyDpga7nvanlsYINCiAgICAgIGxvYWRpbmc6IHRydWUsDQogICAgICAvLyDpgInkuK3mlbDnu4QNCiAgICAgIGlkczogW10sDQogICAgICAvLyDpnZ7ljZXkuKrnpoHnlKgNCiAgICAgIHNpbmdsZTogdHJ1ZSwNCiAgICAgIC8vIOmdnuWkmuS4quemgeeUqA0KICAgICAgbXVsdGlwbGU6IHRydWUsDQogICAgICAvLyDmmL7npLrmkJzntKLmnaHku7YNCiAgICAgIHNob3dTZWFyY2g6IHRydWUsDQogICAgICAvLyDmgLvmnaHmlbANCiAgICAgIHRvdGFsOiAwLA0KICAgICAgLy8g6KW/6Z2S6YeR56eN5a2Q6Lev5ryU5oql5ZCN566h55CG6KGo5qC85pWw5o2uDQogICAgICByZWdpc3RyYXRpb25NYW5hZ2VMaXN0OiBbXSwNCiAgICAgIC8vIOaYr+WQpuaYvuekuuafpeeci+W8ueWHuuWxgg0KICAgICAgdmlld09wZW46IGZhbHNlLA0KICAgICAgLy8g5piv5ZCm5pi+56S65a6h5qC45by55Ye65bGCDQogICAgICBhdWRpdE9wZW46IGZhbHNlLA0KICAgICAgLy8g6KGo5Y2V5pWw5o2u5YiX6KGoDQogICAgICBmb3JtRGF0YUxpc3Q6IFtdLA0KICAgICAgLy8g5p+l6K+i5Y+C5pWwDQogICAgICBxdWVyeVBhcmFtczogew0KICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICBwYWdlU2l6ZTogMTAsDQogICAgICAgIGFjdGl2aXR5SWQ6IG51bGwsDQogICAgICAgIHVzZXJJZDogbnVsbCwNCiAgICAgICAgc3RhdHVzOiBudWxsDQogICAgICB9LA0KICAgICAgLy8g6KGo5Y2V5Y+C5pWwDQogICAgICBmb3JtOiB7fSwNCiAgICAgIC8vIOWuoeaguOihqOWNleWPguaVsA0KICAgICAgYXVkaXRGb3JtOiB7fQ0KICAgIH07DQogIH0sDQogIGNyZWF0ZWQoKSB7DQogICAgdGhpcy5nZXRMaXN0KCk7DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICAvKiog5p+l6K+i6KW/6Z2S6YeR56eN5a2Q6Lev5ryU5oql5ZCN566h55CG5YiX6KGoICovDQogICAgZ2V0TGlzdCgpIHsNCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7DQogICAgICBsaXN0UmVnaXN0cmF0aW9uTWFuYWdlKHRoaXMucXVlcnlQYXJhbXMpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLnJlZ2lzdHJhdGlvbk1hbmFnZUxpc3QgPSByZXNwb25zZS5yb3dzOw0KICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWw7DQogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5pCc57Si5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlUXVlcnkoKSB7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOw0KICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgfSwNCiAgICAvKiog6YeN572u5oyJ6ZKu5pON5L2cICovDQogICAgcmVzZXRRdWVyeSgpIHsNCiAgICAgIHRoaXMucmVzZXRGb3JtKCJxdWVyeUZvcm0iKTsNCiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsNCiAgICB9LA0KICAgIC8vIOWkmumAieahhumAieS4reaVsOaNrg0KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsNCiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcChpdGVtID0+IGl0ZW0ucmVnaXN0cmF0aW9uSWQpDQogICAgICB0aGlzLm11bHRpcGxlID0gIXNlbGVjdGlvbi5sZW5ndGgNCiAgICB9LA0KICAgIC8qKiDmn6XnnIvmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVWaWV3KHJvdykgew0KICAgICAgY29uc3QgcmVnaXN0cmF0aW9uSWQgPSByb3cucmVnaXN0cmF0aW9uSWQ7DQogICAgICBnZXRSZWdpc3RyYXRpb25NYW5hZ2UocmVnaXN0cmF0aW9uSWQpLnRoZW4oYXN5bmMgcmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLmZvcm0gPSByZXNwb25zZS5kYXRhOw0KICAgICAgICBhd2FpdCB0aGlzLnBhcnNlRm9ybURhdGEoKTsNCiAgICAgICAgdGhpcy52aWV3T3BlbiA9IHRydWU7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDop6PmnpDooajljZXmlbDmja4gKi8NCiAgICBhc3luYyBwYXJzZUZvcm1EYXRhKCkgew0KICAgICAgdGhpcy5mb3JtRGF0YUxpc3QgPSBbXTsNCiAgICAgIGlmICh0aGlzLmZvcm0uZm9ybURhdGEpIHsNCiAgICAgICAgdHJ5IHsNCiAgICAgICAgICBjb25zdCBkYXRhID0gSlNPTi5wYXJzZSh0aGlzLmZvcm0uZm9ybURhdGEpOw0KDQogICAgICAgICAgLy8g5qOA5p+l5pWw5o2u5qC85byPDQogICAgICAgICAgaWYgKEFycmF5LmlzQXJyYXkoZGF0YSkpIHsNCiAgICAgICAgICAgIC8vIOaWsOagvOW8j++8muaVsOe7hOagvOW8j++8jOavj+S4quWFg+e0oOWMheWQq25hbWXjgIF0eXBl44CBbGFiZWzjgIF2YWx1ZeetieWxnuaApw0KICAgICAgICAgICAgZGF0YS5mb3JFYWNoKGZpZWxkID0+IHsNCiAgICAgICAgICAgICAgaWYgKGZpZWxkLm5hbWUgJiYgZmllbGQudmFsdWUgIT09IHVuZGVmaW5lZCAmJiBmaWVsZC52YWx1ZSAhPT0gbnVsbCAmJiBmaWVsZC52YWx1ZSAhPT0gJycpIHsNCiAgICAgICAgICAgICAgICB0aGlzLmZvcm1EYXRhTGlzdC5wdXNoKHsNCiAgICAgICAgICAgICAgICAgIGtleTogZmllbGQubGFiZWwgfHwgZmllbGQubmFtZSwgLy8g5LyY5YWI5L2/55SobGFiZWzvvIzmsqHmnInliJnkvb/nlKhuYW1lDQogICAgICAgICAgICAgICAgICB2YWx1ZTogdGhpcy5mb3JtYXRGaWVsZFZhbHVlKGZpZWxkLnZhbHVlLCBmaWVsZC50eXBlKSwNCiAgICAgICAgICAgICAgICAgIHR5cGU6IGZpZWxkLnR5cGUNCiAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfSBlbHNlIGlmICh0eXBlb2YgZGF0YSA9PT0gJ29iamVjdCcpIHsNCiAgICAgICAgICAgIC8vIOaXp+agvOW8j++8muWvueixoeagvOW8j++8jGtleS12YWx1ZeW9ouW8jw0KICAgICAgICAgICAgLy8g6I635Y+W5rS75Yqo55qE6KGo5Y2V6YWN572u5p2l5pi+56S65q2j56Gu55qE5a2X5q615qCH562+DQogICAgICAgICAgICBjb25zdCBmb3JtQ29uZmlnID0gYXdhaXQgdGhpcy5nZXRBY3Rpdml0eUZvcm1Db25maWcoKTsNCiAgICAgICAgICAgIGNvbnN0IGZpZWxkTGFiZWxNYXAgPSB7fTsNCiAgICAgICAgICAgIGlmIChmb3JtQ29uZmlnKSB7DQogICAgICAgICAgICAgIGZvcm1Db25maWcuZm9yRWFjaChmaWVsZCA9PiB7DQogICAgICAgICAgICAgICAgZmllbGRMYWJlbE1hcFtmaWVsZC5uYW1lXSA9IGZpZWxkLmxhYmVsOw0KICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgZm9yIChjb25zdCBrZXkgaW4gZGF0YSkgew0KICAgICAgICAgICAgICBpZiAoZGF0YVtrZXldICE9PSB1bmRlZmluZWQgJiYgZGF0YVtrZXldICE9PSBudWxsICYmIGRhdGFba2V5XSAhPT0gJycpIHsNCiAgICAgICAgICAgICAgICB0aGlzLmZvcm1EYXRhTGlzdC5wdXNoKHsNCiAgICAgICAgICAgICAgICAgIGtleTogZmllbGRMYWJlbE1hcFtrZXldIHx8IGtleSwgLy8g5LyY5YWI5L2/55So5Lit5paH5qCH562+77yM5rKh5pyJ5YiZ5L2/55So5Y6f5a2X5q615ZCNDQogICAgICAgICAgICAgICAgICB2YWx1ZTogZGF0YVtrZXldLA0KICAgICAgICAgICAgICAgICAgdHlwZTogJ3RleHQnDQogICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0gY2F0Y2ggKGUpIHsNCiAgICAgICAgICBjb25zb2xlLmVycm9yKCfop6PmnpDooajljZXmlbDmja7lpLHotKU6JywgZSk7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9LA0KICAgIC8qKiDmoLzlvI/ljJblrZfmrrXlgLwgKi8NCiAgICBmb3JtYXRGaWVsZFZhbHVlKHZhbHVlLCB0eXBlKSB7DQogICAgICBpZiAodmFsdWUgPT09IHVuZGVmaW5lZCB8fCB2YWx1ZSA9PT0gbnVsbCB8fCB2YWx1ZSA9PT0gJycpIHsNCiAgICAgICAgcmV0dXJuICfmnKrloavlhpknOw0KICAgICAgfQ0KDQogICAgICBzd2l0Y2ggKHR5cGUpIHsNCiAgICAgICAgY2FzZSAnY2hlY2tib3gnOg0KICAgICAgICAgIC8vIOWkjemAieahhuexu+Wei++8jHZhbHVl5Y+v6IO95piv5pWw57uEDQogICAgICAgICAgaWYgKEFycmF5LmlzQXJyYXkodmFsdWUpKSB7DQogICAgICAgICAgICByZXR1cm4gdmFsdWUubGVuZ3RoID4gMCA/IHZhbHVlLmpvaW4oJywgJykgOiAn5pyq6YCJ5oupJzsNCiAgICAgICAgICB9DQogICAgICAgICAgcmV0dXJuIHZhbHVlOw0KICAgICAgICBjYXNlICdyYWRpbyc6DQogICAgICAgIGNhc2UgJ3BpY2tlcic6DQogICAgICAgIGNhc2UgJ3NlbGVjdCc6DQogICAgICAgICAgLy8g5Y2V6YCJ57G75Z6LDQogICAgICAgICAgcmV0dXJuIHZhbHVlIHx8ICfmnKrpgInmi6knOw0KICAgICAgICBjYXNlICd0ZXh0YXJlYSc6DQogICAgICAgICAgLy8g5paH5pys5Z+f57G75Z6L77yM5L+d5oyB5o2i6KGMDQogICAgICAgICAgcmV0dXJuIHZhbHVlOw0KICAgICAgICBjYXNlICdkYXRlJzoNCiAgICAgICAgICAvLyDml6XmnJ/nsbvlnosNCiAgICAgICAgICByZXR1cm4gdmFsdWUgfHwgJ+acqumAieaLqSc7DQogICAgICAgIGNhc2UgJ3RlbCc6DQogICAgICAgIGNhc2UgJ3Bob25lJzoNCiAgICAgICAgICAvLyDnlLXor53nsbvlnosNCiAgICAgICAgICByZXR1cm4gdmFsdWU7DQogICAgICAgIGRlZmF1bHQ6DQogICAgICAgICAgLy8g6buY6K6k5paH5pys57G75Z6LDQogICAgICAgICAgcmV0dXJuIHZhbHVlOw0KICAgICAgfQ0KICAgIH0sDQogICAgLyoqIOiOt+WPlua0u+WKqOihqOWNlemFjee9riAqLw0KICAgIGFzeW5jIGdldEFjdGl2aXR5Rm9ybUNvbmZpZygpIHsNCiAgICAgIGlmICghdGhpcy5mb3JtLmFjdGl2aXR5SWQpIHsNCiAgICAgICAgcmV0dXJuIG51bGw7DQogICAgICB9DQogICAgICB0cnkgew0KICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGdldEFjdGl2aXR5Q29uZmlnKHRoaXMuZm9ybS5hY3Rpdml0eUlkKTsNCiAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEgJiYgcmVzcG9uc2UuZGF0YS5mb3JtQ29uZmlnKSB7DQogICAgICAgICAgcmV0dXJuIEpTT04ucGFyc2UocmVzcG9uc2UuZGF0YS5mb3JtQ29uZmlnKTsNCiAgICAgICAgfQ0KICAgICAgfSBjYXRjaCAoZSkgew0KICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5bmtLvliqjooajljZXphY3nva7lpLHotKU6JywgZSk7DQogICAgICB9DQogICAgICByZXR1cm4gbnVsbDsNCiAgICB9LA0KICAgIC8qKiDlrqHmoLjmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVBdWRpdChyb3cpIHsNCiAgICAgIHRoaXMuYXVkaXRGb3JtID0gew0KICAgICAgICByZWdpc3RyYXRpb25JZDogcm93LnJlZ2lzdHJhdGlvbklkLA0KICAgICAgICBzdGF0dXM6ICcxJywNCiAgICAgICAgYXVkaXRSZW1hcms6ICcnDQogICAgICB9Ow0KICAgICAgdGhpcy5hdWRpdE9wZW4gPSB0cnVlOw0KICAgIH0sDQogICAgLyoqIOaPkOS6pOWuoeaguCAqLw0KICAgIHN1Ym1pdEF1ZGl0KCkgew0KICAgICAgYXVkaXRSZWdpc3RyYXRpb25NYW5hZ2UodGhpcy5hdWRpdEZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLlrqHmoLjmiJDlip8iKTsNCiAgICAgICAgdGhpcy5hdWRpdE9wZW4gPSBmYWxzZTsNCiAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDliKDpmaTmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVEZWxldGUocm93KSB7DQogICAgICBjb25zdCByZWdpc3RyYXRpb25JZHMgPSByb3cucmVnaXN0cmF0aW9uSWQgfHwgdGhpcy5pZHM7DQogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfmmK/lkKbnoa7orqTliKDpmaTmiqXlkI3nvJblj7fkuLoiJyArIHJlZ2lzdHJhdGlvbklkcyArICci55qE5pWw5o2u6aG577yfJykudGhlbihmdW5jdGlvbigpIHsNCiAgICAgICAgcmV0dXJuIGRlbFJlZ2lzdHJhdGlvbk1hbmFnZShyZWdpc3RyYXRpb25JZHMpOw0KICAgICAgfSkudGhlbigoKSA9PiB7DQogICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLliKDpmaTmiJDlip8iKTsNCiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KTsNCiAgICB9LA0KICAgIC8qKiDlr7zlh7rmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVFeHBvcnQoKSB7DQogICAgICB0aGlzLmRvd25sb2FkKCdtaW5pYXBwL3hpcWluZy9yZWdpc3RyYXRpb24tbWFuYWdlL2V4cG9ydCcsIHsNCiAgICAgICAgLi4udGhpcy5xdWVyeVBhcmFtcw0KICAgICAgfSwgYHJlZ2lzdHJhdGlvbl9tYW5hZ2VfJHtuZXcgRGF0ZSgpLmdldFRpbWUoKX0ueGxzeGApDQogICAgfQ0KICB9DQp9Ow0K"}, null]}