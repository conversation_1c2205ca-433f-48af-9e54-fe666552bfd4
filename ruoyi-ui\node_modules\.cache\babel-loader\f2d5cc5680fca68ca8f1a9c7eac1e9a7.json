{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\xiqing\\registration-manage\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\xiqing\\registration-manage\\index.vue", "mtime": 1753771710724}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\babel.config.js", "mtime": 1751427652172}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_registrationManage", "require", "_activityConfig", "name", "data", "loading", "ids", "single", "multiple", "showSearch", "total", "registrationManageList", "viewOpen", "auditOpen", "formDataList", "queryParams", "pageNum", "pageSize", "activityId", "userId", "status", "form", "auditForm", "created", "getList", "methods", "_this", "listRegistrationManage", "then", "response", "rows", "handleQuery", "reset<PERSON><PERSON>y", "resetForm", "handleSelectionChange", "selection", "map", "item", "registrationId", "length", "handleView", "row", "_this2", "getRegistrationManage", "_ref", "_asyncToGenerator2", "default", "_regenerator2", "m", "_callee", "w", "_context", "n", "parseFormData", "a", "_x", "apply", "arguments", "_this3", "_callee2", "formConfig", "fieldLabelMap", "key", "_t", "_context2", "formData", "p", "JSON", "parse", "Array", "isArray", "for<PERSON>ach", "field", "value", "undefined", "push", "label", "formatFieldValue", "type", "_typeof2", "getActivityFormConfig", "v", "console", "error", "join", "_this4", "_callee3", "_t2", "_context3", "getActivityConfig", "handleAudit", "auditRemark", "submitAudit", "_this5", "auditRegistrationManage", "$modal", "msgSuccess", "handleDelete", "_this6", "registrationIds", "confirm", "delRegistrationManage", "catch", "handleExport", "download", "_objectSpread2", "concat", "Date", "getTime"], "sources": ["src/views/miniapp/xiqing/registration-manage/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"活动ID\" prop=\"activityId\">\r\n        <el-input\r\n          v-model=\"queryParams.activityId\"\r\n          placeholder=\"请输入活动ID\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"用户ID\" prop=\"userId\">\r\n        <el-input\r\n          v-model=\"queryParams.userId\"\r\n          placeholder=\"请输入用户ID\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"审核状态\" prop=\"status\">\r\n        <el-select v-model=\"queryParams.status\" placeholder=\"请选择审核状态\" clearable>\r\n          <el-option label=\"待审核\" value=\"0\" />\r\n          <el-option label=\"通过\" value=\"1\" />\r\n          <el-option label=\"拒绝\" value=\"2\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['miniapp:xiqing:registration:remove']\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['miniapp:xiqing:registration:export']\"\r\n        >导出</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"registrationManageList\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"报名ID\" align=\"center\" prop=\"registrationId\" width=\"80\" />\r\n      <el-table-column label=\"活动标题\" align=\"center\" prop=\"activityTitle\" :show-overflow-tooltip=\"true\" />\r\n      <el-table-column label=\"用户ID\" align=\"center\" prop=\"userId\" width=\"80\" />\r\n      <el-table-column label=\"报名时间\" align=\"center\" prop=\"registrationTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.registrationTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"审核状态\" align=\"center\" prop=\"status\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag v-if=\"scope.row.status === '0'\" type=\"warning\">待审核</el-tag>\r\n          <el-tag v-else-if=\"scope.row.status === '1'\" type=\"success\">通过</el-tag>\r\n          <el-tag v-else-if=\"scope.row.status === '2'\" type=\"danger\">拒绝</el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"审核人\" align=\"center\" prop=\"auditBy\" width=\"100\" />\r\n      <el-table-column label=\"审核时间\" align=\"center\" prop=\"auditTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.auditTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"200\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-view\"\r\n            @click=\"handleView(scope.row)\"\r\n            v-hasPermi=\"['miniapp:xiqing:registration:query']\"\r\n          >查看</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-check\"\r\n            @click=\"handleAudit(scope.row)\"\r\n            v-hasPermi=\"['miniapp:xiqing:registration:audit']\"\r\n            v-if=\"scope.row.status === '0'\"\r\n          >审核</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n            v-hasPermi=\"['miniapp:xiqing:registration:remove']\"\r\n          >删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    \r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 查看报名详情对话框 -->\r\n    <el-dialog title=\"报名详情\" :visible.sync=\"viewOpen\" width=\"800px\" append-to-body>\r\n      <el-descriptions :column=\"2\" border>\r\n        <el-descriptions-item label=\"报名ID\">{{ form.registrationId }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"活动标题\">{{ form.activityTitle }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"用户ID\">{{ form.userId }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"报名时间\">{{ parseTime(form.registrationTime) }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"审核状态\">\r\n          <el-tag v-if=\"form.status === '0'\" type=\"warning\">待审核</el-tag>\r\n          <el-tag v-else-if=\"form.status === '1'\" type=\"success\">通过</el-tag>\r\n          <el-tag v-else-if=\"form.status === '2'\" type=\"danger\">拒绝</el-tag>\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"审核人\">{{ form.auditBy }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"审核时间\">{{ parseTime(form.auditTime) }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"审核备注\" :span=\"2\">{{ form.auditRemark }}</el-descriptions-item>\r\n      </el-descriptions>\r\n      \r\n      <div style=\"margin-top: 20px;\">\r\n        <h4>报名表单数据：</h4>\r\n        <el-table :data=\"formDataList\" border style=\"margin-top: 10px;\">\r\n          <el-table-column prop=\"key\" label=\"字段名\" width=\"200\" />\r\n          <el-table-column label=\"字段值\">\r\n            <template slot-scope=\"scope\">\r\n              <div v-if=\"scope.row.type === 'textarea'\" class=\"textarea-content\">\r\n                {{ scope.row.value }}\r\n              </div>\r\n              <el-tag v-else-if=\"scope.row.type === 'radio' || scope.row.type === 'picker' || scope.row.type === 'select'\"\r\n                      type=\"primary\" size=\"small\">\r\n                {{ scope.row.value }}\r\n              </el-tag>\r\n              <span v-else-if=\"scope.row.type === 'tel' || scope.row.type === 'phone'\"\r\n                    class=\"phone-number\">\r\n                {{ scope.row.value }}\r\n              </span>\r\n              <el-tag v-else-if=\"scope.row.type === 'date'\" type=\"info\" size=\"small\">\r\n                {{ scope.row.value }}\r\n              </el-tag>\r\n              <div v-else-if=\"scope.row.type === 'checkbox'\" class=\"checkbox-content\">\r\n                {{ scope.row.value }}\r\n              </div>\r\n              <span v-else>{{ scope.row.value }}</span>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </div>\r\n      \r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"viewOpen = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 审核对话框 -->\r\n    <el-dialog title=\"审核报名\" :visible.sync=\"auditOpen\" width=\"500px\" append-to-body>\r\n      <el-form ref=\"auditForm\" :model=\"auditForm\" label-width=\"100px\">\r\n        <el-form-item label=\"审核结果\" prop=\"status\">\r\n          <el-radio-group v-model=\"auditForm.status\">\r\n            <el-radio label=\"1\">通过</el-radio>\r\n            <el-radio label=\"2\">拒绝</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <el-form-item label=\"审核备注\" prop=\"auditRemark\">\r\n          <el-input v-model=\"auditForm.auditRemark\" type=\"textarea\" placeholder=\"请输入审核备注\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitAudit\">确 定</el-button>\r\n        <el-button @click=\"auditOpen = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listRegistrationManage, getRegistrationManage, delRegistrationManage, exportRegistrationManage, auditRegistrationManage } from \"@/api/miniapp/xiqing/registration-manage\";\r\nimport { getActivityConfig } from \"@/api/miniapp/xiqing/activity-config\";\r\n\r\nexport default {\r\n  name: \"XiqingRegistrationManage\",\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 西青金种子路演报名管理表格数据\r\n      registrationManageList: [],\r\n      // 是否显示查看弹出层\r\n      viewOpen: false,\r\n      // 是否显示审核弹出层\r\n      auditOpen: false,\r\n      // 表单数据列表\r\n      formDataList: [],\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        activityId: null,\r\n        userId: null,\r\n        status: null\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 审核表单参数\r\n      auditForm: {}\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询西青金种子路演报名管理列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listRegistrationManage(this.queryParams).then(response => {\r\n        this.registrationManageList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.registrationId)\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 查看按钮操作 */\r\n    handleView(row) {\r\n      const registrationId = row.registrationId;\r\n      getRegistrationManage(registrationId).then(async response => {\r\n        this.form = response.data;\r\n        await this.parseFormData();\r\n        this.viewOpen = true;\r\n      });\r\n    },\r\n    /** 解析表单数据 */\r\n    async parseFormData() {\r\n      this.formDataList = [];\r\n      if (this.form.formData) {\r\n        try {\r\n          const data = JSON.parse(this.form.formData);\r\n\r\n          // 检查数据格式\r\n          if (Array.isArray(data)) {\r\n            // 新格式：数组格式，每个元素包含name、type、label、value等属性\r\n            data.forEach(field => {\r\n              if (field.name && field.value !== undefined && field.value !== null && field.value !== '') {\r\n                this.formDataList.push({\r\n                  key: field.label || field.name, // 优先使用label，没有则使用name\r\n                  value: this.formatFieldValue(field.value, field.type),\r\n                  type: field.type\r\n                });\r\n              }\r\n            });\r\n          } else if (typeof data === 'object') {\r\n            // 旧格式：对象格式，key-value形式\r\n            // 获取活动的表单配置来显示正确的字段标签\r\n            const formConfig = await this.getActivityFormConfig();\r\n            const fieldLabelMap = {};\r\n            if (formConfig) {\r\n              formConfig.forEach(field => {\r\n                fieldLabelMap[field.name] = field.label;\r\n              });\r\n            }\r\n\r\n            for (const key in data) {\r\n              if (data[key] !== undefined && data[key] !== null && data[key] !== '') {\r\n                this.formDataList.push({\r\n                  key: fieldLabelMap[key] || key, // 优先使用中文标签，没有则使用原字段名\r\n                  value: data[key],\r\n                  type: 'text'\r\n                });\r\n              }\r\n            }\r\n          }\r\n        } catch (e) {\r\n          console.error('解析表单数据失败:', e);\r\n        }\r\n      }\r\n    },\r\n    /** 格式化字段值 */\r\n    formatFieldValue(value, type) {\r\n      if (value === undefined || value === null || value === '') {\r\n        return '未填写';\r\n      }\r\n\r\n      switch (type) {\r\n        case 'checkbox':\r\n          // 复选框类型，value可能是数组\r\n          if (Array.isArray(value)) {\r\n            return value.length > 0 ? value.join(', ') : '未选择';\r\n          }\r\n          return value;\r\n        case 'radio':\r\n        case 'picker':\r\n        case 'select':\r\n          // 单选类型\r\n          return value || '未选择';\r\n        case 'textarea':\r\n          // 文本域类型，保持换行\r\n          return value;\r\n        case 'date':\r\n          // 日期类型\r\n          return value || '未选择';\r\n        case 'tel':\r\n        case 'phone':\r\n          // 电话类型\r\n          return value;\r\n        default:\r\n          // 默认文本类型\r\n          return value;\r\n      }\r\n    },\r\n    /** 获取活动表单配置 */\r\n    async getActivityFormConfig() {\r\n      if (!this.form.activityId) {\r\n        return null;\r\n      }\r\n      try {\r\n        const response = await getActivityConfig(this.form.activityId);\r\n        if (response.data && response.data.formConfig) {\r\n          return JSON.parse(response.data.formConfig);\r\n        }\r\n      } catch (e) {\r\n        console.error('获取活动表单配置失败:', e);\r\n      }\r\n      return null;\r\n    },\r\n    /** 审核按钮操作 */\r\n    handleAudit(row) {\r\n      this.auditForm = {\r\n        registrationId: row.registrationId,\r\n        status: '1',\r\n        auditRemark: ''\r\n      };\r\n      this.auditOpen = true;\r\n    },\r\n    /** 提交审核 */\r\n    submitAudit() {\r\n      auditRegistrationManage(this.auditForm).then(response => {\r\n        this.$modal.msgSuccess(\"审核成功\");\r\n        this.auditOpen = false;\r\n        this.getList();\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const registrationIds = row.registrationId || this.ids;\r\n      this.$modal.confirm('是否确认删除报名编号为\"' + registrationIds + '\"的数据项？').then(function() {\r\n        return delRegistrationManage(registrationIds);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('miniapp/xiqing/registration-manage/export', {\r\n        ...this.queryParams\r\n      }, `registration_manage_${new Date().getTime()}.xlsx`)\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.textarea-content {\r\n  white-space: pre-wrap;\r\n  word-break: break-word;\r\n  line-height: 1.5;\r\n  max-height: 120px;\r\n  overflow-y: auto;\r\n  padding: 8px;\r\n  background-color: #f5f7fa;\r\n  border-radius: 4px;\r\n  border: 1px solid #e4e7ed;\r\n}\r\n\r\n.phone-number {\r\n  color: #409eff;\r\n  font-weight: 500;\r\n}\r\n\r\n.checkbox-content {\r\n  color: #606266;\r\n  line-height: 1.5;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AA6LA,IAAAA,mBAAA,GAAAC,OAAA;AACA,IAAAC,eAAA,GAAAD,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAE,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,sBAAA;MACA;MACAC,QAAA;MACA;MACAC,SAAA;MACA;MACAC,YAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,UAAA;QACAC,MAAA;QACAC,MAAA;MACA;MACA;MACAC,IAAA;MACA;MACAC,SAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA,sBACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAArB,OAAA;MACA,IAAAsB,0CAAA,OAAAZ,WAAA,EAAAa,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAAf,sBAAA,GAAAkB,QAAA,CAAAC,IAAA;QACAJ,KAAA,CAAAhB,KAAA,GAAAmB,QAAA,CAAAnB,KAAA;QACAgB,KAAA,CAAArB,OAAA;MACA;IACA;IACA,aACA0B,WAAA,WAAAA,YAAA;MACA,KAAAhB,WAAA,CAAAC,OAAA;MACA,KAAAQ,OAAA;IACA;IACA,aACAQ,UAAA,WAAAA,WAAA;MACA,KAAAC,SAAA;MACA,KAAAF,WAAA;IACA;IACA;IACAG,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAA7B,GAAA,GAAA6B,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,cAAA;MAAA;MACA,KAAA9B,QAAA,IAAA2B,SAAA,CAAAI,MAAA;IACA;IACA,aACAC,UAAA,WAAAA,WAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,IAAAJ,cAAA,GAAAG,GAAA,CAAAH,cAAA;MACA,IAAAK,yCAAA,EAAAL,cAAA,EAAAV,IAAA;QAAA,IAAAgB,IAAA,OAAAC,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAC,QAAApB,QAAA;UAAA,WAAAkB,aAAA,CAAAD,OAAA,IAAAI,CAAA,WAAAC,QAAA;YAAA,kBAAAA,QAAA,CAAAC,CAAA;cAAA;gBACAV,MAAA,CAAArB,IAAA,GAAAQ,QAAA,CAAAzB,IAAA;gBAAA+C,QAAA,CAAAC,CAAA;gBAAA,OACAV,MAAA,CAAAW,aAAA;cAAA;gBACAX,MAAA,CAAA9B,QAAA;cAAA;gBAAA,OAAAuC,QAAA,CAAAG,CAAA;YAAA;UAAA,GAAAL,OAAA;QAAA,CACA;QAAA,iBAAAM,EAAA;UAAA,OAAAX,IAAA,CAAAY,KAAA,OAAAC,SAAA;QAAA;MAAA;IACA;IACA,aACAJ,aAAA,WAAAA,cAAA;MAAA,IAAAK,MAAA;MAAA,WAAAb,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAW,SAAA;QAAA,IAAAvD,IAAA,EAAAwD,UAAA,EAAAC,aAAA,EAAAC,GAAA,EAAAC,EAAA;QAAA,WAAAhB,aAAA,CAAAD,OAAA,IAAAI,CAAA,WAAAc,SAAA;UAAA,kBAAAA,SAAA,CAAAZ,CAAA;YAAA;cACAM,MAAA,CAAA5C,YAAA;cAAA,KACA4C,MAAA,CAAArC,IAAA,CAAA4C,QAAA;gBAAAD,SAAA,CAAAZ,CAAA;gBAAA;cAAA;cAAAY,SAAA,CAAAE,CAAA;cAEA9D,IAAA,GAAA+D,IAAA,CAAAC,KAAA,CAAAV,MAAA,CAAArC,IAAA,CAAA4C,QAAA,GAEA;cAAA,KACAI,KAAA,CAAAC,OAAA,CAAAlE,IAAA;gBAAA4D,SAAA,CAAAZ,CAAA;gBAAA;cAAA;cACA;cACAhD,IAAA,CAAAmE,OAAA,WAAAC,KAAA;gBACA,IAAAA,KAAA,CAAArE,IAAA,IAAAqE,KAAA,CAAAC,KAAA,KAAAC,SAAA,IAAAF,KAAA,CAAAC,KAAA,aAAAD,KAAA,CAAAC,KAAA;kBACAf,MAAA,CAAA5C,YAAA,CAAA6D,IAAA;oBACAb,GAAA,EAAAU,KAAA,CAAAI,KAAA,IAAAJ,KAAA,CAAArE,IAAA;oBAAA;oBACAsE,KAAA,EAAAf,MAAA,CAAAmB,gBAAA,CAAAL,KAAA,CAAAC,KAAA,EAAAD,KAAA,CAAAM,IAAA;oBACAA,IAAA,EAAAN,KAAA,CAAAM;kBACA;gBACA;cACA;cAAAd,SAAA,CAAAZ,CAAA;cAAA;YAAA;cAAA,MACA,IAAA2B,QAAA,CAAAjC,OAAA,EAAA1C,IAAA;gBAAA4D,SAAA,CAAAZ,CAAA;gBAAA;cAAA;cAAAY,SAAA,CAAAZ,CAAA;cAAA,OAGAM,MAAA,CAAAsB,qBAAA;YAAA;cAAApB,UAAA,GAAAI,SAAA,CAAAiB,CAAA;cACApB,aAAA;cACA,IAAAD,UAAA;gBACAA,UAAA,CAAAW,OAAA,WAAAC,KAAA;kBACAX,aAAA,CAAAW,KAAA,CAAArE,IAAA,IAAAqE,KAAA,CAAAI,KAAA;gBACA;cACA;cAEA,KAAAd,GAAA,IAAA1D,IAAA;gBACA,IAAAA,IAAA,CAAA0D,GAAA,MAAAY,SAAA,IAAAtE,IAAA,CAAA0D,GAAA,cAAA1D,IAAA,CAAA0D,GAAA;kBACAJ,MAAA,CAAA5C,YAAA,CAAA6D,IAAA;oBACAb,GAAA,EAAAD,aAAA,CAAAC,GAAA,KAAAA,GAAA;oBAAA;oBACAW,KAAA,EAAArE,IAAA,CAAA0D,GAAA;oBACAgB,IAAA;kBACA;gBACA;cACA;YAAA;cAAAd,SAAA,CAAAZ,CAAA;cAAA;YAAA;cAAAY,SAAA,CAAAE,CAAA;cAAAH,EAAA,GAAAC,SAAA,CAAAiB,CAAA;cAGAC,OAAA,CAAAC,KAAA,cAAApB,EAAA;YAAA;cAAA,OAAAC,SAAA,CAAAV,CAAA;UAAA;QAAA,GAAAK,QAAA;MAAA;IAGA;IACA,aACAkB,gBAAA,WAAAA,iBAAAJ,KAAA,EAAAK,IAAA;MACA,IAAAL,KAAA,KAAAC,SAAA,IAAAD,KAAA,aAAAA,KAAA;QACA;MACA;MAEA,QAAAK,IAAA;QACA;UACA;UACA,IAAAT,KAAA,CAAAC,OAAA,CAAAG,KAAA;YACA,OAAAA,KAAA,CAAAlC,MAAA,OAAAkC,KAAA,CAAAW,IAAA;UACA;UACA,OAAAX,KAAA;QACA;QACA;QACA;UACA;UACA,OAAAA,KAAA;QACA;UACA;UACA,OAAAA,KAAA;QACA;UACA;UACA,OAAAA,KAAA;QACA;QACA;UACA;UACA,OAAAA,KAAA;QACA;UACA;UACA,OAAAA,KAAA;MACA;IACA;IACA,eACAO,qBAAA,WAAAA,sBAAA;MAAA,IAAAK,MAAA;MAAA,WAAAxC,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAsC,SAAA;QAAA,IAAAzD,QAAA,EAAA0D,GAAA;QAAA,WAAAxC,aAAA,CAAAD,OAAA,IAAAI,CAAA,WAAAsC,SAAA;UAAA,kBAAAA,SAAA,CAAApC,CAAA;YAAA;cAAA,IACAiC,MAAA,CAAAhE,IAAA,CAAAH,UAAA;gBAAAsE,SAAA,CAAApC,CAAA;gBAAA;cAAA;cAAA,OAAAoC,SAAA,CAAAlC,CAAA,IACA;YAAA;cAAAkC,SAAA,CAAAtB,CAAA;cAAAsB,SAAA,CAAApC,CAAA;cAAA,OAGA,IAAAqC,iCAAA,EAAAJ,MAAA,CAAAhE,IAAA,CAAAH,UAAA;YAAA;cAAAW,QAAA,GAAA2D,SAAA,CAAAP,CAAA;cAAA,MACApD,QAAA,CAAAzB,IAAA,IAAAyB,QAAA,CAAAzB,IAAA,CAAAwD,UAAA;gBAAA4B,SAAA,CAAApC,CAAA;gBAAA;cAAA;cAAA,OAAAoC,SAAA,CAAAlC,CAAA,IACAa,IAAA,CAAAC,KAAA,CAAAvC,QAAA,CAAAzB,IAAA,CAAAwD,UAAA;YAAA;cAAA4B,SAAA,CAAApC,CAAA;cAAA;YAAA;cAAAoC,SAAA,CAAAtB,CAAA;cAAAqB,GAAA,GAAAC,SAAA,CAAAP,CAAA;cAGAC,OAAA,CAAAC,KAAA,gBAAAI,GAAA;YAAA;cAAA,OAAAC,SAAA,CAAAlC,CAAA,IAEA;UAAA;QAAA,GAAAgC,QAAA;MAAA;IACA;IACA,aACAI,WAAA,WAAAA,YAAAjD,GAAA;MACA,KAAAnB,SAAA;QACAgB,cAAA,EAAAG,GAAA,CAAAH,cAAA;QACAlB,MAAA;QACAuE,WAAA;MACA;MACA,KAAA9E,SAAA;IACA;IACA,WACA+E,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,2CAAA,OAAAxE,SAAA,EAAAM,IAAA,WAAAC,QAAA;QACAgE,MAAA,CAAAE,MAAA,CAAAC,UAAA;QACAH,MAAA,CAAAhF,SAAA;QACAgF,MAAA,CAAArE,OAAA;MACA;IACA;IACA,aACAyE,YAAA,WAAAA,aAAAxD,GAAA;MAAA,IAAAyD,MAAA;MACA,IAAAC,eAAA,GAAA1D,GAAA,CAAAH,cAAA,SAAAhC,GAAA;MACA,KAAAyF,MAAA,CAAAK,OAAA,kBAAAD,eAAA,aAAAvE,IAAA;QACA,WAAAyE,yCAAA,EAAAF,eAAA;MACA,GAAAvE,IAAA;QACAsE,MAAA,CAAA1E,OAAA;QACA0E,MAAA,CAAAH,MAAA,CAAAC,UAAA;MACA,GAAAM,KAAA;IACA;IACA,aACAC,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,kDAAAC,cAAA,CAAA3D,OAAA,MACA,KAAA/B,WAAA,0BAAA2F,MAAA,CACA,IAAAC,IAAA,GAAAC,OAAA;IACA;EACA;AACA", "ignoreList": []}]}